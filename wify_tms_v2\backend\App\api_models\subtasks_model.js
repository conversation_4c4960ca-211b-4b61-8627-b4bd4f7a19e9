var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('./utils/db_resp');
const pagination_filters_utils = require('./utils/pagination_filters_utils');
const users_model = require('./users_model');
// const { default: subtasks_workflow } = require("./workflows/subtasks_workflow");
const {
    checkifUnderCircularGeofence,
    checkifInsideCircularGeofence,
} = require('./utils/geo_coding_utils');
const {
    STANDARD_DEVIATION_FOR_PLACES_LOCATION,
    GPS_NETWORK_LOW_ACCURACY,
} = require('./utils/constants');
const { allQueues } = require('./queues_v2/queues');
var commonUtils = require('./utils/common');
const { callLambdaFn } = require('./utils/lambda_helpers');
const {
    getMandatoryFieldsKeyNotFilled,
    getTheLabelFrMandatoryFields,
    getTmrwDate,
    getStreamFromVariable,
    moduleKeys,
} = require('./utils/helper');
const {
    getSbtskWorkflowModel,
} = require('./queues_v2/processors/helpers/sbtsk_workflow_helper');
const JSONToCsv = require('json-to-csv-stream');
const fs = require('fs');
const path = require('path');
const JSONStream = require('JSONStream');
const {
    dumpExportReqCounter,
    dumpExportFailureCounter,
    dumpExportStatus,
    dumpExportsCounter,
    dumpExportSuccessCounter,
} = require('./utils/metrics');

class subtasks_model {
    getAutoAssgnProto(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                console.log(
                    'tms_get_auto_assgn_proto data',
                    JSON.stringify(query)
                );
                let allUsers = (
                    await this.db.tms_get_auto_assgn_proto(
                        JSON.stringify(query)
                    )
                )[0].tms_get_auto_assgn_proto;
                // console.log('allUsers',JSON.stringify(allUsers));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(allUsers.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('getAutoAssgnProto error', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Unable to find users',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
        });
    }

    getUsersFrAutoAssign(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['pagination_data'] =
                    pagination_filters_utils.decodeQueryParams(query, true);
                // console.log('tms_get_all_users_fr_gen_auto_assgn data',JSON.stringify(query))
                let allUsers = (
                    await this.db.tms_get_all_users_fr_gen_auto_assgn(
                        JSON.stringify(query)
                    )
                )[0].tms_get_all_users_fr_gen_auto_assgn;
                // console.log('allUsers',JSON.stringify(allUsers));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(allUsers.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Unable to find users',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
        });
    }

    generateLambdaBasedAssignments(query) {
        return new Promise(async (resolve, reject) => {
            // Get Users list and service requests data
            // Get Users jobs for the date
            // User details -> Home Address, Skills, Roles, Verticals,Coverage Pincodes,Primary skill, Secondary skill, Area of interest
            // Service request details, - config data, form data
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                // console.log('generateLambdaBasedAssignments form_data',form_data);
                let lambdaPayload = { ...query };

                //Get service request form_data
                this.initServiceModel();
                let queryFrReqs = {
                    ...query,
                    filters: JSON.stringify(query.reqFilter),
                    srvc_type_id: 0,
                    for_lambda: true,
                };
                // console.log('queryFrReqs',queryFrReqs);
                let srvcReqs =
                    await this.getServiceModelInstance().getSrvcReqsFrGenAutoAssign(
                        queryFrReqs
                    );
                // console.log('srvcReqs',srvcReqs.resp);
                try {
                    srvcReqs = JSON.parse(srvcReqs.resp).data;
                } catch (error) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Error decoding requests',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                lambdaPayload['srvcReqs'] = srvcReqs;
                // console.log('lambdaPayload',lambdaPayload)

                if (
                    !lambdaPayload['srvcReqs'] ||
                    lambdaPayload['srvcReqs'].length <= 0
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'No service requests found for the filter applied',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                }
                // Get all users along with their tasks for the org, along with their custom fields data
                let queryFrUsersList = {
                    ...query,
                    filters: JSON.stringify(query.assigneeFilter),
                    for_auto_assign: '1',
                    // 'dep_day': query.assigneeFilter.dep_day,
                    for_lambda: true,
                };
                // console.log('queryFrUsersList',queryFrUsersList);
                let allUsers =
                    await this.getUsersFrAutoAssign(queryFrUsersList);
                // console.log('allUsers',allUsers);

                try {
                    // console.log('allUsers',allUsers.resp);
                    allUsers = JSON.parse(allUsers.resp);
                    lambdaPayload['allUsers'] = allUsers.users_json;

                    lambdaPayload['user_fields_meta'] =
                        allUsers.user_fields_meta.translatedFields || [];
                } catch (error) {
                    console.log('allUsers decoding error', error);
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Error decoding users',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                // console.log(`allUsers count`, lambdaPayload['allUsers'].length)
                // console.log('query.dep_day',query.dep_day);
                // console.log('lambdaPayload',lambdaPayload)
                if (
                    !lambdaPayload['allUsers'] ||
                    lambdaPayload['allUsers'].length <= 0
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'No users found',
                            HttpStatus.StatusCodes.CONFLICT
                        )
                    );
                }
                lambdaPayload['dep_day'] = query.assigneeFilter.dep_day;
                //Call lambda function here
                let lambdaArn = query.sbtskData.lambda_for_auto_assign;
                const params = {
                    FunctionName: lambdaArn,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify(lambdaPayload),
                };
                let respData = await callLambdaFn(params);
                resolve(
                    new sampleOperationResp(
                        true,
                        respData.Payload,
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                // console.log('generateLambdaBasedAssignments error',error);
                this.fatalDbError(resolve, error);
            }
        });
    }

    getSubtasksBatchDataFrWrapper(batchData) {
        let returnData = {};
        if (batchData && batchData.length > 0) {
            batchData.forEach((singleTaskDetail) => {
                let { srvc_type_id } = singleTaskDetail;
                if (returnData[srvc_type_id] == undefined) {
                    returnData[srvc_type_id] = [];
                }
                returnData[srvc_type_id].push(singleTaskDetail);
            });
        }
        return returnData;
    }
    /*
        Why use another function like createSbtskQuickAssign when we already have tms_create_sbtsk_batch? 
        The reason is that tms_create_sbtsk_batch accepts parameters such as srvc_type_id,
        while createSbtskQuickAssign follows a slightly different format. 
        This difference in parameter format led to the creation of a separate function. 
        However, both functions ultimately call tms_create_subtask.
    */
    createSbtskQuickAssign(query) {
        return new Promise(async (resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['batch_data_fr_wrapper'] = this.getSubtasksBatchDataFrWrapper(
                query.batch_data
            );
            query['batch_data'] = undefined;
            var form_data = JSON.stringify(query);
            // console.log("createSbtskQuickAssign form_data",form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_sbtsk_quick_assign_wrapper(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_create_sbtsk_quick_assign_wrapper
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        try {
                            // Extract necessary data from the response
                            const { updation_resp: responseUpdates } =
                                dbResp.data;
                            const {
                                batch_data_fr_wrapper: extractQueryForBatchData,
                                ...restQuery
                            } = query;

                            // Process each response update and trigger the workflow
                            for (const singleResponseUpdate in responseUpdates) {
                                if (
                                    responseUpdates.hasOwnProperty(
                                        singleResponseUpdate
                                    )
                                ) {
                                    const responseData =
                                        responseUpdates[singleResponseUpdate];
                                    // Construct the complete query for the current batch update
                                    const completeBatchQuery = {
                                        ...extractQueryForBatchData[
                                            singleResponseUpdate
                                        ][0],
                                        ...restQuery,
                                    };
                                    // Trigger the subtask workflow with the constructed query and response data
                                    this.triggerSubtaskWorkflow(
                                        this,
                                        completeBatchQuery,
                                        { data: responseData }
                                    );
                                }
                            }
                        } catch (error) {
                            console.error(
                                'createSbtskQuickAssign triggerSubtaskWorkflow failed :',
                                error
                            );
                        }

                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    // console.log('Error hint',error.hint);
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }
    async getSbtskFileDetails(params, query = {}) {
        if (!this.db) {
            return new sampleOperationResp(
                false,
                'DB not found',
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            );
        }
        query['org_id'] = users_model.getOrgId(this.userContext);
        query['usr_id'] = users_model.getUUID(this.userContext);
        query['ip_address'] = this.ip_address;
        query['user_agent'] = this.user_agent_;
        query['srvc_type_id'] = params['srvc_type_id'];
        query['srvc_req_id'] = params['srvc_req_id'];
        query['org_timezone'] = users_model.getOrgTimezone(this.userContext);
        // console.log('params',params);
        var form_data = JSON.stringify(query);
        // console.log('getSbtskFileDetails form_data',form_data);
        try {
            const res = (
                await this.db.tms_get_sbtsk_files_details(form_data)
            )[0].tms_get_sbtsk_files_details;
            return new sampleOperationResp(
                false,
                res.data,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log('tms_get_sbtsk_files_details', error);
            return new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
        // return new sampleOperationResp(false,
        //     'Testing',
        //     HttpStatus.StatusCodes.BAD_REQUEST);
    }

    callLambdaFnForAA(params, query, email) {
        return new Promise(async (resolve, reject) => {
            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);
                // console.log('lambdaRespData',lambdaRespData)
                if (lambdaRespData && lambdaRespData.status) {
                    // save to temp folder
                    // once saved trigger email
                    let org_id = users_model.getOrgId(this.userContext);
                    const d = new Date(); // today now
                    let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                    let savePath = path.join(
                        '',
                        'temp_files',
                        'auto_assign_dumps',
                        '' + org_id,
                        today
                    );
                    // console.log('savePath',savePath);

                    fs.mkdir(savePath, { recursive: true }, (err) => {
                        if (err) {
                            if (err.code != 'EEXIST') {
                                console.log(
                                    'Error in temp folder creation',
                                    err
                                );
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Could not process, please contact admin.',
                                        HttpStatus.StatusCodes.CONFLICT
                                    )
                                );
                            }
                        } else {
                            // console.log('Directory created successfully!');
                        }
                        // Start a send email process
                        let stream = getStreamFromVariable(lambdaRespData.data);
                        let fileName = `Auto assign dump ${today}_${d.getTime()}.csv`;
                        let filePath = path.join(savePath, fileName);

                        stream.on('end', () => {
                            console.log('File written');
                            //Send email by QUEUE
                            let to = email;
                            let subject = `Auto assign subtasks ${today}_${d.getTime()}`;
                            let message =
                                '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                            let attachments = [
                                { path: filePath, filename: fileName },
                            ];

                            //optinal param for save eamil_log
                            let usr_id = query['usr_id'];
                            let ip_address = query['ip_address'];
                            let user_agent = query['user_agent'];

                            const emailJobData = {
                                to,
                                subject,
                                message,
                                attachments,
                                org_id,
                                usr_id,
                                ip_address,
                                user_agent,
                            };
                            allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                            console.log('Job added');
                            resolve(
                                new sampleOperationResp(
                                    lambdaRespData.status,
                                    JSON.stringify(lambdaRespData),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        });
                        stream
                            .pipe(JSONToCsv())
                            .pipe(fs.createWriteStream(filePath));
                    });
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            lambdaRespData.message,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                console.log('lambdaRespData error', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Could not process, please contact admin.',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
        });
    }
    async processAutoAssignByLambda(query) {
        /*
            2. Get all service requests data as per the filter
            3. Get all the users along with their details
            4. Call lambda with all the service requests and the users data
            5. Save received response into a csv and send an email
            6. Post sending of email, delete the CSV from server
        */
        let lambdaArn = query['lambdaArn'];
        let email = query['email'];
        let lambdaPayload = { ...query };
        //Get service request form_data
        try {
            this.initServiceModel();
            let srvcReqs =
                await this.getServiceModelInstance().getSrvcReqsFrAutoAssignLambda(
                    query
                );
            // console.log('srvcReqs',srvcReqs);
            lambdaPayload['srvcReqs'] = srvcReqs;
        } catch (error) {
            return new sampleOperationResp(
                false,
                'Unable to find service requests',
                HttpStatus.StatusCodes.CONFLICT
            );
        }
        if (
            !lambdaPayload['srvcReqs'] ||
            lambdaPayload['srvcReqs'].length <= 0
        ) {
            return new sampleOperationResp(
                false,
                'No service requests found for the filter applied',
                HttpStatus.StatusCodes.CONFLICT
            );
        }
        // Get all users along with their tasks for the org, along with their custom fields data
        try {
            // console.log('JSON.stringify(query)',JSON.stringify(query))
            let allUsers = (
                await this.db.tms_get_all_users_fr_auto_assign_lambda(
                    JSON.stringify(query)
                )
            )[0].tms_get_all_users_fr_auto_assign_lambda;
            // console.log('allUsers',JSON.stringify(allUsers));
            lambdaPayload['allUsers'] = allUsers.data.users_json;

            lambdaPayload['user_fields_meta'] =
                allUsers.data.user_fields_meta.translatedFields || [];
            // console.log(lambdaPayload['user_fields_meta'])
        } catch (error) {
            return new sampleOperationResp(
                false,
                'Unable to find users',
                HttpStatus.StatusCodes.CONFLICT
            );
        }
        // console.log('lambdaPayload',lambdaPayload)
        if (
            !lambdaPayload['allUsers'] ||
            lambdaPayload['allUsers'].length <= 0
        ) {
            return new sampleOperationResp(
                false,
                'No users found',
                HttpStatus.StatusCodes.CONFLICT
            );
        }
        //Call lambda function here
        const params = {
            FunctionName: lambdaArn,
            InvocationType: 'RequestResponse',
            LogType: 'Tail',
            Payload: JSON.stringify(lambdaPayload),
        };

        return await this.callLambdaFnForAA(params, query, email);
    }

    generateAutoAssignSheetByLambda(query, is_customer_access = 0) {
        /*
            1. Get auto assign lambda arn for the service type id from org settings
        */
        return new Promise(async (resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_addr'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['dep_day'] = getTmrwDate();
            let email = users_model.getUserDetailsAsInFrontend(
                this.userContext
            )?.email;
            // console.log('email',email);
            if (email == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'User email not found',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
                return;
            }

            // console.log('query',query)
            let srvcTypeid = query['srvc_type_id'];
            let autoAssignLambdaCnfRes;
            try {
                autoAssignLambdaCnfRes = (
                    await this.db.tms_view_data_auto_dep_lambda_cnf(
                        JSON.stringify(query)
                    )
                )[0].tms_view_data_auto_dep_lambda_cnf;
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            if (autoAssignLambdaCnfRes?.data?.form_data == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            let autoAssignLambdaCnf = autoAssignLambdaCnfRes.data.form_data;
            let lambdaArn =
                autoAssignLambdaCnf[`auto_dep_lambda_arn_for_${srvcTypeid}`];
            console.log('lambdaArn', lambdaArn);
            if (lambdaArn == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            query['lambdaArn'] = lambdaArn;
            query['email'] = email;
            // Add a job
            const subtasks_model_data = this.getSbtskModelData(this);
            const jobData = { query, subtasks_model_data };
            allQueues.WIFY_AA_LAMBDA_BASED_SHEET_GENERATION.addJob(jobData);

            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    callLambdaFnForExecutingStatusUpdateDynamicFormLogic(
        entry_id,
        updateTypeId,
        query
    ) {
        /*
            1. Get subtask config data
            2. Get lambda urn
            3. Get service request data
            4. call lambda urn with everything
            5. give back the lambda result
            Critical query values srvc_type_id sbtsk_type_id srvc_req_id
            and ofcourse all that the dyanmic lambda needs meta,allValues,changedValues
        */
        return new Promise(async (resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['update_type_id'] = updateTypeId;
            let subtaskTypeOverviewResp = await this.getOverviewProto(query);
            if (!subtaskTypeOverviewResp.isSuccess()) {
                resolve(subtaskTypeOverviewResp);
                return;
            }
            let config_data = JSON.parse(
                subtaskTypeOverviewResp.resp
            )?.config_data;

            let { update_type_id, srvc_type_id, srvc_req_id } = query;
            //Get service request form_data
            this.initServiceModel();
            this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
            let srvcReqResp =
                await this.getServiceModelInstance().getSingleEntry(
                    {},
                    srvc_req_id
                );
            if (!srvcReqResp.isSuccess()) {
                resolve(srvcReqResp);
                return;
            }
            let pre_srvc_req_form_data = JSON.parse(
                srvcReqResp.resp
            )?.form_data;
            let request_data = pre_srvc_req_form_data?.form_data;
            request_data['tms_display_code'] = pre_srvc_req_form_data?.title;

            //Get lambda Arn Url
            // console.log('callLambdaFnForExecutingStatusUpdateDynamicFormLogic config_data',config_data);
            let lambdaARN =
                config_data[
                    `sbtsk_status_${update_type_id}_dynamic_form_lambda_arn`
                ];
            if (lambdaARN == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            let payload = {
                ...query,
                request_data,
            };
            // console.log('callLambdaFnForExecutingStatusUpdateDynamicFormLogic payload',JSON.stringify(payload))
            //Call lambda function here
            const params = {
                FunctionName: lambdaARN,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(payload),
            };

            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);

                if (lambdaRespData && lambdaRespData.status) {
                    resolve(
                        new sampleOperationResp(
                            lambdaRespData.status,
                            JSON.stringify(lambdaRespData),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            lambdaRespData.message,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Could not process, please contact admin.',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            resolve();
        });
    }
    async callLambdaFnForStatusValidation(query) {
        /*
            1. Get subtask config_data
            2. And check if sbtsk status is enable from config for ARN
            3. If enable then get service request form_data
            4. And call third-party api for validate this status
        */
        return new Promise(async (resolve, reject) => {
            let subtaskTypeOverviewResp = await this.getOverviewProto(query);
            if (!subtaskTypeOverviewResp.isSuccess()) {
                resolve(subtaskTypeOverviewResp);
                return;
            }
            let config_data = JSON.parse(
                subtaskTypeOverviewResp.resp
            )?.config_data;
            let { update_type_id, srvc_type_id, srvc_req_id } = query;

            let isEnableLambdaBasedValidationKey = `sbtsk_status_enable_lambda_based_validation_for_${update_type_id}_status`;
            let isEnableLambdaBasedValidation =
                config_data?.[isEnableLambdaBasedValidationKey];
            if (isEnableLambdaBasedValidation) {
                //Get service request form_data
                this.initServiceModel();
                this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
                let srvcReqResp =
                    await this.getServiceModelInstance().getSingleEntry(
                        {},
                        srvc_req_id
                    );
                if (!srvcReqResp.isSuccess()) {
                    resolve(srvcReqResp);
                    return;
                }
                let pre_srvc_req_form_data = JSON.parse(
                    srvcReqResp.resp
                )?.form_data;
                let request_data = pre_srvc_req_form_data?.form_data;
                request_data['tms_display_code'] =
                    pre_srvc_req_form_data?.title;
                let form_data = query;

                //Get lambda Arn Url
                let lambdaARN =
                    config_data[
                        `sbtsk_status_${update_type_id}_validation_lambda_arn`
                    ];

                //Call lambda function here
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ form_data, request_data }),
                };

                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);

                    if (lambdaRespData && lambdaRespData.status) {
                        resolve(
                            new sampleOperationResp(
                                true,
                                lambdaRespData.message,
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    } else {
                        resolve(
                            new sampleOperationResp(
                                false,
                                lambdaRespData.message,
                                HttpStatus.StatusCodes.BAD_REQUEST
                            )
                        );
                    }
                } catch (error) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Cannot update, please contact admin.',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } else {
                console.log('Lambda based validation is not enable');
            }

            resolve();
        });
    }

    async callLambdaFnForBleExecutuion(query) {
        /*
            1. Get subtask config_data
            2. And check if sbtsk status is enable from config for ARN
            3. If enable then get service request form_data
            4. And call third-party api for validate this status
        */
        return new Promise(async (resolve, reject) => {
            const { contextInfo } = query;
            if (contextInfo?.sbtskDetails == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'params missing',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            let subtaskTypeOverviewResp = await this.getOverviewProto(
                contextInfo.sbtskDetails
            );
            if (!subtaskTypeOverviewResp.isSuccess()) {
                resolve(subtaskTypeOverviewResp);
                return;
            }
            let config_data = JSON.parse(
                subtaskTypeOverviewResp.resp
            )?.config_data;
            let { update_type_id } = contextInfo.sbtskDetails;

            //Get lambda Arn Url
            let statusCustomFields = JSON.parse(
                config_data[`sbtsk_status_${update_type_id}_fields`]
            );
            let statusTranslatedFields = statusCustomFields.translatedFields;

            let lambdaArn;
            for (const obj of statusTranslatedFields) {
                if (obj.cust_component === 'WIFY_BLE_COMPONENT') {
                    lambdaArn = obj.lambdaARN;
                }
            }

            //Call lambda function here
            const params = {
                FunctionName: lambdaArn,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(query),
            };

            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);

                if (lambdaRespData && lambdaRespData.status) {
                    resolve(
                        new sampleOperationResp(
                            true,
                            lambdaRespData.body,
                            HttpStatus.StatusCodes.OK
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            lambdaRespData.body,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Cannot update, please contact admin.',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
            }

            resolve();
        });
    }

    getOrgDetails(org_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_org_details(org_id).then(
                (res) => {
                    var orgViewResp = new db_resp(res[0].tms_get_org_details);
                    if (!orgViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(orgViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getUserDetails(user_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_details(user_id).then(
                (res) => {
                    var usrViewResp = new db_resp(res[0].tms_get_user_details);
                    if (!usrViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(usrViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createSbtskBatch(
        query,
        srvc_type_id,
        is_customer_access = 0,
        fr_bulk_upload = false,
        srvc_req_id
    ) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_type_id'] = srvc_type_id;
                query['srvc_req_id'] = srvc_req_id;
                query['batch_data'] = query.batch_data;
                query['is_customer_access'] = is_customer_access;
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );
                // check if geo fencing is enabled && device is mobile
                // let resp = await this.processGeoFenceAutomation(query, 0, '', false);
                // if(!resp.isSuccess()){
                //     resolve(resp);
                //     return;
                // }
                var form_data = JSON.stringify(query);
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                // console.log('createOrUpdateBatch Form_data ==>', form_data);
                this.db.tms_create_sbtsk_batch(form_data).then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_create_sbtsk_batch);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            let workflow = getSbtskWorkflowModel(
                                this.getFreshInstance(this)
                            );
                            workflow.trigger(
                                query,
                                0,
                                dbResp,
                                is_customer_access
                            );
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        // console.log('Error hint',error.hint);
                        this.fatalDbError(
                            resolve,
                            fr_bulk_upload ? error : error.hint
                        );
                    }
                );
            } catch (error) {
                // console.log('createSbtskBatch error',error);
                this.fatalDbError(resolve, fr_bulk_upload ? error : error.hint);
            }
        });
    }

    getTechnicianAppOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_technician_app_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_technician_app_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getUserAvailability(query) {
        return new Promise((resolve, reject) => {
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['assignee'] = query['assignee'];
            requester['day'] = query['day'];
            requester['sbtsk_type'] = query['sbtsk_type'];

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            this.db.tms_get_sbtsk_usr_availability(requesterInfo).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }
                    var dbResp = new db_resp(
                        res[0].tms_get_sbtsk_usr_availability
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getOverviewProto(query) {
        return new Promise((resolve, reject) => {
            // console.log("Trying to get overview data for ",this.srvcReqId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log('tms_get_sbtsks_overview_proto form_data', form_data);
            this.db.tms_get_sbtsks_overview_proto(form_data).then(
                (res) => {
                    console.log('tms_get_sbtsks_overview_proto db res', res);
                    var dbResp = new db_resp(
                        res[0].tms_get_sbtsks_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async createdOrUpdateDeployment(params, body) {
        if (!this.db) {
            return new sampleOperationResp(
                false,
                'DB not found',
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            );
        }
        const query = {};
        query['org_id'] = users_model.getOrgId(this.userContext);
        query['usr_id'] = users_model.getUUID(this.userContext);
        query['ip_address'] = this.ip_address;
        query['user_agent'] = this.user_agent_;
        query['srvc_type_id'] = params['srvc_type_id'];
        query['srvc_req_id'] = params['srvc_req_id'];
        query['calendarObj'] = body;
        query['org_timezone'] = users_model.getOrgTimezone(this.userContext);
        // console.log('params',params);
        var form_data = JSON.stringify(query);
        // console.log('form_data', form_data);
        try {
            const res = (
                await this.db.tms_create_or_update_deployment(form_data)
            )[0].tms_create_or_update_deployment;

            let workflow = getSbtskWorkflowModel(this.getFreshInstance(this));
            workflow.triggerNotificationWorkFlow(res.data);
            if (query?.calendarObj?.subtaskIdsToBeDeleted) {
                workflow.triggerProcessRatingsQueueOnSbtskDeletion(
                    { ...query },
                    res.data
                );
                if (res?.data?.entry_id_vs_query_fr_deletion) {
                    workflow.triggerSubtaskDeletionNotificationWorkFlow(
                        res?.data
                    );
                }
            }
            return new sampleOperationResp(
                false,
                res.data,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log('getDeploymentDayProto', error);
            return new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
        // return new sampleOperationResp(false,
        //     'Testing',
        //     HttpStatus.StatusCodes.BAD_REQUEST);
    }

    async getDeploymentDayProto(params, queryParams) {
        try {
            if (!this.db) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(queryParams);
            // const resp = await
            const rolesFrDeployment = queryParams['rolesFrDeployment'] || [];
            // console.log(queryParams['rolesFrDeployment']);
            const query = {};
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = params['srvc_type_id'];
            query['srvc_req_id'] = params['srvc_req_id'];
            query['day'] = params['day'];
            query['rolesFrDeployment'] = rolesFrDeployment.map(
                (singleRoleInJson) => JSON.parse(singleRoleInJson)
            );
            query['sbtsk_start_day'] = queryParams['sbtsk_start_day'];
            query['sbtsk_end_day'] = queryParams['sbtsk_end_day'];
            query['sbtsk_start_time'] = queryParams['sbtsk_start_time'];
            query['sbtsk_end_time'] = queryParams['sbtsk_end_time'];
            query['reporting_to'] = queryParams['reporting_to'];
            query['filters'] = filters_;
            query['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            var form_data = JSON.stringify(query);
            // console.log('form_data',form_data);

            const readinessRes = (
                await this.db.tms_check_deployment_day_readiness(form_data)
            )[0].tms_check_deployment_day_readiness;
            const readinessResponse =
                this.getReadinessResponseFrProjectBased(readinessRes);

            const res = (
                await this.db.tms_get_deployment_day_proto(form_data)
            )[0].tms_get_deployment_day_proto;
            if (readinessResponse) {
                res.data['mandatory_fields_label_arr_fr_project'] =
                    readinessResponse.mandatory_fields_label_arr_fr_project;
            }
            return new sampleOperationResp(
                false,
                res.data,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log('getDeploymentDayProto', error);
        }
        return new sampleOperationResp(
            false,
            'Fatal error!',
            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
        );
    }

    getReadinessResponseFrProjectBased(res) {
        let readinessRes = res.data;
        if (readinessRes?.readiness_check?.mandatory_fields_vs_role_id) {
            const mandatory_keys =
                readinessRes?.readiness_check?.mandatory_keys;
            const mandatory_fields_vs_role_id =
                readinessRes?.readiness_check?.mandatory_fields_vs_role_id;
            const srvc_req_form_data =
                readinessRes?.readiness_check?.srvc_req_form_data;
            const customFields = readinessRes?.readiness_check?.cust_fields;

            const mandatory_fields_label_arr_fr_project = {};
            if (!mandatory_keys) {
                for (const [key, value] of Object.entries(
                    mandatory_fields_vs_role_id
                )) {
                    const missingKeys = getMandatoryFieldsKeyNotFilled(
                        srvc_req_form_data,
                        JSON.parse(value)
                    );
                    const mandatoryFieldsLabel = getTheLabelFrMandatoryFields(
                        customFields,
                        missingKeys
                    );
                    mandatory_fields_label_arr_fr_project[key] =
                        `${mandatoryFieldsLabel}`;
                }
                if (mandatory_fields_label_arr_fr_project) {
                    readinessRes['mandatory_fields_label_arr_fr_project'] =
                        mandatory_fields_label_arr_fr_project;
                    return readinessRes;
                }
            }
        }
    }

    getReadinessResponseFrTaskbased(readinessRes) {
        if (readinessRes?.mandatory_keys?.length > 0) {
            const mandatory_keys = readinessRes.mandatory_keys;
            const srvc_req_form_data = readinessRes.srvc_req_form_data;
            const customFields = readinessRes.cust_fields;
            const missingKeys = getMandatoryFieldsKeyNotFilled(
                srvc_req_form_data,
                mandatory_keys
            );
            const mandatoryFieldsLabel = getTheLabelFrMandatoryFields(
                customFields,
                missingKeys
            );
            if (mandatoryFieldsLabel.length > 0) {
                return new sampleOperationResp(
                    false,
                    `Cannot create this subtask please enter ${mandatoryFieldsLabel}`,
                    HttpStatus.StatusCodes.CONFLICT
                );
            }
        }
    }

    validateOtpFrStatusUpdate(otp, srvc_req_id, entry_id) {
        return new Promise(async (resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
            // console.log("otp",otp,srvc_req_id);
            this.db
                .tms_verify_otp_fr_sbtsk_update(otp, srvc_req_id, entry_id)
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_verify_otp_fr_sbtsk_update
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.code,
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    reSendOtpFrStatusUpdate(query, srvc_req_id, is_customer_access = 0) {
        return new Promise(async (resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_req_id'] = srvc_req_id;
            // console.log("query",query);

            let workflow = getSbtskWorkflowModel(this.getFreshInstance(this));
            workflow.trigger(query, query.sbtsk_db_id, {}, is_customer_access);

            resolve(
                new sampleOperationResp(
                    true,
                    'Successfully Otp has been resent',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    getSingleEntry(query, entry_id = 0, updateTypeId = '') {
        return new Promise(async (resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            // console.log("Trying to get overview data for ",this.srvcReqId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['entry_id'] = entry_id;
            query['update_type_id'] = updateTypeId;
            query['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            var form_data = JSON.stringify(query);
            // resolve(
            //     new sampleOperationResp(true,
            //     'Got - ' + JSON.stringify(query),
            //     HttpStatus.StatusCodes.OK)
            // );
            // return;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log('Form db',form_data);
            this.db.tms_get_sbtsk_details(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_sbtsk_details);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        this.getViewDataFrForm({}).then((operationResp) => {
                            // console.log(dbResp.data);
                            if (operationResp.isSuccess()) {
                                var finalResp = JSON.parse(operationResp.resp);
                                // console.log(dbResp.data);
                                finalResp.form_data = dbResp.data;
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(finalResp),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                resolve(operationResp);
                            }
                        });
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdate(
        query,
        entry_id = 0,
        updateTypeId = '',
        onlyFrLocVerification = false
    ) {
        return new Promise(async (resolve, reject) => {
            try {
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const isAutoMoveStatus = query['auto_move_status'];

                // var validationResp = this.validateCreateNewForm(query);
                // if (!validationResp.isSuccess()) {
                //     resolve(validationResp);
                //     return;
                // }
                query['org_id'] = isAutoMoveStatus
                    ? query?.sbtsk_org_id
                    : users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_req_id'] = query.srvc_req_id
                    ? query.srvc_req_id
                    : this.srvcReqId;
                query['update_type_id'] = updateTypeId;
                query['onlyFrLocVerification'] = onlyFrLocVerification;
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );
                // check if geo fencing is enabled && device is mobile
                let resp = await this.processGeoFenceAutomation(
                    query,
                    entry_id,
                    updateTypeId,
                    onlyFrLocVerification
                );
                if (!resp.isSuccess()) {
                    resolve(resp);
                    return;
                }

                let otpResp;
                if (query.otp) {
                    otpResp = await this.validateOtpFrStatusUpdate(
                        query.otp,
                        query.srvc_req_id,
                        entry_id
                    );
                    // console.log("otpResp",otpResp);
                    if (!otpResp.success) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                JSON.stringify(
                                    otpResp.resp +
                                        ' ' +
                                        '- Get in touch with support team'
                                ),
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                        return;
                    }
                }

                //Call Lambda function for subtask status validation.
                if (entry_id > 0 && !isAutoMoveStatus) {
                    try {
                        let respData =
                            await this.callLambdaFnForStatusValidation(query);
                        if (respData && !respData.isSuccess()) {
                            resolve(respData);
                            return;
                        }
                    } catch (error) {
                        console.log(
                            'callLambdaFnForStatusValidation error',
                            error
                        );
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Cannot update, please contact admin',
                                HttpStatus.StatusCodes.BAD_REQUEST
                            )
                        );
                        return;
                    }
                }

                var form_data = JSON.stringify(query);
                this.db.tms_create_subtask(form_data, entry_id).then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_create_subtask);

                        if (dbResp.code == 'title_or_key_exists') {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Title or Key not unique',
                                    HttpStatus.StatusCodes.CONFLICT
                                )
                            );
                        } else if (dbResp.code == 'permission_denied') {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Permission denied',
                                    HttpStatus.StatusCodes.CONFLICT
                                )
                            );
                        } else if (
                            dbResp.code == 'No change in status, update failed'
                        ) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'No change in status, update failed',
                                    HttpStatus.StatusCodes.CONFLICT
                                )
                            );
                        } else if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            let workflow = getSbtskWorkflowModel(
                                this.getFreshInstance(this)
                            );
                            workflow.trigger({ ...query }, entry_id, dbResp);
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );

                            const _queueData = {
                                sbtsk_type_id: query.sbtsk_type_id,
                                srvc_req_id: query.srvc_req_id,
                                srvc_type_id: query.srvc_type_id,
                                entry_id: entry_id, // imp
                                mic_files: query.mic_files, // imp
                                update_type_id: query.update_type_id, // imp
                                sbtsk_model_data: this.getSbtskModelData(this),
                            };
                            console.log(
                                'process_gai_audio_transcript :: performJob :: will begin :: ',
                                _queueData
                            );
                            if (_queueData.mic_files) {
                                allQueues.WIFY_TMS_PROCESS_GAI_AUDIO_TRANSCRIPT.addJob(
                                    _queueData
                                );
                            }
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    async getAudioTranscript({ entry_id, update_type_id }) {
        try {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return null;
            }

            const _resDb = await this.db.tms_get_subtask_gai_mic_files(
                JSON.stringify({
                    entry_id,
                    update_type_id,
                })
            );
            // console.log('getAudioTranscript :: _resDb :: ', _resDb);

            const dbResp = new db_resp(_resDb[0].tms_get_subtask_gai_mic_files);
            // console.log('getAudioTranscript :: dbResp :: ', dbResp);
            return dbResp.data;
        } catch (error) {
            console.error('getAudioTranscript :: error :: ', error);
            return null;
        }
    }

    async updateAudioTranscript({ entry_id, update_type_id, gai_mic_files }) {
        try {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return null;
            }

            const formData = {
                entry_id: entry_id,
                update_type_id,
                gai_mic_files,
            };
            // encodeInnerJsonStrings(formData.gai_mic_files);

            // Now stringify the whole object
            const _form = JSON.stringify(formData);
            const _resDb =
                await this.db.tms_update_subtask_gai_mic_files(_form);

            const dbResp = new db_resp(
                _resDb[0].tms_update_subtask_gai_mic_files
            );
            return dbResp.data;
        } catch (error) {
            console.error('updateAudioTranscript :: error :: ', error);
            return null;
        }
    }

    getServiceModelInstance() {
        if (!this.service_model_reused_instance) {
            this.service_model_reused_instance =
                require('./services_model').getInstance();
        }
        this.service_model_reused_instance.getFreshInstance(
            this.service_model_reused_instance
        );
        return this.service_model_reused_instance;
    }

    async updateGPSLocationVerfication(
        sbtsk_type_id,
        sbtsk_db_id,
        updateTypeId,
        service_model_reused_instance
    ) {
        console.log(
            'async updateGPSLocationVerfication called',
            sbtsk_db_id,
            updateTypeId
        );
        this.service_model_reused_instance = service_model_reused_instance;
        // the location details of user already reside in
        // the update data
        // so we will have to get it from there
        let queryForUpdate = { sbtsk_type_id };
        let subtskDetailsResp = await this.getSingleEntry(
            queryForUpdate,
            sbtsk_db_id,
            updateTypeId
        );
        if (!subtskDetailsResp.isSuccess()) {
            console.error('async failed subtskDetailsResp', subtskDetailsResp);
            return;
        }
        let sbtskDetails = JSON.parse(subtskDetailsResp.resp)?.form_data;
        let user_gps_location_details =
            sbtskDetails?.form_data?.user_gps_location_details;
        queryForUpdate['user_gps_location_details'] = user_gps_location_details;
        this.updatingLocVerficationInBg = true;
        let createOrUpdateResp = await this.createOrUpdate(
            queryForUpdate,
            sbtsk_db_id,
            updateTypeId,
            true
        );
        console.log('async createOrUpdateResp ', createOrUpdateResp);
        return createOrUpdateResp;
    }

    processGeoFenceAutomation(
        query,
        entry_id,
        updateTypeId,
        dontStartLocMapping = false
    ) {
        return new Promise(async (resolve, reject) => {
            let subtaskTypeOverviewResp = await this.getOverviewProto(query);
            if (!subtaskTypeOverviewResp.isSuccess()) {
                resolve(subtaskTypeOverviewResp);
                return;
            }
            const subtskTypeDetails = JSON.parse(
                subtaskTypeOverviewResp.resp
            )?.config_data;
            if (subtskTypeDetails == undefined) {
                resolve(subtaskTypeOverviewResp);
                return;
            }
            var verificationStatus = 'pending';
            // console.log('subtskTypeDetails',subtskTypeDetails);
            if (
                this.isGeoVerificationEnabledFrStatus(
                    subtskTypeDetails,
                    updateTypeId
                )
            ) {
                let geoVerificationMode =
                    subtskTypeDetails.geo_verification_mode;
                let geoVerificationLimitMtrs =
                    subtskTypeDetails.geo_verification_limit_in_meters;
                console.log(
                    'Geo verification is enabled',
                    geoVerificationMode,
                    geoVerificationLimitMtrs
                );
                // console.log("isMobileApp",this.userContext.isMobileApp);
                if (
                    this.userContext.isMobileApp ||
                    this.updatingLocVerficationInBg
                ) {
                    const locationInUpdateForm =
                        query.user_gps_location_details;
                    console.log('locationInUpdateForm', locationInUpdateForm);
                    if (
                        locationInUpdateForm &&
                        Object.keys(locationInUpdateForm).length > 0 &&
                        !locationInUpdateForm.isMock
                    ) {
                        // location is there in update form
                        // TODO check if location is there in service request
                        let serviceRequestResp =
                            await this.getServiceRequestDetails(
                                query,
                                entry_id,
                                updateTypeId
                            );
                        if (serviceRequestResp.isSuccess()) {
                            let srvcReqDetails = JSON.parse(
                                serviceRequestResp.resp
                            )?.form_data?.form_data;
                            let srvcReqLocation =
                                srvcReqDetails?.geocoding_location_data
                                    ?.location;
                            let srvc_type_id = srvcReqDetails.srvc_type_id;
                            let srvc_req_id = srvcReqDetails.entry_id;
                            if (srvcReqLocation?.lat) {
                                let isInsideGeoFence =
                                    checkifInsideCircularGeofence(
                                        geoVerificationLimitMtrs,
                                        locationInUpdateForm.latitude,
                                        locationInUpdateForm.longitude,
                                        locationInUpdateForm.accuracy,
                                        srvcReqLocation.lat,
                                        srvcReqLocation.lng,
                                        STANDARD_DEVIATION_FOR_PLACES_LOCATION
                                    );
                                // console.log("isInsideGeoFence",isInsideGeoFence);
                                if (isInsideGeoFence) {
                                    if (
                                        locationInUpdateForm.accuracy >
                                        GPS_NETWORK_LOW_ACCURACY
                                    ) {
                                        verificationStatus = 'low_network';
                                    } else {
                                        verificationStatus = 'verified';
                                    }
                                } else {
                                    if (geoVerificationMode == 'lenient_mode') {
                                        verificationStatus = 'failed';
                                    } else if (
                                        geoVerificationMode == 'strict_mode'
                                    ) {
                                        resolve(
                                            new sampleOperationResp(
                                                false,
                                                'You are not at the service request location',
                                                HttpStatus.StatusCodes.BAD_REQUEST
                                            )
                                        );
                                        return;
                                    }
                                }
                            } else {
                                if (!dontStartLocMapping) {
                                    this.initServiceModel();
                                    this.initServiceModelTypeAndReqId(
                                        srvc_type_id,
                                        srvc_req_id
                                    );
                                    // console.log('params',query,srvc_req_id,srvcReqDetails)
                                    console.log(
                                        'trigger WIFY_SRVC_REQ_LOC_MAPPING from subtask_model'
                                    );
                                    let jobData = {
                                        form_data: srvcReqDetails,
                                        query,
                                        new_entry_id: srvc_req_id,
                                        services_model_data:
                                            this.getServiceModelInstance().getServicesModelData(
                                                this.getServiceModelInstance()
                                            ),
                                    };
                                    allQueues.WIFY_SRVC_REQ_LOC_MAPPING.addJob(
                                        jobData
                                    );
                                }
                            }
                        }
                    } else {
                        if (geoVerificationMode == 'lenient_mode') {
                            verificationStatus = 'no_gps';
                        } else if (geoVerificationMode == 'strict_mode') {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Location mandatory',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }
                    }
                } else {
                    verificationStatus = 'no_gps';
                }
                query['user_gps_location_status'] = verificationStatus;
            } else {
                console.log('Geo verification is not enabled');
            }
            resolve(
                new sampleOperationResp(true, query, HttpStatus.StatusCodes.OK)
            );
        });
    }

    getServiceRequestDetails(query, sbtsk_db_id, updateTypeId) {
        return new Promise(async (resolve, reject) => {
            let subtskDetailsResp = await this.getSingleEntry(
                query,
                sbtsk_db_id,
                updateTypeId
            );
            if (!subtskDetailsResp.isSuccess()) {
                resolve(subtskDetailsResp);
                return;
            }
            let sbtskDetails = JSON.parse(subtskDetailsResp.resp)?.form_data;
            // console.log('sbtskDetails',sbtskDetails);
            this.initServiceModel();
            this.initServiceModelTypeAndReqId(
                sbtskDetails.srvc_type_id,
                sbtskDetails.srvc_req_id
            );
            let serviceRequestResp =
                await this.getServiceModelInstance().getSingleEntry(
                    {},
                    sbtskDetails.srvc_req_id
                );
            // console.log('serviceRequestResp',serviceRequestResp)
            resolve(serviceRequestResp);
        });
    }

    isGeoVerificationEnabledFrStatus(config_data, status) {
        return config_data?.enable_geo_verification_for?.includes(status);
    }

    getViewDataFrSubtaskTimeline(query, sbtsk_type_id = 0, entry_id = 0) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['sbtsk_type_id'] = sbtsk_type_id;
            requester['sbtsk_id'] = entry_id;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            // console.log("requesterInfo timeline",requesterInfo);

            this.db
                .tms_get_sbtsk_timeline(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_sbtsk_timeline);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getViewDataFrForm(query) {
        return new Promise(async (resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_req_id'] = this.srvcReqId;
            query['sbtsk_type_id'] = this.subtaskTypeId;
            query['srvc_type_id'] = this.srvc_type_id
                ? this.srvc_type_id
                : query?.srvc_type_id;
            query['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            const is_frm_sbtsk_create_form =
                query.is_frm_sbtsk_create_form || false;

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            if (is_frm_sbtsk_create_form) {
                const readinessRes = (
                    await this.db.tms_hlpr_get_mandatory_fields(form_data)
                )[0].tms_hlpr_get_mandatory_fields;
                const readinessResponse =
                    this.getReadinessResponseFrTaskbased(readinessRes);
                if (readinessResponse) {
                    return resolve(readinessResponse);
                }
            }
            this.db.tms_sbtsks_getview_data(form_data).then(
                (res) => {
                    var sbtskResp = new db_resp(res[0].tms_sbtsks_getview_data);
                    if (!sbtskResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        if (is_frm_sbtsk_create_form) {
                            if (sbtskResp.code === 'no_matching_group_found') {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Error in fetching assignee list, no matching location group found for the request',
                                        HttpStatus.StatusCodes.CONFLICT
                                    )
                                );
                                return;
                            } else if (sbtskResp.code === 'no_pincode_found') {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Error in fetching assignee list, no pin code found on the request',
                                        HttpStatus.StatusCodes.CONFLICT
                                    )
                                );
                                return;
                            } else if (sbtskResp.code === 'no_user_found') {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'No users found for this location group',
                                        HttpStatus.StatusCodes.CONFLICT
                                    )
                                );
                                return;
                            }
                        }
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(sbtskResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAll(query) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['srvc_req_id'] = this.srvcReqId ? this.srvcReqId : 0;
            requester['is_my_tasks'] = this.srvcReqId ? false : true;
            requester['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            // console.log("Call db function ",requesterInfo)
            this.db
                .tms_get_sbtsks(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_sbtsks);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    validateCreateNewForm(form_data) {
        // if(form_data['srvc_type_name'] ==""
        //     || form_data['srvc_type_key'] ==""
        //     || form_data['srvc_type_icon_selector'] == "") {
        //     return new sampleOperationResp(false,
        //         "Mandatory parameters missing!",
        //         HttpStatus.StatusCodes.BAD_REQUEST);
        // }
        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    initServiceModel() {
        this.getServiceModelInstance().database = this.db;
        this.getServiceModelInstance().ip_addr = this.ip_address;
        this.getServiceModelInstance().user_agent = this.user_agent_;
        this.getServiceModelInstance().user_context = this.userContext;
    }

    initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id) {
        // Specific for service types
        this.getServiceModelInstance().srvc_type_id = srvc_type_id;
        this.getServiceModelInstance().srvc_req_id = srvc_req_id;
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    getSbtskModelData(services_model) {
        return {
            ip_address: services_model.ip_address,
            user_agent: services_model.user_agent_,
            user_context: services_model.user_context,
        };
    }

    async getAllTodayTaskBySbtskType(sbtsk_type_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.get_due_sbtsk_fr_today(sbtsk_type_id).then(
                (res) => {
                    var dbResp = new db_resp(res[0].get_due_sbtsk_fr_today);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }
    //task reminder
    async getUpcomingTaskBySbtskType(sbtsk_type_id, reminder_time) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_upcoming_jobs_for_reminder(
                    sbtsk_type_id,
                    reminder_time
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_upcoming_jobs_for_reminder
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getTaskUpdatesOverviewProto(query, sbtsk_type_id = 0) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            if (sbtsk_type_id) {
                query['sbtsk_type_id'] = sbtsk_type_id;
            }
            var form_data = JSON.stringify(query);
            // console.log("form_data",form_data)
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_task_updates_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_task_updates_overview_proto
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getTaskUpdates(query, sbtsk_type_id) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['sbtsk_type_id'] = sbtsk_type_id;
            requester['is_customer_access'] = 0;
            requester['srvc_type_id'] = this.srvcTypeId || 0;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            // console.log('requesterInfo----->>>>>',requesterInfo,'filters>>', filters_);
            this.db
                .tms_get_sbtsks_list_fr_task_updates(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_sbtsks_list_fr_task_updates
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    exportTaskUpdatesByEmail(query, sbtsk_type_id) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_addr'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['sbtsk_type_id'] = sbtsk_type_id;
            requester['is_customer_access'] = 0;
            requester['srvc_type_id'] = this.srvcTypeId || 0;

            const jobData = { requester, filters_ };
            allQueues.WIFY_TASK_UPDATES_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({
                module: moduleKeys.taskUpdates,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processTaskUpdatesExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }

                dbObj
                    .tms_get_task_updates_dumps_fr_usr(
                        requesterInfo,
                        filters_,
                        { stream: true }
                    )
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'task_updates_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Task updates dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.taskUpdates,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        JSONToCsv({
                                            path: '*.tms_get_task_updates_dumps_fr_usr',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.taskUpdates,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            console.log('EXPORT_DUMP_ERR ......');
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.taskUpdates,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    createTimelineforSubtask(
        query,
        sbtskTypeId,
        entry_id,
        updateType,
        timelineTitle
    ) {
        return new Promise(async (resolve, reject) => {
            try {
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                const form_data = JSON.stringify(query);

                this.db
                    .tms_add_to_sbtsk_timeline(
                        sbtskTypeId,
                        entry_id,
                        updateType,
                        timelineTitle,
                        form_data
                    )
                    .then(
                        (res) => {
                            var dbResp = new db_resp(
                                res[0].tms_add_to_sbtsk_timeline
                            );

                            if (!dbResp.status) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );

                                return;
                            } else {
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(dbResp.data),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            }
                        },
                        (error) => {
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }
    // Define a function to handle workflow triggering
    triggerSubtaskWorkflow(context, query, dbResp) {
        let workflow = getSbtskWorkflowModel(context.getFreshInstance(context));
        workflow.trigger(query, 0, dbResp);
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    set sbtsk_type_id(subtaskTypeId) {
        this.subtaskTypeId = subtaskTypeId;
    }

    get sbtsk_type_id() {
        return this.subtaskTypeId;
    }

    set srvc_req_id(srvcReqId) {
        this.srvcReqId = srvcReqId;
    }

    get srvc_req_id() {
        return this.srvcReqId;
    }

    getFreshInstance(model) {
        const clonedInstance = new subtasks_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new subtasks_model();
        return instance;
    }
}

module.exports = new subtasks_model();
