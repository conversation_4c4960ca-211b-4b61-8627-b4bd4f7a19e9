{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\util\\\\helpers.js\";\nimport React from 'react';\nimport { DatePicker, message, Rate, Empty, Button } from 'antd';\nimport { isArray } from 'lodash';\nimport moment from 'moment';\nimport { v4 as uuidv4 } from 'uuid';\nimport ConfigHelpers from './ConfigHelpers';\nimport * as XLSX from 'xlsx';\nimport { decodeFileSectionsFrmJson } from '../components/wify-utils/FieldCreator/helpers';\nimport { isInsideMobileApp } from './AppHelpers';\nimport LocationSearchInput from '../components/LocationSearchInput';\nimport { addressFill, getAddressBasedOnLatAndLng, getAddressFieldKeys, getConcatenatedAddressFrmForm } from './CustomerHelpers';\nimport { getAddressObj, getConcatenatedAddressFormData } from '../routes/users/helper';\nimport MapComponent from '../components/wify-utils/MapComponent/index ';\nimport { useLocation } from 'react-router-dom';\nconst _ = require('lodash');\nconst fileExtensionRegex = /(?:\\.([^.]+))?$/;\n// Common render function\nexport const renderCellContent = (text, item, statuses, backgroundClass) => {\n  const shouldApplyBackground = text !== 0 && !isKeyInDoneOrClosedStatuses(item.status_key, statuses);\n  const className = text !== 0 ? `table_zero ${shouldApplyBackground ? backgroundClass : ''}` : '';\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 12\n    }\n  }, \" \", text, \" \");\n};\n\n// Utility function to check if a key is in DONE or CLOSED statuses\nexport const isKeyInDoneOrClosedStatuses = (key, srvc_type_statuses) => {\n  var _statuses, _statuses$DONE, _statuses2, _statuses2$CLOSED;\n  let statuses;\n  try {\n    statuses = JSON.parse(srvc_type_statuses);\n  } catch (error) {\n    console.error('Failed to parse statuses JSON:', error);\n    return false;\n  }\n  const doneKeys = (_statuses = statuses) === null || _statuses === void 0 ? void 0 : (_statuses$DONE = _statuses.DONE) === null || _statuses$DONE === void 0 ? void 0 : _statuses$DONE.map(status => status.key);\n  const closedKeys = (_statuses2 = statuses) === null || _statuses2 === void 0 ? void 0 : (_statuses2$CLOSED = _statuses2.CLOSED) === null || _statuses2$CLOSED === void 0 ? void 0 : _statuses2$CLOSED.map(status => status.key);\n  return (doneKeys === null || doneKeys === void 0 ? void 0 : doneKeys.includes(key)) || (closedKeys === null || closedKeys === void 0 ? void 0 : closedKeys.includes(key));\n};\nexport const calculateCenterLatLng = latlngArray => {\n  if (latlngArray.length === 0) {\n    return null;\n  }\n  var sumLat = 0;\n  var sumLng = 0;\n\n  // Calculate the sum of all latitudes and longitudes\n  for (var i = 0; i < latlngArray.length; i++) {\n    sumLat += latlngArray[i][0]; // Latitude\n    sumLng += latlngArray[i][1]; // Longitude\n  }\n  // Calculate the average of latitudes and longitudes\n  var avgLat = sumLat / latlngArray.length;\n  var avgLng = sumLng / latlngArray.length;\n  return [avgLat, avgLng]; // Return the center latitude and longitude as an array\n};\nexport const convertTimeToSortableFormat = timeString => {\n  const [time, period] = timeString.split(/(?=[AP]M)/i); // Split time and period (AM/PM)\n  let [hours, minutes] = time.split(':'); // Split hours and minutes\n  hours = parseInt(hours, 10); // Parse hours as integer\n  if (period && period.toLowerCase() === 'pm' && hours < 12) {\n    hours += 12; // Add 12 hours for PM time\n  } else if (period && period.toLowerCase() === 'am' && hours === 12) {\n    hours = 0; // Convert 12 AM to 0 hours\n  }\n  return hours * 60 + parseInt(minutes, 10); // Convert to minutes for sorting\n};\nexport function downloadExcelFileWithTabwise(dataWithCategories, fileName) {\n  /*\r\n      //Generate and download an Excel file with tab-wise data.\r\n      //Sample data format for downloadExcelFileWithTabwise function:\r\n      const dataWithCategories = [\r\n          {\r\n              \"name\": \"Seating\",\r\n              \"items\": [\r\n              {\r\n                  \"Area\": \"1BHK\",\r\n                  \"Room\": \"Hall\",\r\n                  \"Width\": 12\r\n              },\r\n              {\r\n                  \"Area\": \"2BHK\",\r\n                  \"Room\": \"Bedroom\",\r\n                  \"Width\": 20\r\n              }\r\n              ]\r\n          },\r\n          {\r\n              \"name\": \"Sqft\",\r\n              \"items\": [\r\n              {\r\n                  \"Quantity\": 10,\r\n                  \"Price\": 20,\r\n                  \"Total\": 200\r\n              }\r\n              ]\r\n          }\r\n      ];\r\n  */\n  try {\n    const workbook = XLSX.utils.book_new();\n    // Loop through each category group\n    dataWithCategories.forEach(({\n      name,\n      items\n    }) => {\n      const worksheet = XLSX.utils.json_to_sheet(items);\n      XLSX.utils.book_append_sheet(workbook, worksheet, name);\n    });\n    if (!isInsideMobileApp()) {\n      // Generate and download the Excel file\n      XLSX.writeFile(workbook, fileName + '.xlsx');\n    } else {\n      downloadExcelFileForAndroid(workbook, fileName);\n    }\n  } catch (error) {\n    console.error('Error generating and downloading Excel file:', error);\n  }\n}\nexport function downloadExcelFileForAndroid(workbook, fileName) {\n  window.wifyApp.showToastMessage('Downloading File...');\n  // Generate the binary string for the Excel file then create blob url\n  const binaryString = XLSX.write(workbook, {\n    bookType: 'xlsx',\n    type: 'binary'\n  });\n  const arrayBuffer = s2ab(binaryString);\n  const blob = new Blob([arrayBuffer], {\n    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n  });\n  const blobUrl = URL.createObjectURL(blob);\n\n  // Pass the blob url & base64 data to the mobile interface to handle file saving\n  getBase64FromBlobUrl(blobUrl, function (base64data) {\n    if (base64data) {\n      window.wifyApp.saveWorkbookFileInDevice(blobUrl, base64data, fileName + '.xlsx');\n    } else {\n      window.wifyApp.showToastMessage('Downloading Failed!');\n      console.error('Failed to fetch base64 data');\n    }\n  });\n}\nfunction getBase64FromBlobUrl(blobUrl, callback) {\n  fetch(blobUrl).then(response => response.blob()).then(blob => {\n    const reader = new FileReader();\n    reader.onloadend = function () {\n      const base64data = reader.result.split(',')[1]; // Extract base64 part of the data URL\n      callback(base64data);\n    };\n    reader.readAsDataURL(blob);\n  }).catch(error => {\n    console.error('Error fetching the blob:', error);\n    callback(null); // Return null if there's an error\n  });\n}\n\n// Convert binary string to ArrayBuffer\nfunction s2ab(s) {\n  const buf = new ArrayBuffer(s.length);\n  const view = new Uint8Array(buf);\n  for (let i = 0; i < s.length; i++) {\n    view[i] = s.charCodeAt(i) & 0xff;\n  }\n  return buf;\n}\n\n/* \r\n    Unit should be\r\n    1000 for thousand\r\n    100000 for lakhs\r\n    .. so on\r\n*/\nexport function convertValueToUnit(num, unit) {\n  if (isNaN(num)) {\n    return '';\n  }\n  const suffixes = {\n    K: 1000,\n    L: 100000,\n    Cr: 10000000\n    // 'Ar': 1000000000,\n    // 'Ab': 1000000000000\n  };\n  if (!suffixes.hasOwnProperty(unit)) {\n    return 'Invalid unit';\n  }\n  let divisor = suffixes[unit];\n  let formattedNum = (num / divisor).toFixed(2) + unit;\n  return formattedNum;\n}\nexport function getCreatedDateFilter(title, activeFilters, fromDate, toDate) {\n  let creation_date = activeFilters.hasOwnProperty('creation_srvc_req_date');\n  if (creation_date && (activeFilters === null || activeFilters === void 0 ? void 0 : activeFilters.creation_srvc_req_date)) {\n    var _activeFilters$creati, _activeFilters$creati2;\n    fromDate = activeFilters === null || activeFilters === void 0 ? void 0 : (_activeFilters$creati = activeFilters.creation_srvc_req_date) === null || _activeFilters$creati === void 0 ? void 0 : _activeFilters$creati[0];\n    toDate = activeFilters === null || activeFilters === void 0 ? void 0 : (_activeFilters$creati2 = activeFilters.creation_srvc_req_date) === null || _activeFilters$creati2 === void 0 ? void 0 : _activeFilters$creati2[1];\n  }\n  var currentDate = moment().local().format('MMM-DD-YYYY ') + moment().toDate().toLocaleTimeString([], {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n  return /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"h4 gx-text-capitalize gx-ml-3 gx-mb-4\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 13\n    }\n  }, \" \", title, \" \"), creation_date ? /*#__PURE__*/React.createElement(\"small\", {\n    className: \"gx-text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 17\n    }\n  }, \"( Created Between \", convertUTCToDisplayTime(fromDate, true), ' ', \"To \", convertUTCToDisplayTime(toDate, true), \" )\") : currentDate ? /*#__PURE__*/React.createElement(\"small\", {\n    className: \"gx-text-muted\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 17\n    }\n  }, \"( Created between\", ' ', convertUTCToDisplayTime(currentDate, true), \" To\", ' ', getCurrentDateAndTimeFrDisplay(), \" )\") : []);\n}\nexport function getParamsObjFromUrl({\n  paramName = ''\n}) {\n  const urlSearch = document.location.href;\n  const [baseUrl, _query] = urlSearch.split('?');\n  let _queryRes = {};\n  if (paramName && _query && _query.includes(paramName)) {\n    _queryRes = convertNestedQueryUrlToObj({\n      url: _query\n    });\n  }\n  return _queryRes;\n}\nexport function convertNestedQueryUrlToObj({\n  url = false\n}) {\n  if (!url) {\n    console.log('convertNestedQueryUrlToObj :: No url given');\n    return {};\n  }\n  const params = new URLSearchParams(url);\n  const obj = {};\n  for (const [key, value] of params.entries()) {\n    const decodedKey = decodeURIComponent(key);\n    const decodedValue = decodeURIComponent(value);\n    try {\n      const nestedObj = JSON.parse(decodedValue);\n      obj[decodedKey] = nestedObj;\n    } catch (error) {\n      obj[decodedKey] = decodedValue;\n    }\n  }\n  return obj;\n}\nexport function updateQueryParams({\n  query = ''\n}) {\n  const urlSearch = document.location.href;\n  const [baseUrl, _query] = urlSearch.split('?');\n  if (_query) {\n    const newUrl = `${urlSearch}&${query}`;\n    window.history.replaceState({}, '', newUrl);\n  } else {\n    const newUrl = `${window.location.pathname}?${query}`;\n    window.history.replaceState({}, '', newUrl);\n  }\n}\nexport function stringifyQueryParams({\n  queryObject = {}\n}) {\n  const urlSearch = new URLSearchParams();\n  for (const key in queryObject) {\n    if (Object.hasOwnProperty.call(queryObject, key)) {\n      let _value = queryObject[key];\n      if (typeof _value == 'object') {\n        _value = JSON.stringify(_value);\n      }\n      urlSearch.append(key, _value);\n    }\n  }\n  return urlSearch.toString();\n}\nexport const showCreateOrUpdateSuccessMessage = (top_ = 200, duration_ = 2, message_ = 'Submitted successfully') => {\n  message.config({\n    top: top_,\n    // Set the desired distance from the top of the viewport\n    duration: duration_ // Set the duration in seconds for the message to be displayed\n  });\n  message.success(message_);\n};\nexport const getCustomFileFieldsFilter = srvcCustomFieldsJson => {\n  let customFileFieldsMeta = [];\n  let customFileFields = decodeFileSectionsFrmJson(srvcCustomFieldsJson);\n  if (customFileFields) {\n    customFileFields.forEach(singleCustomFileField => {\n      let customFileFieldObj = {\n        key: singleCustomFileField.key + '_uploaded',\n        label: singleCustomFileField.title + ' Uploaded',\n        placeholder: 'Select..',\n        widget: 'select',\n        widgetProps: {\n          // \"mode\":\"multiple\" ,\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        },\n        options: [{\n          label: 'Any',\n          value: '-1',\n          color: 'gx-bg-grey'\n        }, {\n          label: 'Yes',\n          value: 'Yes'\n        }, {\n          label: 'No',\n          value: 'No'\n        }]\n      };\n      customFileFieldsMeta.push(customFileFieldObj);\n    });\n  }\n  return customFileFieldsMeta;\n};\nconst priorities = [\n//urgent,high,normal,low, no priority\n{\n  value: 'Urgent',\n  label: 'Urgent',\n  color: '#f5222d'\n}, {\n  value: 'High',\n  label: 'High',\n  color: '#fadb14'\n}, {\n  value: 'Normal',\n  label: 'Normal',\n  color: '#13c2c2'\n}, {\n  value: 'Low',\n  label: 'Low',\n  color: '#52c41a'\n}, {\n  value: 'None',\n  label: 'None',\n  color: '#8c8c8c'\n}];\nexport const BILLING_TYPE = [{\n  value: 'line_item',\n  label: 'Line item'\n}, {\n  value: 'manday',\n  label: 'Manday'\n}, {\n  value: 'hybrid',\n  label: 'Hybrid'\n}, {\n  value: 'foc',\n  label: 'FOC'\n}];\nexport const getSelectOptionsFrZeroToHundred = (startVal = 1) => {\n  let returnValue = [];\n  for (var i = startVal; i <= 100; i++) {\n    returnValue.push({\n      value: i,\n      label: i\n    });\n  }\n  return returnValue;\n};\nexport const BILLING_DISCOUNT_TYPE = [{\n  value: 'percentage',\n  label: 'Percentage'\n}, {\n  value: 'value',\n  label: 'Value'\n}];\nexport const BILLING_APPROVER_TYPE = [{\n  value: 'authority_based',\n  label: 'Authority based'\n}, {\n  value: 'static_user',\n  label: 'Static User'\n}];\nexport const PROJECT_BASED_SERVICE_TYPE = 'project_based';\nexport const getOptionsFrNatureOfServiceType = () => {\n  return [{\n    label: 'Task based',\n    value: 'task_based'\n  }, {\n    label: 'Project based',\n    value: PROJECT_BASED_SERVICE_TYPE\n  }];\n};\nexport const getOptionsFrDefaultModeOfRating = () => {\n  return [{\n    label: 'Taskwise',\n    value: 'task_wise'\n  }, {\n    label: 'Daywise',\n    value: 'day_wise'\n  }];\n};\nexport const getValueDataFrmFormMeta = (fieldsMeta, form_data) => {\n  //Remove dublicate key from json\n  fieldsMeta = fieldsMeta.filter((listItem, index, self) => self.map(itm => itm.key).indexOf(listItem.key) === index);\n  let valueData = {};\n  fieldsMeta.forEach(singleFieldMeta => {\n    let key = singleFieldMeta === null || singleFieldMeta === void 0 ? void 0 : singleFieldMeta.key;\n    let value = form_data === null || form_data === void 0 ? void 0 : form_data[key];\n    if (form_data === null || form_data === void 0 ? void 0 : form_data[key]) {\n      var _singleFieldMeta$widg;\n      delete singleFieldMeta['colSpan'];\n      if (singleFieldMeta.widget == 'date-picker') {\n        value = moment.utc(value).format('MMM Do YYYY');\n      } else if (singleFieldMeta.widget == 'select') {\n        value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\n      } else if (((_singleFieldMeta$widg = singleFieldMeta.widget) === null || _singleFieldMeta$widg === void 0 ? void 0 : _singleFieldMeta$widg.displayName) == 'Rate') {\n        value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\n      } else if (singleFieldMeta.widget == 'radio-group') {\n        value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\n      } else if (singleFieldMeta.widget == 'checkbox-group') {\n        value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\n      } else if (singleFieldMeta.widget) {\n        let currValue = form_data[key]; // check if label in value\n        if (currValue.label) {\n          value = currValue.label;\n        }\n      }\n    }\n    valueData[key] = value;\n  });\n  return valueData;\n};\nexport const parseFormulaToString = (formula, nameToIdMapping = undefined, data) => {\n  // debugger;\n  let matches = formula.match(/{[^{}]+}/g);\n  matches.forEach(matchString => {\n    let word = matchString.substring(1, matchString.length - 1);\n    let key = nameToIdMapping[word];\n    let fieldValue = data[key] || 'N/A';\n    formula = formula.replace(`{${word}}`, fieldValue);\n  });\n  return formula;\n};\nexport const parseFormulaToValue = (formula, nameToIdMapping = undefined, data, isSrvcReqLocked = true) => {\n  let matches = formula.match(/{[^{}]+}/g);\n  matches.forEach(matchString => {\n    let word = matchString.substring(1, matchString.length - 1);\n    let key = nameToIdMapping[word];\n    let fieldValue = key in data ? data[key] : 'undefined';\n    formula = formula.replace(`{${word}}`, fieldValue);\n  });\n  let result = 0;\n  try {\n    result = eval(formula);\n    if (!isSrvcReqLocked) {\n      result = parseFloat(result).toFixed(2);\n    }\n  } catch (error) {\n    console.log('parseFormulaToValue error', error);\n  }\n  return result;\n};\nexport const getStringToArray = data => {\n  let value = [];\n  if (data != undefined) {\n    var _value2;\n    if (Object.keys(data).length == 0) {\n      value = value;\n    } else if (typeof data === 'string') {\n      value = data.split(',');\n    } else {\n      value = data;\n    }\n    value = (_value2 = value) === null || _value2 === void 0 ? void 0 : _value2.filter(singlePin => singlePin != '');\n  }\n  return value;\n};\n\n/** FROM DB */\nexport const getStatusListForOptions = statuses_db => {\n  let decodeStatuses = [];\n  statuses_db.map(status => {\n    decodeStatuses.push({\n      label: status.title,\n      value: status.key,\n      color: status.color\n    });\n  });\n  return decodeStatuses;\n};\nexport const getCenterLocFrIndMap = () => {\n  return {\n    lat: 21.7372075,\n    lng: 79.8332191\n  };\n};\nconst getColorCodeFrStatusCategory = category => {\n  var colorFrCategory = {\n    ACTIVE: '#f44336',\n    DONE: '#009688',\n    CLOSED: '#4caf50'\n  };\n  return colorFrCategory[category] ? colorFrCategory[category] : '#e1e1e1';\n};\nconst getTextColorFrPriority = priority => {\n  var colorFrPriority = {\n    Urgent: 'gx-text-red',\n    High: 'gx-text-yellow',\n    Normal: 'gx-text-cyan',\n    Low: 'gx-text-green',\n    None: 'gx-text-grey'\n  };\n  return colorFrPriority[priority] ? colorFrPriority[priority] : '';\n};\nconst getRandomIconByColor = () => {\n  var arrayOfColors = ['gx-text-green', 'gx-text-orange', 'gx-text-purple', 'gx-text-amber', 'gx-text-light-blue', 'gx-text-blue', 'gx-text-cyan', 'gx-text-sepia', 'gx-text-pink'];\n  return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\n};\nconst getRandomBgColor = () => {\n  var arrayOfColors = ['gx-bg-green', 'gx-bg-orange', 'gx-bg-purple', 'gx-bg-amber', 'gx-bg-light-blue', 'gx-bg-blue', 'gx-bg-cyan', 'gx-bg-sepia', 'gx-bg-pink'];\n  return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\n};\nexport const getRandomTagBgColor = () => {\n  var arrayOfColors = ['magenta', 'red', 'volcano', 'orange', 'gold', 'lime', 'green', 'cyan', 'blue', 'geekblue', 'purple'];\n  return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\n};\nconst generateRandomKey = (length = 8) => {\n  var result = [];\n  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  var charactersLength = characters.length;\n  for (var i = 0; i < length; i++) {\n    result.push(characters.charAt(Math.floor(Math.random() * charactersLength)));\n  }\n  return result.join('');\n};\nconst getAnyObjectFrFilter = (text = 'Any') => {\n  return {\n    title: text,\n    label: text,\n    value: '-1',\n    color: '#E1E1E1'\n  };\n};\nconst getEmptyObjectFrFilter = (text = 'Empty') => {\n  return {\n    title: text,\n    label: text,\n    value: '-2',\n    color: '#E1E1E1'\n  };\n};\nexport const getPriorityObjectFrFilter = () => {\n  return {\n    key: 'priority',\n    label: 'Priority',\n    placeholder: 'Select..',\n    widget: 'select',\n    quick: true,\n    widgetProps: {\n      // make sure to add mode as multiple when its for quick\n      mode: 'multiple',\n      optionFilterProp: 'children',\n      showSearch: true\n    },\n    options: priorities\n  };\n};\nexport const getSpareOptions = () => {\n  return [{\n    label: 'Repairable',\n    value: 'repairable'\n  }, {\n    label: 'Non Repairable',\n    value: 'non_repairable'\n  }];\n};\nexport const getInventoryTypeList = () => {\n  return [{\n    label: 'Product Serialized',\n    value: 'serialized-product'\n  }, {\n    label: 'Product Non-Serialized',\n    value: 'product'\n  }, {\n    label: 'Spare Serialized',\n    value: 'serialized-spare'\n  }, {\n    label: 'Spare Non-Serialized',\n    value: 'spare'\n  }];\n};\nexport const getInventoryType = inventoryType => {\n  const types = {\n    product: {\n      stock_item_type: 'non-serialized',\n      stock_type: 'product',\n      label: 'Product'\n    },\n    'serialized-product': {\n      stock_item_type: 'serialized',\n      stock_type: 'product',\n      label: 'Product'\n    },\n    spare: {\n      stock_item_type: 'non-serialized',\n      stock_type: 'spare',\n      label: 'Spare'\n    },\n    'serialized-spare': {\n      stock_item_type: 'serialized',\n      stock_type: 'spare',\n      label: 'Spare'\n    }\n  };\n  return types[inventoryType] || {};\n};\nexport const getSolidUnitOptions = () => {\n  return [{\n    label: 'gram',\n    value: 'gram'\n  }, {\n    label: 'kilogram',\n    value: 'kilogram'\n  }, {\n    label: 'tonne',\n    value: 'tonne'\n  }];\n};\nexport const getLiquidUnitOptions = () => {\n  return [{\n    label: 'litre',\n    value: 'litre'\n  }, {\n    label: 'millilitre',\n    value: 'millilitre'\n  }];\n};\nexport const getUnitOptions = () => {\n  return [{\n    label: 'Quantity',\n    shortLabel: 'Quantity',\n    value: 'quantity'\n  }, {\n    label: 'Gram',\n    shortLabel: 'g',\n    value: 'gram'\n  }, {\n    label: 'Kilogram',\n    shortLabel: 'kg',\n    value: 'kilogram'\n  }, {\n    label: 'Tonne',\n    shortLabel: 't',\n    value: 'tonne'\n  }, {\n    label: 'Litre',\n    shortLabel: 'l',\n    value: 'litre'\n  }, {\n    label: 'Millilitre',\n    shortLabel: 'ml',\n    value: 'millilitre'\n  }];\n};\nexport const getIsDeletedFrFilter = () => {\n  return {\n    key: 'is_deleted',\n    label: 'Show Deleted',\n    placeholder: 'Select..',\n    widget: 'select',\n    quick: true,\n    noAny: true,\n    widgetProps: {\n      // make sure to add mode as multiple when its for quick\n      mode: 'multiple',\n      optionFilterProp: 'children'\n    },\n    options: [{\n      label: 'Yes',\n      value: true\n    }, {\n      label: 'No',\n      value: false\n    }]\n  };\n};\nexport const getShowPincodeFrFilter = () => {\n  return {\n    key: 'is_pincode',\n    label: 'Pincode Available',\n    placeholder: 'Select..',\n    widget: 'select',\n    quick: true,\n    widgetProps: {\n      // make sure to add mode as multiple when its for quick\n      mode: 'multiple',\n      optionFilterProp: 'children'\n    },\n    options: [{\n      label: 'Any',\n      value: '-1',\n      color: 'gx-bg-grey'\n    }, {\n      label: 'Yes',\n      value: true\n    }, {\n      label: 'No',\n      value: false\n    }]\n  };\n};\nexport const getAgingFiltersFrBrand = () => {\n  return [{\n    key: 'stages',\n    label: 'Ageing - Status',\n    widget: 'select',\n    options: []\n  }, {\n    key: 'days_spent',\n    label: 'Ageing - Days Spend',\n    widget: 'select',\n    widgetProps: {\n      mode: 'multiple',\n      optionFilterProp: 'children'\n    },\n    options: [],\n    noAny: true\n  }, {\n    key: 'show_all',\n    label: 'Ageing - Show All',\n    widget: 'select',\n    options: [{\n      value: true,\n      label: 'Yes'\n    }, {\n      value: false,\n      label: 'No'\n    }],\n    noAny: true\n  }];\n};\nexport const getNoOfTasksObjectFrFilter = () => {\n  let numberOfTasks = 5;\n  let possibleTasksOptions = [];\n  possibleTasksOptions = getOptionsListFrPossibleNumbers(numberOfTasks, number => ({\n    color: getColorInBetweenByPercentage([255, 72, 0],\n    // Min color\n    [0, 250, 156],\n    // Max color\n    number / numberOfTasks)\n  })).filter(item => item.value != 0);\n  let taskOptionGreaterThanFiveObj = {\n    label: '> 5',\n    value: '>5',\n    color: 'rgb(255,0,0)'\n  };\n  possibleTasksOptions.push(taskOptionGreaterThanFiveObj);\n  return {\n    key: 'no_of_tasks',\n    label: 'No Of Tasks',\n    placeholder: 'Select..',\n    widget: 'select',\n    quick: true,\n    widgetProps: {\n      // make sure to add mode as multiple when its for quick\n      mode: 'multiple',\n      optionFilterProp: 'children',\n      showSearch: true\n    },\n    options: possibleTasksOptions\n  };\n};\nconst convertDateFieldsToMoments = (form_data, fieldsMeta = undefined) => {\n  let returnData = form_data;\n  if (returnData != undefined) {\n    Object.keys(form_data).map(key => {\n      var fieldValue = form_data[key];\n      if (fieldsMeta) {\n        let matchingMeta = fieldsMeta.filter(fieldMeta => fieldMeta.key == key);\n        if (matchingMeta.length > 0) {\n          let fieldMeta = matchingMeta[0];\n          if (fieldMeta.widget && typeof fieldMeta.widget === 'function' && fieldMeta.widget.name == DatePicker.RangePicker.name) {\n            var _fieldMeta$widgetProp;\n            //\n            if (isArray(fieldValue) && fieldValue.length == 2 && ((_fieldMeta$widgetProp = fieldMeta.widgetProps) === null || _fieldMeta$widgetProp === void 0 ? void 0 : _fieldMeta$widgetProp.ranges)) {\n              returnData[key] = [moment(fieldValue[0], true), moment(fieldValue[1], true)];\n            }\n          } else if (fieldMeta.widget == 'date-picker') {\n            // console.log('value',value);\n            if (fieldValue && fieldValue != '') {\n              var date = moment(fieldValue, true);\n              if (date.isValid()) {\n                // console.log('Valid date field converted to moment',key,date)\n                returnData[key] = date;\n              } else {\n                returnData[key] = moment();\n              }\n            } else {\n              returnData[key] = undefined;\n            }\n          } else if (fieldMeta.cust_widget === 'TimePicker' || typeof fieldMeta.widget === 'function' && fieldMeta.widget.name === 'TimePicker') {\n            if (!fieldValue) returnData[key] = null;else {\n              var _parsedTime;\n              let parsedTime;\n              if (moment.isMoment(fieldValue)) {\n                parsedTime = fieldValue;\n              } else {\n                // Try multiple time formats including ones with spaces\n                parsedTime = moment(fieldValue, ['h:mm A',\n                // 2:30 PM\n                'hh:mm A',\n                // 02:30 PM\n                'h:mm  A',\n                // 2:30  PM (double space)\n                'hh:mm  A',\n                // 02:30  PM (double space)\n                'h:mmA',\n                // 2:30PM (no space)\n                'hh:mmA',\n                // 02:30PM (no space)\n                'H:mm',\n                // 14:30 (24-hour)\n                'HH:mm',\n                // 14:30 (24-hour)\n                moment.ISO_8601], true);\n              }\n              returnData[key] = ((_parsedTime = parsedTime) === null || _parsedTime === void 0 ? void 0 : _parsedTime.isValid()) ? parsedTime.format('hh:mm A') : null;\n            }\n          }\n        }\n      } else {\n        // Legacy\n        if (typeof fieldValue === 'string') {\n          var date = moment(fieldValue, true);\n          if (date.isValid()) {\n            // console.log('Valid date field converted to moment',key,date)\n            returnData[key] = date;\n          }\n        }\n      }\n    });\n  }\n  return returnData;\n};\nconst isTimePassed = sql_timestamp => {\n  // console.log(sql_timestamp);\n  let returnData = false;\n  if (sql_timestamp != undefined) {\n    var momentUtc = moment.utc(sql_timestamp);\n    // console.log(moment.utc(),moment.utc(sql_timestamp));\n    if (momentUtc.valueOf() < moment().utc().valueOf()) {\n      returnData = true;\n    }\n  }\n  return returnData;\n};\nexport const convertMomentToLocalDateString = moment_date => {\n  return moment_date === null || moment_date === void 0 ? void 0 : moment_date.local().format('MMM-DD-YYYY ');\n};\nexport const getCustomPrefixNameFrUploadingFiles = () => {\n  let userName = ConfigHelpers.getFullUserName();\n  let currentDate = convertMomentToDateString(moment.utc());\n  return `${userName}_${currentDate}`;\n};\nexport const convertMomentToDateString = moment_date => {\n  return moment_date === null || moment_date === void 0 ? void 0 : moment_date.local().format('YYYY-MM-DD');\n};\nexport const getCurrentDay = () => {\n  var date = moment();\n  return date.local().format('YYYY-MM-DD');\n};\nexport const getCurrentDateAndTimeFrDisplay = () => {\n  var date = moment();\n  return date.local().format('MMM-DD-YYYY ') + date.toDate().toLocaleTimeString([], {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nconst convertUTCToDisplayTime = (sql_timestamp, just_date, just_time) => {\n  let returnData = '';\n  if (sql_timestamp != undefined) {\n    if (typeof sql_timestamp === 'string') {\n      var date = moment.utc(sql_timestamp);\n      if (date.isValid()) {\n        if (just_date) {\n          returnData = date.local().format('MMM-DD-YYYY ');\n        } else if (just_time) {\n          returnData = date.local().format('h:mm A');\n        } else {\n          returnData = date.local().format('MMM-DD-YYYY ') + date.toDate().toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n      }\n    }\n  }\n  return returnData;\n};\nconst getTouchedFieldsValueInForm = (form_data, formInstance, touchFieldsKeys = []) => {\n  let returnFields = {};\n  if (formInstance) {\n    let fieldKeys = Object.keys(form_data);\n    fieldKeys.forEach(singleKey => {\n      if (formInstance.isFieldTouched(singleKey) || touchFieldsKeys.includes(singleKey)) {\n        returnFields[singleKey] = form_data[singleKey];\n        const fieldValue = form_data[singleKey];\n        if (fieldValue === undefined) {\n          // If the field value is undefined, check if it's a touched field\n          if (touchFieldsKeys.includes(singleKey)) {\n            returnFields[singleKey] = null; // Explicitly set to null to ensure it's included in the update\n          }\n        }\n      }\n    });\n  } else {\n    returnFields = form_data;\n    console.log('getTouchedFieldsValueInForm error', 'Form instance undefined, returning all fields');\n  }\n  return returnFields;\n};\nfunction durationAsString(start) {\n  const duration = moment.duration(moment.utc().diff(moment.utc(start)));\n\n  //Get Days\n  const days = Math.floor(duration.asDays()); // .asDays returns float but we are interested in full days only\n  const daysFormatted = days ? `${days}d ` : ''; // if no full days then do not display it at all\n\n  //Get Hours\n  const hours = duration.hours();\n  const hoursFormatted = hours > 0 ? `${hours}h ` : '';\n\n  //Get Minutes\n  const minutes = duration.minutes();\n  const minutesFormatted = `${minutes}m`;\n  return [daysFormatted, hoursFormatted, minutesFormatted].join('');\n}\n\n// console.log(generateRandomKey(5));\nfunction getLinktoObject(url, params) {\n  let search = '';\n  let urlSearch = new URLSearchParams();\n  for (const key in params) {\n    if (Object.hasOwnProperty.call(params, key)) {\n      const element = params[key];\n      let value = element;\n      if (typeof element == 'object') {\n        value = JSON.stringify(element);\n      }\n      urlSearch.append(key, value);\n    }\n  }\n  return {\n    pathname: url,\n    search: urlSearch.toString()\n  };\n}\nfunction getGeneralFileSection(required) {\n  return {\n    key: 'general',\n    title: 'Attachments',\n    required\n  };\n}\nfunction hasAnyFileChanged(newFilesBySection, oldFilesBySection) {\n  let hasChanged = false;\n  // debugger;\n  if (oldFilesBySection == undefined && newFilesBySection) {\n    hasChanged = true;\n  } else {\n    let sections = Object.keys(newFilesBySection);\n    sections.map(singleKey => {\n      let newFilesFrSection = newFilesBySection[singleKey];\n      let oldFilesFrSection = oldFilesBySection[singleKey];\n      if (oldFilesFrSection == undefined || newFilesFrSection == undefined) {\n        hasChanged = true;\n      } else {\n        oldFilesFrSection.map(oldFile => {\n          if (!newFilesFrSection.includes(oldFile)) {\n            hasChanged = true;\n          }\n        });\n        newFilesFrSection.map(newFile => {\n          if (!oldFilesFrSection.includes(newFile)) {\n            hasChanged = true;\n          }\n        });\n      }\n    });\n  }\n  return hasChanged;\n}\nexport const ExcelDateToJSDate = serial => {\n  var isANumber = isNaN(serial) === false;\n  if (isANumber) {\n    var utc_days = Math.floor(serial - 25569);\n    var utc_value = utc_days * 86400;\n    var date_info = new Date(utc_value * 1000);\n    var fractional_day = serial - Math.floor(serial) + 0.0000001;\n    var total_seconds = Math.floor(86400 * fractional_day);\n    var seconds = total_seconds % 60;\n    total_seconds -= seconds;\n    var hours = Math.floor(total_seconds / (60 * 60));\n    var minutes = Math.floor(total_seconds / 60) % 60;\n    return new Date(date_info.getFullYear(), date_info.getMonth(), date_info.getDate(), hours, minutes, seconds);\n  } else {\n    // - / and 0 to 9\n    var regexp = /[^0-9-\\/]+/gi;\n    var cleanDateStr = serial === null || serial === void 0 ? void 0 : serial.replace(regexp, '');\n    var momentDate = moment(cleanDateStr, 'YYYY-MM-DD', true);\n    return momentDate.isValid() ? Date.parse(cleanDateStr) : undefined;\n  }\n};\nexport const decodeAntdFormErrorsToString = errObject => {\n  let finalString = [];\n  errObject.errorFields.map(({\n    errors\n  }) => {\n    errors.map(singleError => {\n      finalString.push(singleError);\n    });\n  });\n  return finalString.join(',');\n};\nexport const getLabelFrmOptionsValue = (options, value) => {\n  let returnLabel = value;\n  if (Array.isArray(options)) {\n    if (Array.isArray(value)) {\n      // If the value is an array, map over it and get the corresponding labels\n      returnLabel = value.map(singleValue => {\n        const matchingOption = options.find(singleOption => singleOption.value === singleValue);\n        return matchingOption ? matchingOption.label : singleValue;\n      }).join(', ');\n    } else {\n      // If the value is not an array, get the label for the single value\n      options.map(singleOption => {\n        if (singleOption.value == value) {\n          returnLabel = singleOption.label;\n        }\n      });\n    }\n  }\n  return returnLabel;\n};\nconst addDaysToDate = function (date, days) {\n  date.setDate(date.getDate() + days);\n  return date;\n};\nexport const getDayName = (dateStr, locale) => {\n  var date = new Date(dateStr);\n  return date.toLocaleDateString(locale, {\n    weekday: 'long'\n  });\n};\nexport const getDaysBetweenMoments = (startDate, stopDate, inclusive = false) => {\n  return getDaysBetweenDates(startDate === null || startDate === void 0 ? void 0 : startDate.toDate(), stopDate === null || stopDate === void 0 ? void 0 : stopDate.toDate(), inclusive);\n};\nexport const getDaysBetweenDates = (startDate, stopDate, inclusive = false) => {\n  console.log('startDate', startDate, stopDate);\n  var dateArray = new Array();\n  var currentDate = startDate;\n  while (currentDate <= stopDate) {\n    dateArray.push(convertMomentToDateString(moment(currentDate)));\n    currentDate = addDaysToDate(currentDate, 1);\n  }\n  if (!inclusive && dateArray.length > 0) {\n    dateArray.shift();\n    dateArray.pop();\n  }\n  return dateArray;\n};\nexport const getPresetRangesForFutureSpans = () => {\n  return {\n    Today: [moment(), moment()],\n    Today: [moment(), moment()],\n    Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],\n    'Next 2 days': [moment().add(1, 'days'), moment().add(2, 'days')],\n    'Next 3 days': [moment().add(1, 'days'), moment().add(3, 'days')],\n    'Next 4 days': [moment().add(1, 'days'), moment().add(4, 'days')],\n    'Next 5 days': [moment().add(1, 'days'), moment().add(5, 'days')],\n    'Next 6 days': [moment().add(1, 'days'), moment().add(6, 'days')],\n    'Next 7 days': [moment().add(1, 'days'), moment().add(7, 'days')],\n    'Next 2 weeks': [moment().add(1, 'days'), moment().add(14, 'days')],\n    'Next month': [moment().add(1, 'days'), moment().add(31, 'days')]\n  };\n};\nexport const getPresetRangesForRangeDatePicker = () => {\n  return {\n    Today: [moment(), moment()],\n    Yesterday: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],\n    'Last 7 days': [moment().subtract(7, 'days'), moment()],\n    'Last week': [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')],\n    'Month to date': [moment().startOf('month'), moment()],\n    'Previous Month': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],\n    'Year to Date': [moment().startOf('year'), moment()]\n  };\n};\nexport const setPageTitleAndFavIcon = (title, icon) => {\n  const favicon = document.getElementById('favicon');\n  if (icon && icon != '') {\n    favicon.href = icon;\n  }\n  const html_title = document.getElementById('html_title');\n  if (title && title != '') {\n    html_title.innerHTML = title;\n  }\n};\nexport const getSearchInLinkAsObject = () => {\n  let searchParams = new URLSearchParams(document.location.search.substring(1));\n  let paramsObject = {};\n  searchParams.forEach((value, key) => paramsObject[key] = value);\n  // message.info(JSON.stringify(paramsObject));\n  return paramsObject;\n};\n\n// http://jsfiddle.net/vksn3yLL/\nexport const getColorInBetweenByPercentage = (color1, color2, percentage) => {\n  var p = percentage;\n  var w = p * 2 - 1;\n  var w1 = (w / 1 + 1) / 2;\n  var w2 = 1 - w1;\n  var rgb = [Math.round(color1[0] * w1 + color2[0] * w2), Math.round(color1[1] * w1 + color2[1] * w2), Math.round(color1[2] * w1 + color2[2] * w2)];\n  return 'rgb(' + rgb.join() + ')';\n};\nexport const getOptionsListFrPossibleNumbers = (maxNumber, customParamsCallback) => {\n  let returnOptionsArray = [];\n  if (maxNumber) {\n    let optionsArray = Array.from(Array(maxNumber + 1).keys());\n    optionsArray = optionsArray.map((value, index) => {\n      let customParams = {};\n      if (customParamsCallback) {\n        customParams = customParamsCallback(index);\n      }\n      return {\n        label: '' + index,\n        value: index,\n        ...customParams\n      };\n    });\n    // console.log(\"optionsArray\",optionsArray)\n    returnOptionsArray.push(...optionsArray);\n  }\n  return returnOptionsArray;\n};\nexport const getViewModeMetaFrFormMeta = (fieldsMeta, form_data) => {\n  //Remove dublicate key from json\n  fieldsMeta = fieldsMeta.filter((listItem, index, self) => self.map(itm => itm.key).indexOf(listItem.key) === index);\n  let formMetaFrDisplay = [];\n  fieldsMeta.forEach(singleFieldMeta => {\n    let key = singleFieldMeta.key;\n    if (form_data[key]) {\n      var _singleFieldMeta$widg2, _singleFieldMeta$widg3;\n      delete singleFieldMeta['colSpan'];\n      if (singleFieldMeta.widget == 'date-picker') {\n        singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(\"div\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 21\n          }\n        }, moment.utc(value).format('MMM Do YYYY'));\n      } else if (singleFieldMeta.widget == 'select') {\n        singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1243,\n            columnNumber: 21\n          }\n        }, getLabelFrmOptionsValue(singleFieldMeta.options, value));\n      } else if (((_singleFieldMeta$widg2 = singleFieldMeta.widget) === null || _singleFieldMeta$widg2 === void 0 ? void 0 : _singleFieldMeta$widg2.displayName) == 'Rate') {\n        singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(Rate, {\n          value: (singleFieldMeta.options, value),\n          disabled: true,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1252,\n            columnNumber: 21\n          }\n        });\n      } else if (singleFieldMeta.cust_widget == 'TimePicker' || ((_singleFieldMeta$widg3 = singleFieldMeta.widget) === null || _singleFieldMeta$widg3 === void 0 ? void 0 : _singleFieldMeta$widg3.name) == 'TimePicker') {\n        singleFieldMeta.renderView = value => {\n          // Always display TimePicker values in hh:mm A format\n          let displayText = '';\n          if (moment.isMoment(value)) {\n            displayText = value.format('hh:mm A');\n          } else if (value && typeof value === 'string') {\n            const parsedTime = moment(value, ['h:mm A', 'hh:mm A', 'H:mm', 'HH:mm'], true);\n            displayText = parsedTime.isValid() ? parsedTime.format('hh:mm A') : value;\n          }\n          return /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 28\n            }\n          }, displayText);\n        };\n      } else if (singleFieldMeta.widget == 'radio-group') {\n        singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1268,\n            columnNumber: 21\n          }\n        }, getLabelFrmOptionsValue(singleFieldMeta.options, value));\n      } else if (singleFieldMeta.widget == 'checkbox-group') {\n        singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1277,\n            columnNumber: 21\n          }\n        }, getLabelFrmOptionsValue(singleFieldMeta.options, value));\n      } else if (singleFieldMeta.widget) {\n        let value = form_data[key]; // check if label in value\n        if (value.label) {\n          singleFieldMeta.renderView = value => /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 25\n            }\n          }, value.label);\n        }\n      } else if (singleFieldMeta.key == 'comment') {\n        singleFieldMeta.renderView = value => {\n          return value;\n        };\n      }\n      // key exists in form_data so this field needs to be displayed\n      formMetaFrDisplay.push(singleFieldMeta);\n    }\n  });\n  return formMetaFrDisplay;\n};\nexport const generateUUID = () => {\n  return uuidv4();\n};\nexport const NoData = () => {\n  return /*#__PURE__*/React.createElement(Empty, {\n    \"data-testid\": \"no-data\",\n    image: Empty.PRESENTED_IMAGE_SIMPLE,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1309,\n      columnNumber: 12\n    }\n  });\n};\nexport const getRandomHexBgColor = () => {\n  var arrayOfColors = ['#008000', '#FFA500', '#800080', '#FFBF00', '#ADD8E6', '#0000FF', '#00FFFF', '#704214', '#FFC0CB'];\n  return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\n};\nexport const getColorCodeFrReqDate = category => {\n  var colorFrReqDate = {\n    TODAY: '#008080',\n    UPCOMING: '#FFA500',\n    OVERDUE: '#FF0000',\n    UNSPECIFIED: '#808080'\n  };\n  return colorFrReqDate[category] ? colorFrReqDate[category] : '#e1e1e1';\n};\nexport const isAndroidApp = () => {\n  var _navigator;\n  const user_agent = (_navigator = navigator) === null || _navigator === void 0 ? void 0 : _navigator.userAgent;\n  console.log('user_agent', user_agent);\n  return user_agent.includes('||TMS_APP');\n};\nexport const areArraysSame = (a1, a2) => {\n  /* WARNING: arrays must not contain {objects} or behavior may be undefined */\n  return JSON.stringify(a1) == JSON.stringify(a2);\n};\nexport const handleClearSelect = (value, formRef, key) => {\n  let fieldKey = {};\n  fieldKey[key] = '';\n  if (value == undefined) {\n    var _formRef$current;\n    if (formRef === null || formRef === void 0 ? void 0 : (_formRef$current = formRef.current) === null || _formRef$current === void 0 ? void 0 : _formRef$current.setFieldsValue) {\n      formRef.current.setFieldsValue(fieldKey);\n    }\n  }\n};\nexport const setRemarkFieldAsNoRemarkIfEmpty = (prefillFormData, formRef) => {\n  if (formRef === null || formRef === void 0 ? void 0 : formRef.current) {\n    var _formRef$current2;\n    const remark = formRef === null || formRef === void 0 ? void 0 : (_formRef$current2 = formRef.current) === null || _formRef$current2 === void 0 ? void 0 : _formRef$current2.getFieldValue('remarks');\n    if ((prefillFormData === null || prefillFormData === void 0 ? void 0 : prefillFormData.remarks) == undefined && remark == undefined) {\n      formRef.current.setFieldsValue({\n        remarks: 'No remarks'\n      });\n    }\n  } else {\n    setTimeout(() => {\n      setRemarkFieldAsNoRemarkIfEmpty(prefillFormData);\n    }, 100);\n  }\n};\nexport const getCustomVarMetaFrTemplate = (selected_custom_fields_meta, full_prefix, custom_fields_meta, filledCustomVars, formRef, refresh) => {\n  if ((selected_custom_fields_meta === null || selected_custom_fields_meta === void 0 ? void 0 : selected_custom_fields_meta.length) > 0) {\n    selected_custom_fields_meta.forEach(single_selected_custom_fields_meta_ => {\n      var _formRef$current3;\n      let copy_field = {\n        ...single_selected_custom_fields_meta_\n      };\n      copy_field.key = full_prefix + '_' + single_selected_custom_fields_meta_.key;\n      copy_field['onChange'] = () => refresh();\n      custom_fields_meta.push(copy_field);\n      filledCustomVars[`%${single_selected_custom_fields_meta_.key}%`] = formRef === null || formRef === void 0 ? void 0 : (_formRef$current3 = formRef.current) === null || _formRef$current3 === void 0 ? void 0 : _formRef$current3.getFieldValue(copy_field.key);\n    });\n  }\n};\n\n//handles null value for date filters\nexport const handleFilterClearDateIfNull = filters => {\n  let keys = Object.keys(filters);\n  for (let i = 0; i < keys.length; i++) {\n    let filterKey = keys[i];\n    if (filters[filterKey] == null || filters[filterKey] == undefined) {\n      delete filters[filterKey];\n    }\n  }\n  return filters;\n};\nexport function commaSeparatedNumberForDashboard(number) {\n  try {\n    if (number >= 0) {\n      const parts = number === null || number === void 0 ? void 0 : number.toLocaleString('en-IN').split('.');\n      const formatted = parts[0].replace(/(\\d)(?=(\\d\\d)+\\d$)/g, '$1,');\n      if (parts.length === 2) {\n        return `${formatted}.${parts[1]}`;\n      } else {\n        return formatted;\n      }\n    }\n  } catch (e) {\n    return number;\n  }\n}\nexport function convertToDecimalPrefix(number) {\n  const prefixes = ['', 'k', 'L', 'Cr', 'Ar', 'Mo', 'Br', 'Tr', 'Qu'];\n  let exponent = 0;\n  if (number >= 10000000) {\n    exponent = 3; // Crore\n    number = number / 10000000;\n  } else if (number >= 100000) {\n    exponent = 2; // Lakh\n    number = number / 100000;\n  } else if (number >= 1000) {\n    exponent = 1; // Thousand\n    number = number / 1000;\n  }\n  if (exponent > 0) {\n    // Check if the number is a whole number\n    if (Number.isInteger(number)) {\n      return number + prefixes[exponent];\n    } else {\n      return number.toFixed(2) + prefixes[exponent];\n    }\n  }\n  return number;\n}\nexport const getReminders = (punch_in_time_fr_org, first_reminder_fr_org, factor) => {\n  punch_in_time_fr_org = punch_in_time_fr_org.slice(0, -2) + ' ' + punch_in_time_fr_org.slice(-2);\n  function generateReminders(punchInTime, reminderTime) {\n    var notifications = [];\n\n    // Convert punchInTime to minutes\n    var punchInMinutes = getMinutes(punchInTime);\n\n    // First reminder\n    var reminderMinutes = punchInMinutes - reminderTime;\n    var reminder = formatTime(reminderMinutes);\n    notifications.push({\n      reminderTime: reminder,\n      reminderMinutes: reminderTime\n    });\n\n    // Generate subsequent notifications\n    while (reminderTime > 1) {\n      reminderTime = Math.ceil(reminderTime / factor);\n      var notificationMinutes = punchInMinutes - reminderTime;\n      var notification = formatTime(notificationMinutes);\n      notifications.push({\n        reminderTime: notification,\n        reminderMinutes: reminderTime\n      });\n    }\n    function getMinutes(time) {\n      var [hours, minutes, period] = time.match(/^(\\d+):(\\d+)\\s+(AM|PM)$/).slice(1);\n      hours = parseInt(hours);\n      minutes = parseInt(minutes);\n      if (period === 'PM' && hours !== 12) {\n        hours += 12;\n      } else if (period === 'AM' && hours === 12) {\n        hours = 0;\n      }\n      return hours * 60 + minutes;\n    }\n\n    // Helper function to format minutes to time string\n    function formatTime(minutes) {\n      var hours = Math.floor(minutes / 60) % 12;\n      var mins = minutes % 60;\n      var period = Math.floor(minutes / 60) >= 12 ? 'PM' : 'AM';\n      hours = hours === 0 ? 12 : hours;\n      return padZero(hours) + ':' + padZero(mins) + ' ' + period;\n    }\n\n    // Helper function to pad single digit numbers with leading zero\n    function padZero(number) {\n      return number.toString().padStart(2, '0');\n    }\n    return notifications;\n  }\n  function timeStringToDate(timeStr) {\n    const [time, meridiem] = timeStr.split(' ');\n    let [hours, minutes] = time.split(':');\n    hours = parseInt(hours, 10);\n    minutes = parseInt(minutes, 10);\n    if (meridiem === 'PM' && hours !== 12) {\n      hours += 12;\n    } else if (meridiem === 'AM' && hours === 12) {\n      hours = 0;\n    }\n    const timeInMillis = new Date(0, 0, 0, hours, minutes).getTime();\n    return timeInMillis;\n  }\n  let reminders = generateReminders(punch_in_time_fr_org, first_reminder_fr_org);\n  return reminders;\n};\nexport const convertValuesOfObjToMoment = obj => {\n  if (obj) {\n    Object.keys(obj).map(key => {\n      const value = obj[key];\n      if (isValidDate(value)) {\n        //  debugger;\n        const momentDate = moment(value, true);\n        if (momentDate.isValid()) {\n          obj[key] = momentDate;\n        }\n      }\n    });\n  }\n  return obj;\n};\nfunction isTimestampDateFormat(inputString) {\n  return !isNaN(Date.parse(inputString));\n}\nfunction isValidDate(dateString) {\n  try {\n    var regEx = /^\\d{4}-\\d{2}-\\d{2}$/;\n    if (!dateString.match(regEx)) return false; // Invalid format\n    var d = new Date(dateString);\n    var dNum = d.getTime();\n    if (!dNum && dNum !== 0) return false; // NaN value, Invalid date\n    return d.toISOString().slice(0, 10) === dateString;\n  } catch (error) {\n    return false;\n  }\n}\nexport const getDataForLastThreeMonths = (keys, filter) => {\n  console.log('active', keys, filter);\n  const endDate = moment();\n  const startDate = moment().subtract(5, 'months');\n  filter[keys] = [startDate, endDate].map(date => date.toISOString());\n  return filter;\n};\nexport const getSkillLevelOptions = (isSrvcPrvdr = false) => {\n  let skillLevelPrefix = isSrvcPrvdr ? 'vertical_level' : 'srvc_type_level';\n  return [{\n    value: `${skillLevelPrefix}_1`,\n    label: '1(Beginner)'\n  }, {\n    value: `${skillLevelPrefix}_2`,\n    label: '2'\n  }, {\n    value: `${skillLevelPrefix}_3`,\n    label: '3'\n  }, {\n    value: `${skillLevelPrefix}_4`,\n    label: '4'\n  }, {\n    value: `${skillLevelPrefix}_5`,\n    label: '5(Expert)'\n  }];\n};\nexport const getLabel = (key, options) => {\n  let label;\n  if ((options === null || options === void 0 ? void 0 : options.length) > 0) {\n    options.forEach(singleOption => {\n      if (singleOption.value == key) {\n        label = singleOption.label;\n      }\n    });\n  }\n  return label;\n};\nexport const isTodayDate = dateString => {\n  var givenDate = new Date(dateString);\n  var currentDate = new Date();\n  return givenDate.toDateString() === currentDate.toDateString();\n};\nexport const isYesterdayDate = dateString => {\n  var givenDate = new Date(dateString);\n  var currentDate = new Date();\n  var yesterday = new Date();\n  yesterday.setDate(currentDate.getDate() - 1);\n  return givenDate.toDateString() === yesterday.toDateString();\n};\nexport const validateCronFrequency = (rule, value, callback) => {\n  // This is a basic pattern for validating cron expressions\n  // It covers typical cron patterns but might need to be adjusted for more complex cases\n  const cronPattern = /^(\\*|([0-5]?\\d)) (\\*|([0-5]?\\d)) (\\*|1?\\d|2[0-3]) (\\*|[1-9]|[12]\\d|3[01]) (\\*|[1-9]|1[0-2])$/;\n  if (!value || cronPattern.test(value)) {\n    callback(); // Validation passed\n  } else {\n    callback('Please enter a valid cron frequency (* * * * *)'); // Validation failed\n  }\n};\nexport const validateLambdaArn = (rule, value, callback) => {\n  const lambdaArnPattern = /^arn:aws:lambda:[a-z\\d-]+:\\d{12}:function:[a-zA-Z0-9-_]+$/;\n  if (!value || lambdaArnPattern.test(value)) {\n    callback(); // Validation passed\n  } else {\n    callback('Please enter a valid Lambda ARN'); // Validation failed\n  }\n};\nexport const getRatingFrFilter = () => {\n  let numberOfRatingValue = 5;\n  let possibleRatingOptions = [];\n  possibleRatingOptions = getOptionsListFrPossibleNumbers(numberOfRatingValue, number => ({\n    color: getColorInBetweenByPercentage([255, 72, 0],\n    // Min color\n    [0, 250, 156],\n    // Max color\n    number / numberOfRatingValue)\n  })).filter(item => item.value != 0);\n  return {\n    key: 'rating_value',\n    label: 'Rating',\n    placeholder: 'Select..',\n    widget: 'select',\n    quick: true,\n    widgetProps: {\n      // make sure to add mode as multiple when its for quick\n      mode: 'multiple',\n      optionFilterProp: 'children',\n      showSearch: true\n    },\n    options: possibleRatingOptions\n  };\n};\nconst isMobileView = () => {\n  return window.innerWidth <= 768;\n};\nconst isScreenZoomPercentage125 = () => {\n  return window.devicePixelRatio == 1.25;\n};\nexport const getMapAddressFieldsMeta = ({\n  form_data,\n  formRef,\n  forceUpdateFn,\n  showPickOnMapModel,\n  togglePickOnMapModel,\n  is_pincode_mandatory,\n  orgSettingsData,\n  prefix\n}) => {\n  var _formRef$current4, _formRef$current5;\n  const filledAddress = getConcatenatedAddressFrmForm(prefix, formRef) || getConcatenatedAddressFormData(prefix, form_data, formRef);\n  const showClearFieldsButton = !!filledAddress;\n  const latitude = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current4 = formRef.current) === null || _formRef$current4 === void 0 ? void 0 : _formRef$current4.getFieldValue('location_latitude')) || (form_data === null || form_data === void 0 ? void 0 : form_data.location_latitude);\n  const longitude = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current5 = formRef.current) === null || _formRef$current5 === void 0 ? void 0 : _formRef$current5.getFieldValue('location_Longitude')) || (form_data === null || form_data === void 0 ? void 0 : form_data.location_Longitude);\n  const onLatLngValueChange = async () => {\n    var _formRef$current6, _formRef$current7;\n    const latitude = formRef === null || formRef === void 0 ? void 0 : (_formRef$current6 = formRef.current) === null || _formRef$current6 === void 0 ? void 0 : _formRef$current6.getFieldValue('location_latitude');\n    const longitude = formRef === null || formRef === void 0 ? void 0 : (_formRef$current7 = formRef.current) === null || _formRef$current7 === void 0 ? void 0 : _formRef$current7.getFieldValue('location_Longitude');\n    if (latitude && longitude) {\n      var _data$results;\n      const data = await getAddressBasedOnLatAndLng(latitude, longitude);\n      addressFill(getAddressObj((_data$results = data.results) === null || _data$results === void 0 ? void 0 : _data$results[0]), formRef, prefix);\n    }\n  };\n  let clearGoogleAddressSearch = new Date().getTime();\n  const clearAddress = formRef => {\n    const keyEmptyValue = {};\n    getAddressFieldKeys(prefix).forEach(singleKey => {\n      keyEmptyValue[singleKey] = '';\n    });\n    clearGoogleAddressSearch = new Date().getTime();\n    formRef.current.setFieldsValue(keyEmptyValue);\n    forceUpdateFn();\n  };\n  const meta = {\n    formItemLayout: null,\n    fields: [{\n      key: 'mark_location_on_map',\n      render: () => /*#__PURE__*/React.createElement(\"div\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1719,\n          columnNumber: 21\n        }\n      }, /*#__PURE__*/React.createElement(LocationSearchInput, {\n        placeholder: \"Address\",\n        useCountryAndID: true,\n        onChange: address => {\n          addressFill(address, formRef, prefix);\n          forceUpdateFn();\n        },\n        orgSettingsData: orgSettingsData,\n        triggerClear: clearGoogleAddressSearch,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1720,\n          columnNumber: 25\n        }\n      }), /*#__PURE__*/React.createElement(Button, {\n        onClick: togglePickOnMapModel,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1730,\n          columnNumber: 25\n        }\n      }, \"Pick on Map\"), showPickOnMapModel && /*#__PURE__*/React.createElement(MapComponent, {\n        showPickOnMapModel: showPickOnMapModel,\n        defaultLocation: {\n          lat: latitude || 22.5437692,\n          lng: longitude || 79.1230844\n        },\n        onChange: address => {\n          addressFill(address, formRef, prefix);\n          forceUpdateFn();\n          togglePickOnMapModel();\n        },\n        togglePickOnMapModel: togglePickOnMapModel,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1734,\n          columnNumber: 29\n        }\n      }))\n    }, {\n      key: 'clear_fields',\n      colSpan: 4,\n      label: 'Clear fields',\n      render: () => showClearFieldsButton && /*#__PURE__*/React.createElement(Button, {\n        type: \"link\",\n        onClick: () => clearAddress(formRef),\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1757,\n          columnNumber: 25\n        }\n      }, \"Reset Address\")\n    }, {\n      key: `${prefix}line_0`,\n      colSpan: 4,\n      label: 'Flat no',\n      onChange: forceUpdateFn,\n      rules: [{\n        max: 50\n      }]\n    }, {\n      key: `${prefix}line_1`,\n      colSpan: 4,\n      label: 'Building/Apartment name',\n      onChange: forceUpdateFn,\n      rules: [{\n        max: 200\n      }]\n    }, {\n      key: `${prefix}line_2`,\n      label: 'Line 1',\n      colSpan: 4,\n      disabled: true,\n      rules: [{\n        max: 1000\n      }]\n    }, {\n      key: `${prefix}line_3`,\n      label: 'Line 2',\n      colSpan: 4,\n      disabled: true,\n      rules: [{\n        max: 200\n      }]\n    }, {\n      key: `${prefix}pincode`,\n      label: 'Pincode',\n      colSpan: 2,\n      required: is_pincode_mandatory,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        params: {\n          fn: 'getPincode'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: `${prefix}city`,\n      label: 'City',\n      colSpan: 2,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        params: {\n          fn: 'getCities'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: `${prefix}state`,\n      label: 'State',\n      colSpan: 4,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        params: {\n          fn: 'getState'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: 'location_latitude',\n      label: 'Latitude',\n      colSpan: 4,\n      //required: !!formRef?.current?.getFieldValue('location_Longitude'),\n      placeholder: 'Eg 37.7749',\n      onChange: e => {\n        onLatLngValueChange();\n        forceUpdateFn();\n      }\n    }, {\n      key: 'location_Longitude',\n      label: 'Longitude',\n      colSpan: 4,\n      // required: !!formRef?.current?.getFieldValue('location_latitude'),\n      placeholder: 'Eg -122.4194',\n      onChange: e => {\n        onLatLngValueChange();\n        forceUpdateFn();\n      }\n    }, ...(latitude && longitude ? [{\n      key: 'view_location',\n      render: () => {\n        const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;\n        return /*#__PURE__*/React.createElement(\"a\", {\n          href: url,\n          target: \"_blank\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1874,\n            columnNumber: 35\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-location\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1875,\n            columnNumber: 39\n          }\n        }, \" \"), \"View on Google Map\");\n      }\n    }] : [])]\n  };\n  return meta;\n};\nexport { hasAnyFileChanged, getGeneralFileSection, getLinktoObject, durationAsString, getTouchedFieldsValueInForm, isTimePassed, getColorCodeFrStatusCategory, priorities, getTextColorFrPriority, convertUTCToDisplayTime, convertDateFieldsToMoments, getRandomIconByColor, getRandomBgColor, generateRandomKey, getAnyObjectFrFilter, getEmptyObjectFrFilter, isMobileView, isScreenZoomPercentage125 };\nexport const setHtmlHeadTitle = (path = '/', suffixLabel = '') => {\n  var _userDetails$org, _userDetails$org2, _suffixLabel;\n  suffixLabel = typeof suffixLabel === 'string' ? suffixLabel : String(suffixLabel || '');\n  path = path.replace(/\\/$/, '').trim();\n  const routeTitleMap = extractLinksAndLabels();\n  const userDetails = ConfigHelpers.getUserDetailsInLocalStorage();\n  const iconInConfig = userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$org = userDetails.org) === null || _userDetails$org === void 0 ? void 0 : _userDetails$org.icon;\n  const orgNickname = (userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$org2 = userDetails.org) === null || _userDetails$org2 === void 0 ? void 0 : _userDetails$org2.nickname) || '';\n\n  // Function to find the base path by stripping dynamic segments\n  const getBasePath = currentPath => {\n    const segments = currentPath.split('/');\n    // Iteratively reduce the path to find the longest matching base path in routeTitleMap\n    while (segments.length > 0) {\n      const potentialPath = segments.join('/') || '/';\n      if (routeTitleMap[potentialPath]) {\n        return potentialPath;\n      }\n      segments.pop();\n    }\n    return '/'; // Fallback to root if no match is found\n  };\n  const basePath = getBasePath(path);\n  const basePageTitle = routeTitleMap[basePath] || '';\n  let fullTitle = `${orgNickname} ${basePageTitle}`;\n  // If a suffixLabel is provided, append it with a hyphen\n  if (((_suffixLabel = suffixLabel) === null || _suffixLabel === void 0 ? void 0 : _suffixLabel.trim()) !== '') {\n    fullTitle += ` - ${suffixLabel}`;\n  }\n  fullTitle += ' TMS';\n  setPageTitleAndFavIcon(fullTitle, iconInConfig);\n};\nexport function extractLinksAndLabels() {\n  const links = document.querySelectorAll('a'); // Selecting all <a> tags in the DOM\n\n  return Array.from(links).reduce((acc, link) => {\n    const href = link.getAttribute('href');\n    const url = new URL(href, window.location.origin); // Ensure base URL if href is relative\n    const pathname = url.pathname; // Extract only the pathname (excluding query params and hash)\n\n    const textContent = Array.from(link.childNodes).filter(node => {\n      return node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== '' || node.nodeName === 'SPAN' || node.nodeName === 'TAG';\n    }).map(node => {\n      // Use a regular expression to strip out numbers like '4.8 of 103'\n      let text = node.textContent.trim();\n      text = text.replace(/\\d+(\\.\\d+)?/g, '').trim(); // Remove numeric values\n      return text;\n    }).join(' ');\n    if (pathname && textContent) {\n      acc[pathname] = textContent;\n    }\n    return acc;\n  }, {});\n}\nexport const WIFY_LOCAL_STROAGE_KEY = 'wify_data';\nexport const LOCAL_STORAGE_KEYS = {\n  dashboard: {\n    value: 'dashboard',\n    my_data_widget: {\n      value: 'my_data_widget'\n    }\n  },\n  user_settings: {\n    value: 'user_settings',\n    show_org_details: false\n  },\n  ai_chat_bot: {\n    value: 'ai_chat_bot',\n    isOpen: false\n  }\n};\nexport function writeToLocalStorage({\n  key,\n  value\n}) {\n  const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\n\n  // Initialize empty object if nothing is stored\n  if (!storedValue) {\n    localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify({}));\n  }\n  let updatedValue = {};\n  if (storedValue) {\n    try {\n      updatedValue = JSON.parse(storedValue);\n    } catch (error) {\n      console.log('Error parsing JSON from localStorage:', error);\n    }\n  }\n\n  // Initialize the key if it doesn't exist\n  if (!updatedValue[key]) {\n    updatedValue[key] = value;\n  } else {\n    // If it's already there, we want to merge/update the value based on its type\n    if (typeof updatedValue[key] === 'object' && typeof value === 'object') {\n      updatedValue[key] = {\n        ...updatedValue[key],\n        ...value\n      }; // If both are objects, merge them\n    } else {\n      updatedValue[key] = value; // Otherwise, just replace the value\n    }\n  }\n\n  // Save the updated value back to localStorage\n  localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify(updatedValue));\n}\nexport const findNestedKey = (obj, searchKey) => {\n  if (obj === null || typeof obj !== 'object') {\n    return null;\n  }\n  if (obj.hasOwnProperty(searchKey)) {\n    return obj[searchKey];\n  }\n  for (const k in obj) {\n    const result = findNestedKey(obj[k], searchKey);\n    if (result !== null) {\n      return result;\n    }\n  }\n  return null;\n};\nexport function readFromLocalStorage({\n  key\n}) {\n  const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\n  if (storedValue) {\n    try {\n      const parsedValue = JSON.parse(storedValue);\n      return findNestedKey(parsedValue, key);\n    } catch (error) {\n      console.log('Error parsing JSON from localStorage:', error);\n      return null;\n    }\n  }\n  return null; // Return null if WIFY_LOCAL_STROAGE_KEY is not found\n}\nexport function deleteFromLocalStorage({\n  key\n}) {\n  const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\n  if (storedValue) {\n    try {\n      const parsedValue = JSON.parse(storedValue);\n\n      // Helper function to delete a nested key\n      const deleteNestedKey = (obj, searchKey) => {\n        if (obj === null || typeof obj !== 'object') {\n          return false; // Return false if it's not an object\n        }\n        if (obj.hasOwnProperty(searchKey)) {\n          delete obj[searchKey];\n          return true; // Key found and deleted\n        }\n        for (const k in obj) {\n          if (deleteNestedKey(obj[k], searchKey)) {\n            return true; // Key found and deleted in nested object\n          }\n        }\n        return false; // Key not found\n      };\n      const isDeleted = deleteNestedKey(parsedValue, key);\n      if (isDeleted) {\n        localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify(parsedValue));\n      } else {\n        console.warn(`Key \"${key}\" not found.`);\n      }\n    } catch (error) {\n      console.log('Error parsing JSON from localStorage:', error);\n    }\n  } else {\n    console.warn(`No data found in ${WIFY_LOCAL_STROAGE_KEY}.`);\n  }\n}\nexport const getFileExtension = fileUrl => {\n  const fileName = getFileNameFrmUrl(fileUrl);\n  return fileExtensionRegex.exec(fileName)[1];\n};\nexport const getFileNameFrmUrl = fileUrl => {\n  fileUrl = decodeURI(fileUrl.split('?')[0]);\n  return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);\n};\n\n// Helper function to compare two objects based on the keys of the submitted object.\nexport const isEqualData = (submitted, original) => {\n  return Object.keys(submitted).every(key => {\n    const submittedValue = submitted[key];\n    const originalValue = original[key];\n\n    // If the value is an array, check equality with submitted original\n    if (Array.isArray(submittedValue) && Array.isArray(originalValue)) {\n      return JSON.stringify(submittedValue) === JSON.stringify(originalValue);\n    }\n    return submittedValue === originalValue;\n  });\n};\nexport const getUserRightsFrService = (srvc_id, userServiceAccess) => {\n  var _foundServiceAccess;\n  let foundServiceAccess = undefined;\n  if (userServiceAccess) {\n    userServiceAccess.map(singleServiceAccess => {\n      if (singleServiceAccess.menu_id == srvc_id) {\n        foundServiceAccess = singleServiceAccess;\n      }\n    });\n  }\n  return (_foundServiceAccess = foundServiceAccess) === null || _foundServiceAccess === void 0 ? void 0 : _foundServiceAccess.rights_type;\n};", "map": {"version": 3, "names": ["React", "DatePicker", "message", "Rate", "Empty", "<PERSON><PERSON>", "isArray", "moment", "v4", "uuidv4", "ConfigHelpers", "XLSX", "decodeFileSectionsFrmJson", "isInsideMobileApp", "LocationSearchInput", "addressFill", "getAddressBasedOnLatAndLng", "getAddressFieldKeys", "getConcatenatedAddressFrmForm", "getAddressObj", "getConcatenatedAddressFormData", "MapComponent", "useLocation", "_", "require", "fileExtensionRegex", "renderCellContent", "text", "item", "statuses", "backgroundClass", "shouldApplyBackground", "isKeyInDoneOrClosedStatuses", "status_key", "className", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "key", "srvc_type_statuses", "_statuses", "_statuses$DONE", "_statuses2", "_statuses2$CLOSED", "JSON", "parse", "error", "console", "done<PERSON><PERSON><PERSON>", "DONE", "map", "status", "closedKeys", "CLOSED", "includes", "calculateCenterLatLng", "latlngArray", "length", "sumLat", "sumLng", "i", "avgLat", "avgLng", "convertTimeToSortableFormat", "timeString", "time", "period", "split", "hours", "minutes", "parseInt", "toLowerCase", "downloadExcelFileWithTabwise", "dataWithCategories", "workbook", "utils", "book_new", "for<PERSON>ach", "name", "items", "worksheet", "json_to_sheet", "book_append_sheet", "writeFile", "downloadExcelFileForAndroid", "window", "wifyApp", "showToastMessage", "binaryString", "write", "bookType", "type", "arrayBuffer", "s2ab", "blob", "Blob", "blobUrl", "URL", "createObjectURL", "getBase64FromBlobUrl", "base64data", "saveWorkbookFileInDevice", "callback", "fetch", "then", "response", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "catch", "s", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "Uint8Array", "charCodeAt", "convertValueToUnit", "num", "unit", "isNaN", "suffixes", "K", "L", "Cr", "hasOwnProperty", "divisor", "formattedNum", "toFixed", "getCreated<PERSON>ate<PERSON><PERSON>er", "title", "activeFilters", "fromDate", "toDate", "creation_date", "creation_srvc_req_date", "_activeFilters$creati", "_activeFilters$creati2", "currentDate", "local", "format", "toLocaleTimeString", "hour", "minute", "convertUTCToDisplayTime", "getCurrentDateAndTimeFrDisplay", "getParamsObjFromUrl", "paramName", "urlSearch", "document", "location", "href", "baseUrl", "_query", "_queryRes", "convertNestedQueryUrlToObj", "url", "log", "params", "URLSearchParams", "obj", "value", "entries", "decoded<PERSON>ey", "decodeURIComponent", "decodedValue", "nestedObj", "updateQueryParams", "query", "newUrl", "history", "replaceState", "pathname", "stringifyQueryParams", "queryObject", "Object", "call", "_value", "stringify", "append", "toString", "showCreateOrUpdateSuccessMessage", "top_", "duration_", "message_", "config", "top", "duration", "success", "getCustom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "srvcCustomFields<PERSON>son", "customFileFieldsMeta", "customFileFields", "singleCustomFileField", "customFileFieldObj", "label", "placeholder", "widget", "widgetProps", "allowClear", "showSearch", "optionFilterProp", "options", "color", "push", "priorities", "BILLING_TYPE", "getSelectOptionsFrZeroToHundred", "startVal", "returnValue", "BILLING_DISCOUNT_TYPE", "BILLING_APPROVER_TYPE", "PROJECT_BASED_SERVICE_TYPE", "getOptionsFrNatureOfServiceType", "getOptionsFrDefaultModeOfRating", "getValueDataFrmFormMeta", "fieldsMeta", "form_data", "filter", "listItem", "index", "self", "itm", "indexOf", "valueData", "singleFieldMeta", "_singleFieldMeta$widg", "utc", "getLabelFrmOptionsValue", "displayName", "currValue", "parseFormulaToString", "formula", "nameToIdMapping", "undefined", "data", "matches", "match", "matchString", "word", "substring", "fieldValue", "replace", "parseFormulaToValue", "isSrvcReqLocked", "eval", "parseFloat", "getStringToArray", "_value2", "keys", "singlePin", "getStatusListForOptions", "statuses_db", "decodeStatuses", "getCenterLocFrIndMap", "lat", "lng", "getColorCodeFrStatusCategory", "category", "colorFrCategory", "ACTIVE", "getTextColorFrPriority", "priority", "colorFrPriority", "<PERSON><PERSON>", "High", "Normal", "Low", "None", "getRandomIconByColor", "arrayOfColors", "Math", "floor", "random", "getRandomBgColor", "getRandomTagBgColor", "generateRandomKey", "characters", "<PERSON><PERSON><PERSON><PERSON>", "char<PERSON>t", "join", "getAnyObjectFrFilter", "getEmptyObjectFrFilter", "getPriorityObjectFrFilter", "quick", "mode", "getSpareOptions", "getInventoryTypeList", "getInventoryType", "inventoryType", "types", "product", "stock_item_type", "stock_type", "spare", "getSolidUnitOptions", "getLiquidUnitOptions", "getUnitOptions", "<PERSON><PERSON><PERSON><PERSON>", "getIsDeletedFrFilter", "noAny", "getShowPincodeFrFilter", "getAgingFiltersFrBrand", "getNoOfTasksObjectFrFilter", "numberOfTasks", "possibleTasksOptions", "getOptionsListFrPossibleNumbers", "number", "getColorInBetweenByPercentage", "taskOptionGreaterThanFiveObj", "convertDateFieldsToMoments", "returnData", "matchingMeta", "fieldMeta", "RangePicker", "_fieldMeta$widgetProp", "ranges", "date", "<PERSON><PERSON><PERSON><PERSON>", "cust_widget", "_parsedTime", "parsedTime", "isMoment", "ISO_8601", "isTimePassed", "sql_timestamp", "momentUtc", "valueOf", "convertMomentToLocalDateString", "moment_date", "getCustomPrefixNameFrUploadingFiles", "userName", "getFullUserName", "convertMomentToDateString", "getCurrentDay", "just_date", "just_time", "getTouchedFieldsValueInForm", "formInstance", "touchFieldsKeys", "returnFields", "fieldKeys", "singleKey", "isFieldTouched", "durationAsString", "start", "diff", "days", "asDays", "daysFormatted", "hoursFormatted", "minutesFormatted", "getLinktoObject", "search", "element", "getGeneralFileSection", "required", "hasAnyFileChanged", "newFilesBySection", "oldFilesBySection", "has<PERSON><PERSON>ed", "sections", "newFilesFrSection", "oldFilesFrSection", "oldFile", "newFile", "ExcelDateToJSDate", "serial", "isANumber", "utc_days", "utc_value", "date_info", "Date", "fractional_day", "total_seconds", "seconds", "getFullYear", "getMonth", "getDate", "regexp", "cleanDateStr", "momentDate", "decodeAntdFormErrorsToString", "errObject", "finalString", "errorFields", "errors", "singleError", "return<PERSON>abel", "Array", "singleValue", "matchingOption", "find", "singleOption", "addDaysToDate", "setDate", "getDayName", "dateStr", "locale", "toLocaleDateString", "weekday", "getDaysBetweenMoments", "startDate", "stopDate", "inclusive", "getDaysBetweenDates", "dateArray", "shift", "pop", "getPresetRangesForFutureSpans", "Today", "Tomorrow", "add", "getPresetRangesForRangeDatePicker", "Yesterday", "subtract", "startOf", "endOf", "setPageTitleAndFavIcon", "icon", "favicon", "getElementById", "html_title", "innerHTML", "getSearchInLinkAsObject", "searchParams", "paramsObject", "color1", "color2", "percentage", "p", "w", "w1", "w2", "rgb", "round", "maxNumber", "customParamsCallback", "returnOptionsArray", "optionsArray", "from", "customParams", "getViewModeMetaFrFormMeta", "formMetaFrDisplay", "_singleFieldMeta$widg2", "_singleFieldMeta$widg3", "render<PERSON>iew", "disabled", "displayText", "generateUUID", "NoData", "image", "PRESENTED_IMAGE_SIMPLE", "getRandomHexBgColor", "getColorCodeFrReqDate", "colorFrReqDate", "TODAY", "UPCOMING", "OVERDUE", "UNSPECIFIED", "isAndroidApp", "_navigator", "user_agent", "navigator", "userAgent", "areArraysSame", "a1", "a2", "handleClearSelect", "formRef", "<PERSON><PERSON><PERSON>", "_formRef$current", "current", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setRemarkFieldAsNoRemarkIfEmpty", "prefillFormData", "_formRef$current2", "remark", "getFieldValue", "remarks", "setTimeout", "getCustomVarMetaFrTemplate", "selected_custom_fields_meta", "full_prefix", "custom_fields_meta", "filledCustomVars", "refresh", "single_selected_custom_fields_meta_", "_formRef$current3", "copy_field", "handleFilterClearDateIfNull", "filters", "<PERSON><PERSON><PERSON>", "commaSeparatedNumberForDashboard", "parts", "toLocaleString", "formatted", "e", "convertToDecimalPrefix", "prefixes", "exponent", "Number", "isInteger", "get<PERSON><PERSON><PERSON>s", "punch_in_time_fr_org", "first_reminder_fr_org", "factor", "slice", "generateReminders", "punchInTime", "reminderTime", "notifications", "punchInMinutes", "getMinutes", "reminderMinutes", "reminder", "formatTime", "ceil", "notificationMinutes", "notification", "mins", "padZero", "padStart", "timeStringToDate", "timeStr", "meridiem", "timeInMillis", "getTime", "reminders", "convertValuesOfObjToMoment", "isValidDate", "isTimestampDateFormat", "inputString", "dateString", "regEx", "d", "dNum", "toISOString", "getDataForLastThreeMonths", "endDate", "getSkillLevelOptions", "isSrvcPrvdr", "skillLevelPrefix", "get<PERSON><PERSON><PERSON>", "isTodayDate", "givenDate", "toDateString", "isYesterdayDate", "yesterday", "validateCronFrequency", "rule", "cronPattern", "test", "validateLambdaArn", "lambdaArnPattern", "getRatingFr<PERSON>ilter", "numberOfRatingValue", "possibleRatingOptions", "isMobile<PERSON>iew", "innerWidth", "isScreenZoomPercentage125", "devicePixelRatio", "getMapAddressFieldsMeta", "forceUpdateFn", "showPickOnMapModel", "togglePickOnMapModel", "is_pincode_mandatory", "orgSettingsData", "prefix", "_formRef$current4", "_formRef$current5", "<PERSON><PERSON><PERSON><PERSON>", "showClear<PERSON>ieldsButton", "latitude", "location_latitude", "longitude", "location_Longitude", "onLatLngValueChange", "_formRef$current6", "_formRef$current7", "_data$results", "results", "clearGoogleAddressSearch", "<PERSON><PERSON><PERSON><PERSON>", "keyEmptyValue", "meta", "formItemLayout", "fields", "render", "useCountryAndID", "onChange", "address", "triggerClear", "onClick", "defaultLocation", "colSpan", "rules", "max", "fn", "labelInValue", "style", "width", "target", "setHtmlHeadTitle", "path", "suffix<PERSON>abel", "_userDetails$org", "_userDetails$org2", "_suffix<PERSON>abel", "String", "trim", "routeTitleMap", "extractLinksAndLabels", "userDetails", "getUserDetailsInLocalStorage", "iconInConfig", "org", "orgNickname", "nickname", "get<PERSON><PERSON><PERSON><PERSON>", "currentPath", "segments", "potentialPath", "basePath", "basePageTitle", "fullTitle", "links", "querySelectorAll", "reduce", "acc", "link", "getAttribute", "origin", "textContent", "childNodes", "node", "nodeType", "Node", "TEXT_NODE", "nodeName", "WIFY_LOCAL_STROAGE_KEY", "LOCAL_STORAGE_KEYS", "dashboard", "my_data_widget", "user_settings", "show_org_details", "ai_chat_bot", "isOpen", "writeToLocalStorage", "storedValue", "localStorage", "getItem", "setItem", "updatedValue", "find<PERSON><PERSON><PERSON><PERSON>", "search<PERSON>ey", "k", "readFromLocalStorage", "parsedValue", "deleteFromLocalStorage", "deleteNestedKey", "isDeleted", "warn", "getFileExtension", "fileUrl", "getFileNameFrmUrl", "exec", "decodeURI", "lastIndexOf", "isEqualData", "submitted", "original", "every", "submittedValue", "originalValue", "getUserRightsFrService", "srvc_id", "userServiceAccess", "_foundServiceAccess", "foundServiceAccess", "singleServiceAccess", "menu_id", "rights_type"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/util/helpers.js"], "sourcesContent": ["import React from 'react';\r\nimport { DatePicker, message, Rate, Empty, Button } from 'antd';\r\nimport { isArray } from 'lodash';\r\nimport moment from 'moment';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport ConfigHelpers from './ConfigHelpers';\r\nimport * as XLSX from 'xlsx';\r\nimport { decodeFileSectionsFrmJson } from '../components/wify-utils/FieldCreator/helpers';\r\nimport { isInsideMobileApp } from './AppHelpers';\r\nimport LocationSearchInput from '../components/LocationSearchInput';\r\nimport {\r\n    addressFill,\r\n    getAddressBasedOnLatAndLng,\r\n    getAddressFieldKeys,\r\n    getConcatenatedAddressFrmForm,\r\n} from './CustomerHelpers';\r\nimport {\r\n    getAddressObj,\r\n    getConcatenatedAddressFormData,\r\n} from '../routes/users/helper';\r\nimport MapComponent from '../components/wify-utils/MapComponent/index ';\r\nimport { useLocation } from 'react-router-dom';\r\n\r\nconst _ = require('lodash');\r\nconst fileExtensionRegex = /(?:\\.([^.]+))?$/;\r\n// Common render function\r\nexport const renderCellContent = (text, item, statuses, backgroundClass) => {\r\n    const shouldApplyBackground =\r\n        text !== 0 && !isKeyInDoneOrClosedStatuses(item.status_key, statuses);\r\n    const className =\r\n        text !== 0\r\n            ? `table_zero ${shouldApplyBackground ? backgroundClass : ''}`\r\n            : '';\r\n    return <span className={className}> {text} </span>;\r\n};\r\n\r\n// Utility function to check if a key is in DONE or CLOSED statuses\r\nexport const isKeyInDoneOrClosedStatuses = (key, srvc_type_statuses) => {\r\n    let statuses;\r\n    try {\r\n        statuses = JSON.parse(srvc_type_statuses);\r\n    } catch (error) {\r\n        console.error('Failed to parse statuses JSON:', error);\r\n        return false;\r\n    }\r\n    const doneKeys = statuses?.DONE?.map((status) => status.key);\r\n    const closedKeys = statuses?.CLOSED?.map((status) => status.key);\r\n    return doneKeys?.includes(key) || closedKeys?.includes(key);\r\n};\r\n\r\nexport const calculateCenterLatLng = (latlngArray) => {\r\n    if (latlngArray.length === 0) {\r\n        return null;\r\n    }\r\n    var sumLat = 0;\r\n    var sumLng = 0;\r\n\r\n    // Calculate the sum of all latitudes and longitudes\r\n    for (var i = 0; i < latlngArray.length; i++) {\r\n        sumLat += latlngArray[i][0]; // Latitude\r\n        sumLng += latlngArray[i][1]; // Longitude\r\n    }\r\n    // Calculate the average of latitudes and longitudes\r\n    var avgLat = sumLat / latlngArray.length;\r\n    var avgLng = sumLng / latlngArray.length;\r\n\r\n    return [avgLat, avgLng]; // Return the center latitude and longitude as an array\r\n};\r\n\r\nexport const convertTimeToSortableFormat = (timeString) => {\r\n    const [time, period] = timeString.split(/(?=[AP]M)/i); // Split time and period (AM/PM)\r\n    let [hours, minutes] = time.split(':'); // Split hours and minutes\r\n    hours = parseInt(hours, 10); // Parse hours as integer\r\n    if (period && period.toLowerCase() === 'pm' && hours < 12) {\r\n        hours += 12; // Add 12 hours for PM time\r\n    } else if (period && period.toLowerCase() === 'am' && hours === 12) {\r\n        hours = 0; // Convert 12 AM to 0 hours\r\n    }\r\n    return hours * 60 + parseInt(minutes, 10); // Convert to minutes for sorting\r\n};\r\n\r\nexport function downloadExcelFileWithTabwise(dataWithCategories, fileName) {\r\n    /*\r\n        //Generate and download an Excel file with tab-wise data.\r\n        //Sample data format for downloadExcelFileWithTabwise function:\r\n        const dataWithCategories = [\r\n            {\r\n                \"name\": \"Seating\",\r\n                \"items\": [\r\n                {\r\n                    \"Area\": \"1BHK\",\r\n                    \"Room\": \"Hall\",\r\n                    \"Width\": 12\r\n                },\r\n                {\r\n                    \"Area\": \"2BHK\",\r\n                    \"Room\": \"Bedroom\",\r\n                    \"Width\": 20\r\n                }\r\n                ]\r\n            },\r\n            {\r\n                \"name\": \"Sqft\",\r\n                \"items\": [\r\n                {\r\n                    \"Quantity\": 10,\r\n                    \"Price\": 20,\r\n                    \"Total\": 200\r\n                }\r\n                ]\r\n            }\r\n        ];\r\n    */\r\n    try {\r\n        const workbook = XLSX.utils.book_new();\r\n        // Loop through each category group\r\n        dataWithCategories.forEach(({ name, items }) => {\r\n            const worksheet = XLSX.utils.json_to_sheet(items);\r\n            XLSX.utils.book_append_sheet(workbook, worksheet, name);\r\n        });\r\n        if (!isInsideMobileApp()) {\r\n            // Generate and download the Excel file\r\n            XLSX.writeFile(workbook, fileName + '.xlsx');\r\n        } else {\r\n            downloadExcelFileForAndroid(workbook, fileName);\r\n        }\r\n    } catch (error) {\r\n        console.error('Error generating and downloading Excel file:', error);\r\n    }\r\n}\r\n\r\nexport function downloadExcelFileForAndroid(workbook, fileName) {\r\n    window.wifyApp.showToastMessage('Downloading File...');\r\n    // Generate the binary string for the Excel file then create blob url\r\n    const binaryString = XLSX.write(workbook, {\r\n        bookType: 'xlsx',\r\n        type: 'binary',\r\n    });\r\n    const arrayBuffer = s2ab(binaryString);\r\n    const blob = new Blob([arrayBuffer], {\r\n        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n    });\r\n    const blobUrl = URL.createObjectURL(blob);\r\n\r\n    // Pass the blob url & base64 data to the mobile interface to handle file saving\r\n    getBase64FromBlobUrl(blobUrl, function (base64data) {\r\n        if (base64data) {\r\n            window.wifyApp.saveWorkbookFileInDevice(\r\n                blobUrl,\r\n                base64data,\r\n                fileName + '.xlsx'\r\n            );\r\n        } else {\r\n            window.wifyApp.showToastMessage('Downloading Failed!');\r\n            console.error('Failed to fetch base64 data');\r\n        }\r\n    });\r\n}\r\n\r\nfunction getBase64FromBlobUrl(blobUrl, callback) {\r\n    fetch(blobUrl)\r\n        .then((response) => response.blob())\r\n        .then((blob) => {\r\n            const reader = new FileReader();\r\n            reader.onloadend = function () {\r\n                const base64data = reader.result.split(',')[1]; // Extract base64 part of the data URL\r\n                callback(base64data);\r\n            };\r\n            reader.readAsDataURL(blob);\r\n        })\r\n        .catch((error) => {\r\n            console.error('Error fetching the blob:', error);\r\n            callback(null); // Return null if there's an error\r\n        });\r\n}\r\n\r\n// Convert binary string to ArrayBuffer\r\nfunction s2ab(s) {\r\n    const buf = new ArrayBuffer(s.length);\r\n    const view = new Uint8Array(buf);\r\n    for (let i = 0; i < s.length; i++) {\r\n        view[i] = s.charCodeAt(i) & 0xff;\r\n    }\r\n    return buf;\r\n}\r\n\r\n/* \r\n    Unit should be\r\n    1000 for thousand\r\n    100000 for lakhs\r\n    .. so on\r\n*/\r\nexport function convertValueToUnit(num, unit) {\r\n    if (isNaN(num)) {\r\n        return '';\r\n    }\r\n    const suffixes = {\r\n        K: 1000,\r\n        L: 100000,\r\n        Cr: 10000000,\r\n        // 'Ar': 1000000000,\r\n        // 'Ab': 1000000000000\r\n    };\r\n\r\n    if (!suffixes.hasOwnProperty(unit)) {\r\n        return 'Invalid unit';\r\n    }\r\n\r\n    let divisor = suffixes[unit];\r\n    let formattedNum = (num / divisor).toFixed(2) + unit;\r\n\r\n    return formattedNum;\r\n}\r\n\r\nexport function getCreatedDateFilter(title, activeFilters, fromDate, toDate) {\r\n    let creation_date = activeFilters.hasOwnProperty('creation_srvc_req_date');\r\n    if (creation_date && activeFilters?.creation_srvc_req_date) {\r\n        fromDate = activeFilters?.creation_srvc_req_date?.[0];\r\n        toDate = activeFilters?.creation_srvc_req_date?.[1];\r\n    }\r\n    var currentDate =\r\n        moment().local().format('MMM-DD-YYYY ') +\r\n        moment()\r\n            .toDate()\r\n            .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    return (\r\n        <h2 className=\"h4 gx-text-capitalize gx-ml-3 gx-mb-4\">\r\n            <span> {title} </span>\r\n            {creation_date ? (\r\n                <small className=\"gx-text-muted\">\r\n                    ( Created Between {convertUTCToDisplayTime(fromDate, true)}{' '}\r\n                    To {convertUTCToDisplayTime(toDate, true)} )\r\n                </small>\r\n            ) : currentDate ? (\r\n                <small className=\"gx-text-muted\">\r\n                    ( Created between{' '}\r\n                    {convertUTCToDisplayTime(currentDate, true)} To{' '}\r\n                    {getCurrentDateAndTimeFrDisplay()} )\r\n                </small>\r\n            ) : (\r\n                []\r\n            )}\r\n        </h2>\r\n    );\r\n}\r\n\r\nexport function getParamsObjFromUrl({ paramName = '' }) {\r\n    const urlSearch = document.location.href;\r\n    const [baseUrl, _query] = urlSearch.split('?');\r\n    let _queryRes = {};\r\n    if (paramName && _query && _query.includes(paramName)) {\r\n        _queryRes = convertNestedQueryUrlToObj({ url: _query });\r\n    }\r\n    return _queryRes;\r\n}\r\n\r\nexport function convertNestedQueryUrlToObj({ url = false }) {\r\n    if (!url) {\r\n        console.log('convertNestedQueryUrlToObj :: No url given');\r\n        return {};\r\n    }\r\n    const params = new URLSearchParams(url);\r\n    const obj = {};\r\n\r\n    for (const [key, value] of params.entries()) {\r\n        const decodedKey = decodeURIComponent(key);\r\n        const decodedValue = decodeURIComponent(value);\r\n        try {\r\n            const nestedObj = JSON.parse(decodedValue);\r\n            obj[decodedKey] = nestedObj;\r\n        } catch (error) {\r\n            obj[decodedKey] = decodedValue;\r\n        }\r\n    }\r\n    return obj;\r\n}\r\n\r\nexport function updateQueryParams({ query = '' }) {\r\n    const urlSearch = document.location.href;\r\n    const [baseUrl, _query] = urlSearch.split('?');\r\n    if (_query) {\r\n        const newUrl = `${urlSearch}&${query}`;\r\n        window.history.replaceState({}, '', newUrl);\r\n    } else {\r\n        const newUrl = `${window.location.pathname}?${query}`;\r\n        window.history.replaceState({}, '', newUrl);\r\n    }\r\n}\r\n\r\nexport function stringifyQueryParams({ queryObject = {} }) {\r\n    const urlSearch = new URLSearchParams();\r\n    for (const key in queryObject) {\r\n        if (Object.hasOwnProperty.call(queryObject, key)) {\r\n            let _value = queryObject[key];\r\n            if (typeof _value == 'object') {\r\n                _value = JSON.stringify(_value);\r\n            }\r\n            urlSearch.append(key, _value);\r\n        }\r\n    }\r\n    return urlSearch.toString();\r\n}\r\n\r\nexport const showCreateOrUpdateSuccessMessage = (\r\n    top_ = 200,\r\n    duration_ = 2,\r\n    message_ = 'Submitted successfully'\r\n) => {\r\n    message.config({\r\n        top: top_, // Set the desired distance from the top of the viewport\r\n        duration: duration_, // Set the duration in seconds for the message to be displayed\r\n    });\r\n    message.success(message_);\r\n};\r\n\r\nexport const getCustomFileFieldsFilter = (srvcCustomFieldsJson) => {\r\n    let customFileFieldsMeta = [];\r\n    let customFileFields = decodeFileSectionsFrmJson(srvcCustomFieldsJson);\r\n    if (customFileFields) {\r\n        customFileFields.forEach((singleCustomFileField) => {\r\n            let customFileFieldObj = {\r\n                key: singleCustomFileField.key + '_uploaded',\r\n                label: singleCustomFileField.title + ' Uploaded',\r\n                placeholder: 'Select..',\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    // \"mode\":\"multiple\" ,\r\n                    allowClear: true,\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                },\r\n                options: [\r\n                    { label: 'Any', value: '-1', color: 'gx-bg-grey' },\r\n                    { label: 'Yes', value: 'Yes' },\r\n                    { label: 'No', value: 'No' },\r\n                ],\r\n            };\r\n            customFileFieldsMeta.push(customFileFieldObj);\r\n        });\r\n    }\r\n    return customFileFieldsMeta;\r\n};\r\n\r\nconst priorities = [\r\n    //urgent,high,normal,low, no priority\r\n    { value: 'Urgent', label: 'Urgent', color: '#f5222d' },\r\n    { value: 'High', label: 'High', color: '#fadb14' },\r\n    { value: 'Normal', label: 'Normal', color: '#13c2c2' },\r\n    { value: 'Low', label: 'Low', color: '#52c41a' },\r\n    { value: 'None', label: 'None', color: '#8c8c8c' },\r\n];\r\n\r\nexport const BILLING_TYPE = [\r\n    { value: 'line_item', label: 'Line item' },\r\n    { value: 'manday', label: 'Manday' },\r\n    { value: 'hybrid', label: 'Hybrid' },\r\n    { value: 'foc', label: 'FOC' },\r\n];\r\n\r\nexport const getSelectOptionsFrZeroToHundred = (startVal = 1) => {\r\n    let returnValue = [];\r\n    for (var i = startVal; i <= 100; i++) {\r\n        returnValue.push({ value: i, label: i });\r\n    }\r\n    return returnValue;\r\n};\r\n\r\nexport const BILLING_DISCOUNT_TYPE = [\r\n    { value: 'percentage', label: 'Percentage' },\r\n    { value: 'value', label: 'Value' },\r\n];\r\n\r\nexport const BILLING_APPROVER_TYPE = [\r\n    { value: 'authority_based', label: 'Authority based' },\r\n    { value: 'static_user', label: 'Static User' },\r\n];\r\n\r\nexport const PROJECT_BASED_SERVICE_TYPE = 'project_based';\r\n\r\nexport const getOptionsFrNatureOfServiceType = () => {\r\n    return [\r\n        { label: 'Task based', value: 'task_based' },\r\n        { label: 'Project based', value: PROJECT_BASED_SERVICE_TYPE },\r\n    ];\r\n};\r\nexport const getOptionsFrDefaultModeOfRating = () => {\r\n    return [\r\n        { label: 'Taskwise', value: 'task_wise' },\r\n        { label: 'Daywise', value: 'day_wise' },\r\n    ];\r\n};\r\n\r\nexport const getValueDataFrmFormMeta = (fieldsMeta, form_data) => {\r\n    //Remove dublicate key from json\r\n    fieldsMeta = fieldsMeta.filter(\r\n        (listItem, index, self) =>\r\n            self.map((itm) => itm.key).indexOf(listItem.key) === index\r\n    );\r\n\r\n    let valueData = {};\r\n    fieldsMeta.forEach((singleFieldMeta) => {\r\n        let key = singleFieldMeta?.key;\r\n        let value = form_data?.[key];\r\n        if (form_data?.[key]) {\r\n            delete singleFieldMeta['colSpan'];\r\n            if (singleFieldMeta.widget == 'date-picker') {\r\n                value = moment.utc(value).format('MMM Do YYYY');\r\n            } else if (singleFieldMeta.widget == 'select') {\r\n                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\r\n            } else if (singleFieldMeta.widget?.displayName == 'Rate') {\r\n                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\r\n            } else if (singleFieldMeta.widget == 'radio-group') {\r\n                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\r\n            } else if (singleFieldMeta.widget == 'checkbox-group') {\r\n                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);\r\n            } else if (singleFieldMeta.widget) {\r\n                let currValue = form_data[key]; // check if label in value\r\n                if (currValue.label) {\r\n                    value = currValue.label;\r\n                }\r\n            }\r\n        }\r\n        valueData[key] = value;\r\n    });\r\n    return valueData;\r\n};\r\n\r\nexport const parseFormulaToString = (\r\n    formula,\r\n    nameToIdMapping = undefined,\r\n    data\r\n) => {\r\n    // debugger;\r\n    let matches = formula.match(/{[^{}]+}/g);\r\n    matches.forEach((matchString) => {\r\n        let word = matchString.substring(1, matchString.length - 1);\r\n        let key = nameToIdMapping[word];\r\n        let fieldValue = data[key] || 'N/A';\r\n        formula = formula.replace(`{${word}}`, fieldValue);\r\n    });\r\n    return formula;\r\n};\r\n\r\nexport const parseFormulaToValue = (\r\n    formula,\r\n    nameToIdMapping = undefined,\r\n    data,\r\n    isSrvcReqLocked = true\r\n) => {\r\n    let matches = formula.match(/{[^{}]+}/g);\r\n    matches.forEach((matchString) => {\r\n        let word = matchString.substring(1, matchString.length - 1);\r\n        let key = nameToIdMapping[word];\r\n        let fieldValue = key in data ? data[key] : 'undefined';\r\n        formula = formula.replace(`{${word}}`, fieldValue);\r\n    });\r\n    let result = 0;\r\n    try {\r\n        result = eval(formula);\r\n        if (!isSrvcReqLocked) {\r\n            result = parseFloat(result).toFixed(2);\r\n        }\r\n    } catch (error) {\r\n        console.log('parseFormulaToValue error', error);\r\n    }\r\n    return result;\r\n};\r\n\r\nexport const getStringToArray = (data) => {\r\n    let value = [];\r\n    if (data != undefined) {\r\n        if (Object.keys(data).length == 0) {\r\n            value = value;\r\n        } else if (typeof data === 'string') {\r\n            value = data.split(',');\r\n        } else {\r\n            value = data;\r\n        }\r\n        value = value?.filter((singlePin) => singlePin != '');\r\n    }\r\n    return value;\r\n};\r\n\r\n/** FROM DB */\r\nexport const getStatusListForOptions = (statuses_db) => {\r\n    let decodeStatuses = [];\r\n    statuses_db.map((status) => {\r\n        decodeStatuses.push({\r\n            label: status.title,\r\n            value: status.key,\r\n            color: status.color,\r\n        });\r\n    });\r\n    return decodeStatuses;\r\n};\r\n\r\nexport const getCenterLocFrIndMap = () => {\r\n    return { lat: 21.7372075, lng: 79.8332191 };\r\n};\r\n\r\nconst getColorCodeFrStatusCategory = (category) => {\r\n    var colorFrCategory = {\r\n        ACTIVE: '#f44336',\r\n        DONE: '#009688',\r\n        CLOSED: '#4caf50',\r\n    };\r\n    return colorFrCategory[category] ? colorFrCategory[category] : '#e1e1e1';\r\n};\r\nconst getTextColorFrPriority = (priority) => {\r\n    var colorFrPriority = {\r\n        Urgent: 'gx-text-red',\r\n        High: 'gx-text-yellow',\r\n        Normal: 'gx-text-cyan',\r\n        Low: 'gx-text-green',\r\n        None: 'gx-text-grey',\r\n    };\r\n    return colorFrPriority[priority] ? colorFrPriority[priority] : '';\r\n};\r\nconst getRandomIconByColor = () => {\r\n    var arrayOfColors = [\r\n        'gx-text-green',\r\n        'gx-text-orange',\r\n        'gx-text-purple',\r\n        'gx-text-amber',\r\n        'gx-text-light-blue',\r\n        'gx-text-blue',\r\n        'gx-text-cyan',\r\n        'gx-text-sepia',\r\n        'gx-text-pink',\r\n    ];\r\n    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\r\n};\r\n\r\nconst getRandomBgColor = () => {\r\n    var arrayOfColors = [\r\n        'gx-bg-green',\r\n        'gx-bg-orange',\r\n        'gx-bg-purple',\r\n        'gx-bg-amber',\r\n        'gx-bg-light-blue',\r\n        'gx-bg-blue',\r\n        'gx-bg-cyan',\r\n        'gx-bg-sepia',\r\n        'gx-bg-pink',\r\n    ];\r\n    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\r\n};\r\nexport const getRandomTagBgColor = () => {\r\n    var arrayOfColors = [\r\n        'magenta',\r\n        'red',\r\n        'volcano',\r\n        'orange',\r\n        'gold',\r\n        'lime',\r\n        'green',\r\n        'cyan',\r\n        'blue',\r\n        'geekblue',\r\n        'purple',\r\n    ];\r\n    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\r\n};\r\n\r\nconst generateRandomKey = (length = 8) => {\r\n    var result = [];\r\n    var characters =\r\n        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\r\n    var charactersLength = characters.length;\r\n    for (var i = 0; i < length; i++) {\r\n        result.push(\r\n            characters.charAt(Math.floor(Math.random() * charactersLength))\r\n        );\r\n    }\r\n    return result.join('');\r\n};\r\n\r\nconst getAnyObjectFrFilter = (text = 'Any') => {\r\n    return { title: text, label: text, value: '-1', color: '#E1E1E1' };\r\n};\r\n\r\nconst getEmptyObjectFrFilter = (text = 'Empty') => {\r\n    return { title: text, label: text, value: '-2', color: '#E1E1E1' };\r\n};\r\n\r\nexport const getPriorityObjectFrFilter = () => {\r\n    return {\r\n        key: 'priority',\r\n        label: 'Priority',\r\n        placeholder: 'Select..',\r\n        widget: 'select',\r\n        quick: true,\r\n        widgetProps: {\r\n            // make sure to add mode as multiple when its for quick\r\n            mode: 'multiple',\r\n            optionFilterProp: 'children',\r\n            showSearch: true,\r\n        },\r\n        options: priorities,\r\n    };\r\n};\r\n\r\nexport const getSpareOptions = () => {\r\n    return [\r\n        { label: 'Repairable', value: 'repairable' },\r\n        { label: 'Non Repairable', value: 'non_repairable' },\r\n    ];\r\n};\r\n\r\nexport const getInventoryTypeList = () => {\r\n    return [\r\n        { label: 'Product Serialized', value: 'serialized-product' },\r\n        { label: 'Product Non-Serialized', value: 'product' },\r\n        { label: 'Spare Serialized', value: 'serialized-spare' },\r\n        { label: 'Spare Non-Serialized', value: 'spare' },\r\n    ];\r\n};\r\n\r\nexport const getInventoryType = (inventoryType) => {\r\n    const types = {\r\n        product: {\r\n            stock_item_type: 'non-serialized',\r\n            stock_type: 'product',\r\n            label: 'Product',\r\n        },\r\n        'serialized-product': {\r\n            stock_item_type: 'serialized',\r\n            stock_type: 'product',\r\n            label: 'Product',\r\n        },\r\n        spare: {\r\n            stock_item_type: 'non-serialized',\r\n            stock_type: 'spare',\r\n            label: 'Spare',\r\n        },\r\n        'serialized-spare': {\r\n            stock_item_type: 'serialized',\r\n            stock_type: 'spare',\r\n            label: 'Spare',\r\n        },\r\n    };\r\n\r\n    return types[inventoryType] || {};\r\n};\r\n\r\nexport const getSolidUnitOptions = () => {\r\n    return [\r\n        { label: 'gram', value: 'gram' },\r\n        { label: 'kilogram', value: 'kilogram' },\r\n        { label: 'tonne', value: 'tonne' },\r\n    ];\r\n};\r\n\r\nexport const getLiquidUnitOptions = () => {\r\n    return [\r\n        { label: 'litre', value: 'litre' },\r\n        { label: 'millilitre', value: 'millilitre' },\r\n    ];\r\n};\r\n\r\nexport const getUnitOptions = () => {\r\n    return [\r\n        { label: 'Quantity', shortLabel: 'Quantity', value: 'quantity' },\r\n        { label: 'Gram', shortLabel: 'g', value: 'gram' },\r\n        { label: 'Kilogram', shortLabel: 'kg', value: 'kilogram' },\r\n        { label: 'Tonne', shortLabel: 't', value: 'tonne' },\r\n        { label: 'Litre', shortLabel: 'l', value: 'litre' },\r\n        { label: 'Millilitre', shortLabel: 'ml', value: 'millilitre' },\r\n    ];\r\n};\r\n\r\nexport const getIsDeletedFrFilter = () => {\r\n    return {\r\n        key: 'is_deleted',\r\n        label: 'Show Deleted',\r\n        placeholder: 'Select..',\r\n        widget: 'select',\r\n        quick: true,\r\n        noAny: true,\r\n        widgetProps: {\r\n            // make sure to add mode as multiple when its for quick\r\n            mode: 'multiple',\r\n            optionFilterProp: 'children',\r\n        },\r\n        options: [\r\n            { label: 'Yes', value: true },\r\n            { label: 'No', value: false },\r\n        ],\r\n    };\r\n};\r\n\r\nexport const getShowPincodeFrFilter = () => {\r\n    return {\r\n        key: 'is_pincode',\r\n        label: 'Pincode Available',\r\n        placeholder: 'Select..',\r\n        widget: 'select',\r\n        quick: true,\r\n        widgetProps: {\r\n            // make sure to add mode as multiple when its for quick\r\n            mode: 'multiple',\r\n            optionFilterProp: 'children',\r\n        },\r\n        options: [\r\n            { label: 'Any', value: '-1', color: 'gx-bg-grey' },\r\n            { label: 'Yes', value: true },\r\n            { label: 'No', value: false },\r\n        ],\r\n    };\r\n};\r\nexport const getAgingFiltersFrBrand = () => {\r\n    return [\r\n        {\r\n            key: 'stages',\r\n            label: 'Ageing - Status',\r\n            widget: 'select',\r\n            options: [],\r\n        },\r\n        {\r\n            key: 'days_spent',\r\n            label: 'Ageing - Days Spend',\r\n            widget: 'select',\r\n            widgetProps: { mode: 'multiple', optionFilterProp: 'children' },\r\n            options: [],\r\n            noAny: true,\r\n        },\r\n        {\r\n            key: 'show_all',\r\n            label: 'Ageing - Show All',\r\n            widget: 'select',\r\n            options: [\r\n                { value: true, label: 'Yes' },\r\n                { value: false, label: 'No' },\r\n            ],\r\n            noAny: true,\r\n        },\r\n    ];\r\n};\r\nexport const getNoOfTasksObjectFrFilter = () => {\r\n    let numberOfTasks = 5;\r\n    let possibleTasksOptions = [];\r\n    possibleTasksOptions = getOptionsListFrPossibleNumbers(\r\n        numberOfTasks,\r\n        (number) => ({\r\n            color: getColorInBetweenByPercentage(\r\n                [255, 72, 0], // Min color\r\n                [0, 250, 156], // Max color\r\n                number / numberOfTasks\r\n            ),\r\n        })\r\n    ).filter((item) => item.value != 0);\r\n\r\n    let taskOptionGreaterThanFiveObj = {\r\n        label: '> 5',\r\n        value: '>5',\r\n        color: 'rgb(255,0,0)',\r\n    };\r\n    possibleTasksOptions.push(taskOptionGreaterThanFiveObj);\r\n    return {\r\n        key: 'no_of_tasks',\r\n        label: 'No Of Tasks',\r\n        placeholder: 'Select..',\r\n        widget: 'select',\r\n        quick: true,\r\n        widgetProps: {\r\n            // make sure to add mode as multiple when its for quick\r\n            mode: 'multiple',\r\n            optionFilterProp: 'children',\r\n            showSearch: true,\r\n        },\r\n        options: possibleTasksOptions,\r\n    };\r\n};\r\n\r\nconst convertDateFieldsToMoments = (form_data, fieldsMeta = undefined) => {\r\n    let returnData = form_data;\r\n    if (returnData != undefined) {\r\n        Object.keys(form_data).map((key) => {\r\n            var fieldValue = form_data[key];\r\n            if (fieldsMeta) {\r\n                let matchingMeta = fieldsMeta.filter(\r\n                    (fieldMeta) => fieldMeta.key == key\r\n                );\r\n                if (matchingMeta.length > 0) {\r\n                    let fieldMeta = matchingMeta[0];\r\n                    if (\r\n                        fieldMeta.widget &&\r\n                        typeof fieldMeta.widget === 'function' &&\r\n                        fieldMeta.widget.name == DatePicker.RangePicker.name\r\n                    ) {\r\n                        //\r\n                        if (\r\n                            isArray(fieldValue) &&\r\n                            fieldValue.length == 2 &&\r\n                            fieldMeta.widgetProps?.ranges\r\n                        ) {\r\n                            returnData[key] = [\r\n                                moment(fieldValue[0], true),\r\n                                moment(fieldValue[1], true),\r\n                            ];\r\n                        }\r\n                    } else if (fieldMeta.widget == 'date-picker') {\r\n                        // console.log('value',value);\r\n                        if (fieldValue && fieldValue != '') {\r\n                            var date = moment(fieldValue, true);\r\n                            if (date.isValid()) {\r\n                                // console.log('Valid date field converted to moment',key,date)\r\n                                returnData[key] = date;\r\n                            } else {\r\n                                returnData[key] = moment();\r\n                            }\r\n                        } else {\r\n                            returnData[key] = undefined;\r\n                        }\r\n                    } else if (\r\n                        fieldMeta.cust_widget === 'TimePicker' ||\r\n                        (typeof fieldMeta.widget === 'function' && fieldMeta.widget.name === 'TimePicker')\r\n                    ) {\r\n                        if (!fieldValue) returnData[key] = null;\r\n                        else {\r\n                            let parsedTime;\r\n                            if (moment.isMoment(fieldValue)) {\r\n                                parsedTime = fieldValue;\r\n                            } else {\r\n                                // Try multiple time formats including ones with spaces\r\n                                parsedTime = moment(fieldValue, [\r\n                                    'h:mm A',    // 2:30 PM\r\n                                    'hh:mm A',   // 02:30 PM\r\n                                    'h:mm  A',   // 2:30  PM (double space)\r\n                                    'hh:mm  A',  // 02:30  PM (double space)\r\n                                    'h:mmA',     // 2:30PM (no space)\r\n                                    'hh:mmA',    // 02:30PM (no space)\r\n                                    'H:mm',      // 14:30 (24-hour)\r\n                                    'HH:mm',     // 14:30 (24-hour)\r\n                                    moment.ISO_8601\r\n                                ], true);\r\n                            }\r\n\r\n                            returnData[key] = parsedTime?.isValid() ? parsedTime.format('hh:mm A') : null;\r\n                        }\r\n                    }\r\n\r\n                }\r\n            } else {\r\n                // Legacy\r\n                if (typeof fieldValue === 'string') {\r\n                    var date = moment(fieldValue, true);\r\n                    if (date.isValid()) {\r\n                        // console.log('Valid date field converted to moment',key,date)\r\n                        returnData[key] = date;\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return returnData;\r\n};\r\n\r\nconst isTimePassed = (sql_timestamp) => {\r\n    // console.log(sql_timestamp);\r\n    let returnData = false;\r\n    if (sql_timestamp != undefined) {\r\n        var momentUtc = moment.utc(sql_timestamp);\r\n        // console.log(moment.utc(),moment.utc(sql_timestamp));\r\n        if (momentUtc.valueOf() < moment().utc().valueOf()) {\r\n            returnData = true;\r\n        }\r\n    }\r\n    return returnData;\r\n};\r\nexport const convertMomentToLocalDateString = (moment_date) => {\r\n    return moment_date?.local().format('MMM-DD-YYYY ');\r\n};\r\n\r\nexport const getCustomPrefixNameFrUploadingFiles = () => {\r\n    let userName = ConfigHelpers.getFullUserName();\r\n    let currentDate = convertMomentToDateString(moment.utc());\r\n    return `${userName}_${currentDate}`;\r\n};\r\n\r\nexport const convertMomentToDateString = (moment_date) => {\r\n    return moment_date?.local().format('YYYY-MM-DD');\r\n};\r\n\r\nexport const getCurrentDay = () => {\r\n    var date = moment();\r\n    return date.local().format('YYYY-MM-DD');\r\n};\r\n\r\nexport const getCurrentDateAndTimeFrDisplay = () => {\r\n    var date = moment();\r\n    return (\r\n        date.local().format('MMM-DD-YYYY ') +\r\n        date\r\n            .toDate()\r\n            .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    );\r\n};\r\n\r\nconst convertUTCToDisplayTime = (sql_timestamp, just_date, just_time) => {\r\n    let returnData = '';\r\n    if (sql_timestamp != undefined) {\r\n        if (typeof sql_timestamp === 'string') {\r\n            var date = moment.utc(sql_timestamp);\r\n            if (date.isValid()) {\r\n                if (just_date) {\r\n                    returnData = date.local().format('MMM-DD-YYYY ');\r\n                } else if (just_time) {\r\n                    returnData = date.local().format('h:mm A');\r\n                } else {\r\n                    returnData =\r\n                        date.local().format('MMM-DD-YYYY ') +\r\n                        date.toDate().toLocaleTimeString([], {\r\n                            hour: '2-digit',\r\n                            minute: '2-digit',\r\n                        });\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return returnData;\r\n};\r\n\r\nconst getTouchedFieldsValueInForm = (\r\n    form_data,\r\n    formInstance,\r\n    touchFieldsKeys = []\r\n) => {\r\n    let returnFields = {};\r\n    if (formInstance) {\r\n        let fieldKeys = Object.keys(form_data);\r\n        fieldKeys.forEach((singleKey) => {\r\n            if (\r\n                formInstance.isFieldTouched(singleKey) ||\r\n                touchFieldsKeys.includes(singleKey)\r\n            ) {\r\n                returnFields[singleKey] = form_data[singleKey];\r\n                const fieldValue = form_data[singleKey];\r\n                if (fieldValue === undefined) {\r\n                    // If the field value is undefined, check if it's a touched field\r\n                    if (touchFieldsKeys.includes(singleKey)) {\r\n                        returnFields[singleKey] = null; // Explicitly set to null to ensure it's included in the update\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    } else {\r\n        returnFields = form_data;\r\n        console.log(\r\n            'getTouchedFieldsValueInForm error',\r\n            'Form instance undefined, returning all fields'\r\n        );\r\n    }\r\n    return returnFields;\r\n};\r\n\r\nfunction durationAsString(start) {\r\n    const duration = moment.duration(moment.utc().diff(moment.utc(start)));\r\n\r\n    //Get Days\r\n    const days = Math.floor(duration.asDays()); // .asDays returns float but we are interested in full days only\r\n    const daysFormatted = days ? `${days}d ` : ''; // if no full days then do not display it at all\r\n\r\n    //Get Hours\r\n    const hours = duration.hours();\r\n    const hoursFormatted = hours > 0 ? `${hours}h ` : '';\r\n\r\n    //Get Minutes\r\n    const minutes = duration.minutes();\r\n    const minutesFormatted = `${minutes}m`;\r\n\r\n    return [daysFormatted, hoursFormatted, minutesFormatted].join('');\r\n}\r\n\r\n// console.log(generateRandomKey(5));\r\nfunction getLinktoObject(url, params) {\r\n    let search = '';\r\n    let urlSearch = new URLSearchParams();\r\n    for (const key in params) {\r\n        if (Object.hasOwnProperty.call(params, key)) {\r\n            const element = params[key];\r\n            let value = element;\r\n            if (typeof element == 'object') {\r\n                value = JSON.stringify(element);\r\n            }\r\n            urlSearch.append(key, value);\r\n        }\r\n    }\r\n    return { pathname: url, search: urlSearch.toString() };\r\n}\r\n\r\nfunction getGeneralFileSection(required) {\r\n    return { key: 'general', title: 'Attachments', required };\r\n}\r\n\r\nfunction hasAnyFileChanged(newFilesBySection, oldFilesBySection) {\r\n    let hasChanged = false;\r\n    // debugger;\r\n    if (oldFilesBySection == undefined && newFilesBySection) {\r\n        hasChanged = true;\r\n    } else {\r\n        let sections = Object.keys(newFilesBySection);\r\n\r\n        sections.map((singleKey) => {\r\n            let newFilesFrSection = newFilesBySection[singleKey];\r\n            let oldFilesFrSection = oldFilesBySection[singleKey];\r\n            if (\r\n                oldFilesFrSection == undefined ||\r\n                newFilesFrSection == undefined\r\n            ) {\r\n                hasChanged = true;\r\n            } else {\r\n                oldFilesFrSection.map((oldFile) => {\r\n                    if (!newFilesFrSection.includes(oldFile)) {\r\n                        hasChanged = true;\r\n                    }\r\n                });\r\n                newFilesFrSection.map((newFile) => {\r\n                    if (!oldFilesFrSection.includes(newFile)) {\r\n                        hasChanged = true;\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    return hasChanged;\r\n}\r\n\r\nexport const ExcelDateToJSDate = (serial) => {\r\n    var isANumber = isNaN(serial) === false;\r\n    if (isANumber) {\r\n        var utc_days = Math.floor(serial - 25569);\r\n        var utc_value = utc_days * 86400;\r\n        var date_info = new Date(utc_value * 1000);\r\n\r\n        var fractional_day = serial - Math.floor(serial) + 0.0000001;\r\n\r\n        var total_seconds = Math.floor(86400 * fractional_day);\r\n\r\n        var seconds = total_seconds % 60;\r\n\r\n        total_seconds -= seconds;\r\n\r\n        var hours = Math.floor(total_seconds / (60 * 60));\r\n        var minutes = Math.floor(total_seconds / 60) % 60;\r\n\r\n        return new Date(\r\n            date_info.getFullYear(),\r\n            date_info.getMonth(),\r\n            date_info.getDate(),\r\n            hours,\r\n            minutes,\r\n            seconds\r\n        );\r\n    } else {\r\n        // - / and 0 to 9\r\n        var regexp = /[^0-9-\\/]+/gi;\r\n        var cleanDateStr = serial?.replace(regexp, '');\r\n        var momentDate = moment(cleanDateStr, 'YYYY-MM-DD', true);\r\n        return momentDate.isValid() ? Date.parse(cleanDateStr) : undefined;\r\n    }\r\n};\r\n\r\nexport const decodeAntdFormErrorsToString = (errObject) => {\r\n    let finalString = [];\r\n    errObject.errorFields.map(({ errors }) => {\r\n        errors.map((singleError) => {\r\n            finalString.push(singleError);\r\n        });\r\n    });\r\n    return finalString.join(',');\r\n};\r\nexport const getLabelFrmOptionsValue = (options, value) => {\r\n    let returnLabel = value;\r\n    if (Array.isArray(options)) {\r\n        if (Array.isArray(value)) {\r\n            // If the value is an array, map over it and get the corresponding labels\r\n            returnLabel = value\r\n                .map((singleValue) => {\r\n                    const matchingOption = options.find(\r\n                        (singleOption) => singleOption.value === singleValue\r\n                    );\r\n                    return matchingOption ? matchingOption.label : singleValue;\r\n                })\r\n                .join(', ');\r\n        } else {\r\n            // If the value is not an array, get the label for the single value\r\n            options.map((singleOption) => {\r\n                if (singleOption.value == value) {\r\n                    returnLabel = singleOption.label;\r\n                }\r\n            });\r\n        }\r\n    }\r\n    return returnLabel;\r\n};\r\n\r\nconst addDaysToDate = function (date, days) {\r\n    date.setDate(date.getDate() + days);\r\n    return date;\r\n};\r\n\r\nexport const getDayName = (dateStr, locale) => {\r\n    var date = new Date(dateStr);\r\n    return date.toLocaleDateString(locale, { weekday: 'long' });\r\n};\r\n\r\nexport const getDaysBetweenMoments = (\r\n    startDate,\r\n    stopDate,\r\n    inclusive = false\r\n) => {\r\n    return getDaysBetweenDates(\r\n        startDate?.toDate(),\r\n        stopDate?.toDate(),\r\n        inclusive\r\n    );\r\n};\r\nexport const getDaysBetweenDates = (startDate, stopDate, inclusive = false) => {\r\n    console.log('startDate', startDate, stopDate);\r\n    var dateArray = new Array();\r\n    var currentDate = startDate;\r\n    while (currentDate <= stopDate) {\r\n        dateArray.push(convertMomentToDateString(moment(currentDate)));\r\n        currentDate = addDaysToDate(currentDate, 1);\r\n    }\r\n    if (!inclusive && dateArray.length > 0) {\r\n        dateArray.shift();\r\n        dateArray.pop();\r\n    }\r\n    return dateArray;\r\n};\r\n\r\nexport const getPresetRangesForFutureSpans = () => {\r\n    return {\r\n        Today: [moment(), moment()],\r\n        Today: [moment(), moment()],\r\n        Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],\r\n        'Next 2 days': [moment().add(1, 'days'), moment().add(2, 'days')],\r\n        'Next 3 days': [moment().add(1, 'days'), moment().add(3, 'days')],\r\n        'Next 4 days': [moment().add(1, 'days'), moment().add(4, 'days')],\r\n        'Next 5 days': [moment().add(1, 'days'), moment().add(5, 'days')],\r\n        'Next 6 days': [moment().add(1, 'days'), moment().add(6, 'days')],\r\n        'Next 7 days': [moment().add(1, 'days'), moment().add(7, 'days')],\r\n        'Next 2 weeks': [moment().add(1, 'days'), moment().add(14, 'days')],\r\n        'Next month': [moment().add(1, 'days'), moment().add(31, 'days')],\r\n    };\r\n};\r\n\r\nexport const getPresetRangesForRangeDatePicker = () => {\r\n    return {\r\n        Today: [moment(), moment()],\r\n        Yesterday: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],\r\n        'Last 7 days': [moment().subtract(7, 'days'), moment()],\r\n        'Last week': [\r\n            moment().subtract(1, 'weeks').startOf('week'),\r\n            moment().subtract(1, 'weeks').endOf('week'),\r\n        ],\r\n        'Month to date': [moment().startOf('month'), moment()],\r\n        'Previous Month': [\r\n            moment().subtract(1, 'months').startOf('month'),\r\n            moment().subtract(1, 'months').endOf('month'),\r\n        ],\r\n        'Year to Date': [moment().startOf('year'), moment()],\r\n    };\r\n};\r\n\r\nexport const setPageTitleAndFavIcon = (title, icon) => {\r\n    const favicon = document.getElementById('favicon');\r\n    if (icon && icon != '') {\r\n        favicon.href = icon;\r\n    }\r\n    const html_title = document.getElementById('html_title');\r\n    if (title && title != '') {\r\n        html_title.innerHTML = title;\r\n    }\r\n};\r\n\r\nexport const getSearchInLinkAsObject = () => {\r\n    let searchParams = new URLSearchParams(\r\n        document.location.search.substring(1)\r\n    );\r\n    let paramsObject = {};\r\n    searchParams.forEach((value, key) => (paramsObject[key] = value));\r\n    // message.info(JSON.stringify(paramsObject));\r\n    return paramsObject;\r\n};\r\n\r\n// http://jsfiddle.net/vksn3yLL/\r\nexport const getColorInBetweenByPercentage = (color1, color2, percentage) => {\r\n    var p = percentage;\r\n    var w = p * 2 - 1;\r\n    var w1 = (w / 1 + 1) / 2;\r\n    var w2 = 1 - w1;\r\n    var rgb = [\r\n        Math.round(color1[0] * w1 + color2[0] * w2),\r\n        Math.round(color1[1] * w1 + color2[1] * w2),\r\n        Math.round(color1[2] * w1 + color2[2] * w2),\r\n    ];\r\n    return 'rgb(' + rgb.join() + ')';\r\n};\r\n\r\nexport const getOptionsListFrPossibleNumbers = (\r\n    maxNumber,\r\n    customParamsCallback\r\n) => {\r\n    let returnOptionsArray = [];\r\n    if (maxNumber) {\r\n        let optionsArray = Array.from(Array(maxNumber + 1).keys());\r\n        optionsArray = optionsArray.map((value, index) => {\r\n            let customParams = {};\r\n            if (customParamsCallback) {\r\n                customParams = customParamsCallback(index);\r\n            }\r\n\r\n            return { label: '' + index, value: index, ...customParams };\r\n        });\r\n        // console.log(\"optionsArray\",optionsArray)\r\n        returnOptionsArray.push(...optionsArray);\r\n    }\r\n    return returnOptionsArray;\r\n};\r\n\r\nexport const getViewModeMetaFrFormMeta = (fieldsMeta, form_data) => {\r\n    //Remove dublicate key from json\r\n    fieldsMeta = fieldsMeta.filter(\r\n        (listItem, index, self) =>\r\n            self.map((itm) => itm.key).indexOf(listItem.key) === index\r\n    );\r\n\r\n    let formMetaFrDisplay = [];\r\n    fieldsMeta.forEach((singleFieldMeta) => {\r\n        let key = singleFieldMeta.key;\r\n        if (form_data[key]) {\r\n            delete singleFieldMeta['colSpan'];\r\n            if (singleFieldMeta.widget == 'date-picker') {\r\n                singleFieldMeta.renderView = (value) => (\r\n                    <div>{moment.utc(value).format('MMM Do YYYY')}</div>\r\n                );\r\n            } else if (singleFieldMeta.widget == 'select') {\r\n                singleFieldMeta.renderView = (value) => (\r\n                    <span>\r\n                        {getLabelFrmOptionsValue(\r\n                            singleFieldMeta.options,\r\n                            value\r\n                        )}\r\n                    </span>\r\n                );\r\n            } else if (singleFieldMeta.widget?.displayName == 'Rate') {\r\n                singleFieldMeta.renderView = (value) => (\r\n                    <Rate value={(singleFieldMeta.options, value)} disabled />\r\n                );\r\n            } else if (singleFieldMeta.cust_widget == 'TimePicker' || singleFieldMeta.widget?.name == 'TimePicker') {\r\n                singleFieldMeta.renderView = (value) => {\r\n                    // Always display TimePicker values in hh:mm A format\r\n                    let displayText = '';\r\n                    if (moment.isMoment(value)) {\r\n                        displayText = value.format('hh:mm A');\r\n                    } else if (value && typeof value === 'string') {\r\n                        const parsedTime = moment(value, ['h:mm A', 'hh:mm A', 'H:mm', 'HH:mm'], true);\r\n                        displayText = parsedTime.isValid() ? parsedTime.format('hh:mm A') : value;\r\n                    }\r\n                    return <span>{displayText}</span>;\r\n                };\r\n            } else if (singleFieldMeta.widget == 'radio-group') {\r\n                singleFieldMeta.renderView = (value) => (\r\n                    <span>\r\n                        {getLabelFrmOptionsValue(\r\n                            singleFieldMeta.options,\r\n                            value\r\n                        )}\r\n                    </span>\r\n                );\r\n            } else if (singleFieldMeta.widget == 'checkbox-group') {\r\n                singleFieldMeta.renderView = (value) => (\r\n                    <span>\r\n                        {getLabelFrmOptionsValue(\r\n                            singleFieldMeta.options,\r\n                            value\r\n                        )}\r\n                    </span>\r\n                );\r\n            } else if (singleFieldMeta.widget) {\r\n                let value = form_data[key]; // check if label in value\r\n                if (value.label) {\r\n                    singleFieldMeta.renderView = (value) => (\r\n                        <span>{value.label}</span>\r\n                    );\r\n                }\r\n            } else if (singleFieldMeta.key == 'comment') {\r\n                singleFieldMeta.renderView = (value) => {\r\n                    return value;\r\n                };\r\n            }\r\n            // key exists in form_data so this field needs to be displayed\r\n            formMetaFrDisplay.push(singleFieldMeta);\r\n        }\r\n    });\r\n\r\n    return formMetaFrDisplay;\r\n};\r\n\r\nexport const generateUUID = () => {\r\n    return uuidv4();\r\n};\r\n\r\nexport const NoData = () => {\r\n    return <Empty data-testid=\"no-data\" image={Empty.PRESENTED_IMAGE_SIMPLE} />;\r\n};\r\n\r\nexport const getRandomHexBgColor = () => {\r\n    var arrayOfColors = [\r\n        '#008000',\r\n        '#FFA500',\r\n        '#800080',\r\n        '#FFBF00',\r\n        '#ADD8E6',\r\n        '#0000FF',\r\n        '#00FFFF',\r\n        '#704214',\r\n        '#FFC0CB',\r\n    ];\r\n    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];\r\n};\r\n\r\nexport const getColorCodeFrReqDate = (category) => {\r\n    var colorFrReqDate = {\r\n        TODAY: '#008080',\r\n        UPCOMING: '#FFA500',\r\n        OVERDUE: '#FF0000',\r\n        UNSPECIFIED: '#808080',\r\n    };\r\n    return colorFrReqDate[category] ? colorFrReqDate[category] : '#e1e1e1';\r\n};\r\n\r\nexport const isAndroidApp = () => {\r\n    const user_agent = navigator?.userAgent;\r\n    console.log('user_agent', user_agent);\r\n\r\n    return user_agent.includes('||TMS_APP');\r\n};\r\n\r\nexport const areArraysSame = (a1, a2) => {\r\n    /* WARNING: arrays must not contain {objects} or behavior may be undefined */\r\n    return JSON.stringify(a1) == JSON.stringify(a2);\r\n};\r\n\r\nexport const handleClearSelect = (value, formRef, key) => {\r\n    let fieldKey = {};\r\n    fieldKey[key] = '';\r\n    if (value == undefined) {\r\n        if (formRef?.current?.setFieldsValue) {\r\n            formRef.current.setFieldsValue(fieldKey);\r\n        }\r\n    }\r\n};\r\n\r\nexport const setRemarkFieldAsNoRemarkIfEmpty = (prefillFormData, formRef) => {\r\n    if (formRef?.current) {\r\n        const remark = formRef?.current?.getFieldValue('remarks');\r\n        if (prefillFormData?.remarks == undefined && remark == undefined) {\r\n            formRef.current.setFieldsValue({ remarks: 'No remarks' });\r\n        }\r\n    } else {\r\n        setTimeout(() => {\r\n            setRemarkFieldAsNoRemarkIfEmpty(prefillFormData);\r\n        }, 100);\r\n    }\r\n};\r\n\r\nexport const getCustomVarMetaFrTemplate = (\r\n    selected_custom_fields_meta,\r\n    full_prefix,\r\n    custom_fields_meta,\r\n    filledCustomVars,\r\n    formRef,\r\n    refresh\r\n) => {\r\n    if (selected_custom_fields_meta?.length > 0) {\r\n        selected_custom_fields_meta.forEach(\r\n            (single_selected_custom_fields_meta_) => {\r\n                let copy_field = { ...single_selected_custom_fields_meta_ };\r\n                copy_field.key =\r\n                    full_prefix + '_' + single_selected_custom_fields_meta_.key;\r\n                copy_field['onChange'] = () => refresh();\r\n                custom_fields_meta.push(copy_field);\r\n                filledCustomVars[\r\n                    `%${single_selected_custom_fields_meta_.key}%`\r\n                ] = formRef?.current?.getFieldValue(copy_field.key);\r\n            }\r\n        );\r\n    }\r\n};\r\n\r\n//handles null value for date filters\r\nexport const handleFilterClearDateIfNull = (filters) => {\r\n    let keys = Object.keys(filters);\r\n    for (let i = 0; i < keys.length; i++) {\r\n        let filterKey = keys[i];\r\n        if (filters[filterKey] == null || filters[filterKey] == undefined) {\r\n            delete filters[filterKey];\r\n        }\r\n    }\r\n    return filters;\r\n};\r\n\r\nexport function commaSeparatedNumberForDashboard(number) {\r\n    try {\r\n        if (number >= 0) {\r\n            const parts = number?.toLocaleString('en-IN').split('.');\r\n            const formatted = parts[0].replace(/(\\d)(?=(\\d\\d)+\\d$)/g, '$1,');\r\n            if (parts.length === 2) {\r\n                return `${formatted}.${parts[1]}`;\r\n            } else {\r\n                return formatted;\r\n            }\r\n        }\r\n    } catch (e) {\r\n        return number;\r\n    }\r\n}\r\n\r\nexport function convertToDecimalPrefix(number) {\r\n    const prefixes = ['', 'k', 'L', 'Cr', 'Ar', 'Mo', 'Br', 'Tr', 'Qu'];\r\n    let exponent = 0;\r\n    if (number >= 10000000) {\r\n        exponent = 3; // Crore\r\n        number = number / 10000000;\r\n    } else if (number >= 100000) {\r\n        exponent = 2; // Lakh\r\n        number = number / 100000;\r\n    } else if (number >= 1000) {\r\n        exponent = 1; // Thousand\r\n        number = number / 1000;\r\n    }\r\n    if (exponent > 0) {\r\n        // Check if the number is a whole number\r\n        if (Number.isInteger(number)) {\r\n            return number + prefixes[exponent];\r\n        } else {\r\n            return number.toFixed(2) + prefixes[exponent];\r\n        }\r\n    }\r\n    return number;\r\n}\r\n\r\nexport const getReminders = (\r\n    punch_in_time_fr_org,\r\n    first_reminder_fr_org,\r\n    factor\r\n) => {\r\n    punch_in_time_fr_org =\r\n        punch_in_time_fr_org.slice(0, -2) +\r\n        ' ' +\r\n        punch_in_time_fr_org.slice(-2);\r\n    function generateReminders(punchInTime, reminderTime) {\r\n        var notifications = [];\r\n\r\n        // Convert punchInTime to minutes\r\n        var punchInMinutes = getMinutes(punchInTime);\r\n\r\n        // First reminder\r\n        var reminderMinutes = punchInMinutes - reminderTime;\r\n        var reminder = formatTime(reminderMinutes);\r\n        notifications.push({\r\n            reminderTime: reminder,\r\n            reminderMinutes: reminderTime,\r\n        });\r\n\r\n        // Generate subsequent notifications\r\n        while (reminderTime > 1) {\r\n            reminderTime = Math.ceil(reminderTime / factor);\r\n            var notificationMinutes = punchInMinutes - reminderTime;\r\n            var notification = formatTime(notificationMinutes);\r\n            notifications.push({\r\n                reminderTime: notification,\r\n                reminderMinutes: reminderTime,\r\n            });\r\n        }\r\n\r\n        function getMinutes(time) {\r\n            var [hours, minutes, period] = time\r\n                .match(/^(\\d+):(\\d+)\\s+(AM|PM)$/)\r\n                .slice(1);\r\n            hours = parseInt(hours);\r\n            minutes = parseInt(minutes);\r\n\r\n            if (period === 'PM' && hours !== 12) {\r\n                hours += 12;\r\n            } else if (period === 'AM' && hours === 12) {\r\n                hours = 0;\r\n            }\r\n\r\n            return hours * 60 + minutes;\r\n        }\r\n\r\n        // Helper function to format minutes to time string\r\n        function formatTime(minutes) {\r\n            var hours = Math.floor(minutes / 60) % 12;\r\n            var mins = minutes % 60;\r\n            var period = Math.floor(minutes / 60) >= 12 ? 'PM' : 'AM';\r\n            hours = hours === 0 ? 12 : hours;\r\n\r\n            return padZero(hours) + ':' + padZero(mins) + ' ' + period;\r\n        }\r\n\r\n        // Helper function to pad single digit numbers with leading zero\r\n        function padZero(number) {\r\n            return number.toString().padStart(2, '0');\r\n        }\r\n        return notifications;\r\n    }\r\n\r\n    function timeStringToDate(timeStr) {\r\n        const [time, meridiem] = timeStr.split(' ');\r\n        let [hours, minutes] = time.split(':');\r\n        hours = parseInt(hours, 10);\r\n        minutes = parseInt(minutes, 10);\r\n\r\n        if (meridiem === 'PM' && hours !== 12) {\r\n            hours += 12;\r\n        } else if (meridiem === 'AM' && hours === 12) {\r\n            hours = 0;\r\n        }\r\n        const timeInMillis = new Date(0, 0, 0, hours, minutes).getTime();\r\n        return timeInMillis;\r\n    }\r\n    let reminders = generateReminders(\r\n        punch_in_time_fr_org,\r\n        first_reminder_fr_org\r\n    );\r\n    return reminders;\r\n};\r\n\r\nexport const convertValuesOfObjToMoment = (obj) => {\r\n    if (obj) {\r\n        Object.keys(obj).map((key) => {\r\n            const value = obj[key];\r\n            if (isValidDate(value)) {\r\n                //  debugger;\r\n                const momentDate = moment(value, true);\r\n                if (momentDate.isValid()) {\r\n                    obj[key] = momentDate;\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    return obj;\r\n};\r\nfunction isTimestampDateFormat(inputString) {\r\n    return !isNaN(Date.parse(inputString));\r\n}\r\n\r\nfunction isValidDate(dateString) {\r\n    try {\r\n        var regEx = /^\\d{4}-\\d{2}-\\d{2}$/;\r\n        if (!dateString.match(regEx)) return false; // Invalid format\r\n        var d = new Date(dateString);\r\n        var dNum = d.getTime();\r\n        if (!dNum && dNum !== 0) return false; // NaN value, Invalid date\r\n        return d.toISOString().slice(0, 10) === dateString;\r\n    } catch (error) {\r\n        return false;\r\n    }\r\n}\r\n\r\nexport const getDataForLastThreeMonths = (keys, filter) => {\r\n    console.log('active', keys, filter);\r\n    const endDate = moment();\r\n    const startDate = moment().subtract(5, 'months');\r\n    filter[keys] = [startDate, endDate].map((date) => date.toISOString());\r\n    return filter;\r\n};\r\n\r\nexport const getSkillLevelOptions = (isSrvcPrvdr = false) => {\r\n    let skillLevelPrefix = isSrvcPrvdr ? 'vertical_level' : 'srvc_type_level';\r\n    return [\r\n        { value: `${skillLevelPrefix}_1`, label: '1(Beginner)' },\r\n        { value: `${skillLevelPrefix}_2`, label: '2' },\r\n        { value: `${skillLevelPrefix}_3`, label: '3' },\r\n        { value: `${skillLevelPrefix}_4`, label: '4' },\r\n        { value: `${skillLevelPrefix}_5`, label: '5(Expert)' },\r\n    ];\r\n};\r\nexport const getLabel = (key, options) => {\r\n    let label;\r\n    if (options?.length > 0) {\r\n        options.forEach((singleOption) => {\r\n            if (singleOption.value == key) {\r\n                label = singleOption.label;\r\n            }\r\n        });\r\n    }\r\n    return label;\r\n};\r\n\r\nexport const isTodayDate = (dateString) => {\r\n    var givenDate = new Date(dateString);\r\n    var currentDate = new Date();\r\n    return givenDate.toDateString() === currentDate.toDateString();\r\n};\r\n\r\nexport const isYesterdayDate = (dateString) => {\r\n    var givenDate = new Date(dateString);\r\n    var currentDate = new Date();\r\n    var yesterday = new Date();\r\n    yesterday.setDate(currentDate.getDate() - 1);\r\n    return givenDate.toDateString() === yesterday.toDateString();\r\n};\r\n\r\nexport const validateCronFrequency = (rule, value, callback) => {\r\n    // This is a basic pattern for validating cron expressions\r\n    // It covers typical cron patterns but might need to be adjusted for more complex cases\r\n    const cronPattern =\r\n        /^(\\*|([0-5]?\\d)) (\\*|([0-5]?\\d)) (\\*|1?\\d|2[0-3]) (\\*|[1-9]|[12]\\d|3[01]) (\\*|[1-9]|1[0-2])$/;\r\n\r\n    if (!value || cronPattern.test(value)) {\r\n        callback(); // Validation passed\r\n    } else {\r\n        callback('Please enter a valid cron frequency (* * * * *)'); // Validation failed\r\n    }\r\n};\r\n\r\nexport const validateLambdaArn = (rule, value, callback) => {\r\n    const lambdaArnPattern =\r\n        /^arn:aws:lambda:[a-z\\d-]+:\\d{12}:function:[a-zA-Z0-9-_]+$/;\r\n    if (!value || lambdaArnPattern.test(value)) {\r\n        callback(); // Validation passed\r\n    } else {\r\n        callback('Please enter a valid Lambda ARN'); // Validation failed\r\n    }\r\n};\r\n\r\nexport const getRatingFrFilter = () => {\r\n    let numberOfRatingValue = 5;\r\n    let possibleRatingOptions = [];\r\n    possibleRatingOptions = getOptionsListFrPossibleNumbers(\r\n        numberOfRatingValue,\r\n        (number) => ({\r\n            color: getColorInBetweenByPercentage(\r\n                [255, 72, 0], // Min color\r\n                [0, 250, 156], // Max color\r\n                number / numberOfRatingValue\r\n            ),\r\n        })\r\n    ).filter((item) => item.value != 0);\r\n\r\n    return {\r\n        key: 'rating_value',\r\n        label: 'Rating',\r\n        placeholder: 'Select..',\r\n        widget: 'select',\r\n        quick: true,\r\n        widgetProps: {\r\n            // make sure to add mode as multiple when its for quick\r\n            mode: 'multiple',\r\n            optionFilterProp: 'children',\r\n            showSearch: true,\r\n        },\r\n        options: possibleRatingOptions,\r\n    };\r\n};\r\nconst isMobileView = () => {\r\n    return window.innerWidth <= 768;\r\n};\r\nconst isScreenZoomPercentage125 = () => {\r\n    return window.devicePixelRatio == 1.25;\r\n};\r\n\r\nexport const getMapAddressFieldsMeta = ({\r\n    form_data,\r\n    formRef,\r\n    forceUpdateFn,\r\n    showPickOnMapModel,\r\n    togglePickOnMapModel,\r\n    is_pincode_mandatory,\r\n    orgSettingsData,\r\n    prefix,\r\n}) => {\r\n    const filledAddress =\r\n        getConcatenatedAddressFrmForm(prefix, formRef) ||\r\n        getConcatenatedAddressFormData(prefix, form_data, formRef);\r\n    const showClearFieldsButton = !!filledAddress;\r\n    const latitude =\r\n        formRef?.current?.getFieldValue('location_latitude') ||\r\n        form_data?.location_latitude;\r\n    const longitude =\r\n        formRef?.current?.getFieldValue('location_Longitude') ||\r\n        form_data?.location_Longitude;\r\n\r\n    const onLatLngValueChange = async () => {\r\n        const latitude = formRef?.current?.getFieldValue('location_latitude');\r\n        const longitude = formRef?.current?.getFieldValue('location_Longitude');\r\n        if (latitude && longitude) {\r\n            const data = await getAddressBasedOnLatAndLng(latitude, longitude);\r\n            addressFill(getAddressObj(data.results?.[0]), formRef, prefix);\r\n        }\r\n    };\r\n\r\n    let clearGoogleAddressSearch = new Date().getTime();\r\n    const clearAddress = (formRef) => {\r\n        const keyEmptyValue = {};\r\n        getAddressFieldKeys(prefix).forEach((singleKey) => {\r\n            keyEmptyValue[singleKey] = '';\r\n        });\r\n        clearGoogleAddressSearch = new Date().getTime();\r\n        formRef.current.setFieldsValue(keyEmptyValue);\r\n        forceUpdateFn();\r\n    };\r\n\r\n    const meta = {\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'mark_location_on_map',\r\n                render: () => (\r\n                    <div>\r\n                        <LocationSearchInput\r\n                            placeholder=\"Address\"\r\n                            useCountryAndID={true}\r\n                            onChange={(address) => {\r\n                                addressFill(address, formRef, prefix);\r\n                                forceUpdateFn();\r\n                            }}\r\n                            orgSettingsData={orgSettingsData}\r\n                            triggerClear={clearGoogleAddressSearch}\r\n                        />\r\n                        <Button onClick={togglePickOnMapModel}>\r\n                            Pick on Map\r\n                        </Button>\r\n                        {showPickOnMapModel && (\r\n                            <MapComponent\r\n                                showPickOnMapModel={showPickOnMapModel}\r\n                                defaultLocation={{\r\n                                    lat: latitude || 22.5437692,\r\n                                    lng: longitude || 79.1230844,\r\n                                }}\r\n                                onChange={(address) => {\r\n                                    addressFill(address, formRef, prefix);\r\n                                    forceUpdateFn();\r\n                                    togglePickOnMapModel();\r\n                                }}\r\n                                togglePickOnMapModel={togglePickOnMapModel}\r\n                            />\r\n                        )}\r\n                    </div>\r\n                ),\r\n            },\r\n            {\r\n                key: 'clear_fields',\r\n                colSpan: 4,\r\n                label: 'Clear fields',\r\n                render: () =>\r\n                    showClearFieldsButton && (\r\n                        <Button\r\n                            type=\"link\"\r\n                            onClick={() => clearAddress(formRef)}\r\n                        >\r\n                            Reset Address\r\n                        </Button>\r\n                    ),\r\n            },\r\n            {\r\n                key: `${prefix}line_0`,\r\n                colSpan: 4,\r\n                label: 'Flat no',\r\n                onChange: forceUpdateFn,\r\n                rules: [{ max: 50 }],\r\n            },\r\n            {\r\n                key: `${prefix}line_1`,\r\n                colSpan: 4,\r\n                label: 'Building/Apartment name',\r\n                onChange: forceUpdateFn,\r\n                rules: [{ max: 200 }],\r\n            },\r\n            {\r\n                key: `${prefix}line_2`,\r\n                label: 'Line 1',\r\n                colSpan: 4,\r\n                disabled: true,\r\n                rules: [{ max: 1000 }],\r\n            },\r\n            {\r\n                key: `${prefix}line_3`,\r\n                label: 'Line 2',\r\n                colSpan: 4,\r\n                disabled: true,\r\n                rules: [{ max: 200 }],\r\n            },\r\n            {\r\n                key: `${prefix}pincode`,\r\n                label: 'Pincode',\r\n                colSpan: 2,\r\n                required: is_pincode_mandatory,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    params: { fn: 'getPincode' },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: { width: '100%' },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: `${prefix}city`,\r\n                label: 'City',\r\n                colSpan: 2,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    params: { fn: 'getCities' },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: { width: '100%' },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: `${prefix}state`,\r\n                label: 'State',\r\n                colSpan: 4,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    params: { fn: 'getState' },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: { width: '100%' },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: 'location_latitude',\r\n                label: 'Latitude',\r\n                colSpan: 4,\r\n                //required: !!formRef?.current?.getFieldValue('location_Longitude'),\r\n                placeholder: 'Eg 37.7749',\r\n                onChange: (e) => {\r\n                    onLatLngValueChange();\r\n                    forceUpdateFn();\r\n                },\r\n            },\r\n            {\r\n                key: 'location_Longitude',\r\n                label: 'Longitude',\r\n                colSpan: 4,\r\n                // required: !!formRef?.current?.getFieldValue('location_latitude'),\r\n                placeholder: 'Eg -122.4194',\r\n                onChange: (e) => {\r\n                    onLatLngValueChange();\r\n                    forceUpdateFn();\r\n                },\r\n            },\r\n            ...(latitude && longitude\r\n                ? [\r\n                      {\r\n                          key: 'view_location',\r\n                          render: () => {\r\n                              const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;\r\n                              return (\r\n                                  <a href={url} target=\"_blank\">\r\n                                      <i className=\"icon icon-location\"> </i>\r\n                                      View on Google Map\r\n                                  </a>\r\n                              );\r\n                          },\r\n                      },\r\n                  ]\r\n                : []),\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport {\r\n    hasAnyFileChanged,\r\n    getGeneralFileSection,\r\n    getLinktoObject,\r\n    durationAsString,\r\n    getTouchedFieldsValueInForm,\r\n    isTimePassed,\r\n    getColorCodeFrStatusCategory,\r\n    priorities,\r\n    getTextColorFrPriority,\r\n    convertUTCToDisplayTime,\r\n    convertDateFieldsToMoments,\r\n    getRandomIconByColor,\r\n    getRandomBgColor,\r\n    generateRandomKey,\r\n    getAnyObjectFrFilter,\r\n    getEmptyObjectFrFilter,\r\n    isMobileView,\r\n    isScreenZoomPercentage125,\r\n};\r\nexport const setHtmlHeadTitle = (path = '/', suffixLabel = '') => {\r\n    suffixLabel =\r\n        typeof suffixLabel === 'string'\r\n            ? suffixLabel\r\n            : String(suffixLabel || '');\r\n    path = path.replace(/\\/$/, '').trim();\r\n    const routeTitleMap = extractLinksAndLabels();\r\n    const userDetails = ConfigHelpers.getUserDetailsInLocalStorage();\r\n    const iconInConfig = userDetails?.org?.icon;\r\n    const orgNickname = userDetails?.org?.nickname || '';\r\n\r\n    // Function to find the base path by stripping dynamic segments\r\n    const getBasePath = (currentPath) => {\r\n        const segments = currentPath.split('/');\r\n        // Iteratively reduce the path to find the longest matching base path in routeTitleMap\r\n        while (segments.length > 0) {\r\n            const potentialPath = segments.join('/') || '/';\r\n            if (routeTitleMap[potentialPath]) {\r\n                return potentialPath;\r\n            }\r\n            segments.pop();\r\n        }\r\n        return '/'; // Fallback to root if no match is found\r\n    };\r\n    const basePath = getBasePath(path);\r\n    const basePageTitle = routeTitleMap[basePath] || '';\r\n\r\n    let fullTitle = `${orgNickname} ${basePageTitle}`;\r\n    // If a suffixLabel is provided, append it with a hyphen\r\n    if (suffixLabel?.trim() !== '') {\r\n        fullTitle += ` - ${suffixLabel}`;\r\n    }\r\n    fullTitle += ' TMS';\r\n\r\n    setPageTitleAndFavIcon(fullTitle, iconInConfig);\r\n};\r\n\r\nexport function extractLinksAndLabels() {\r\n    const links = document.querySelectorAll('a'); // Selecting all <a> tags in the DOM\r\n\r\n    return Array.from(links).reduce((acc, link) => {\r\n        const href = link.getAttribute('href');\r\n\r\n        const url = new URL(href, window.location.origin); // Ensure base URL if href is relative\r\n        const pathname = url.pathname; // Extract only the pathname (excluding query params and hash)\r\n\r\n        const textContent = Array.from(link.childNodes)\r\n            .filter((node) => {\r\n                return (\r\n                    (node.nodeType === Node.TEXT_NODE &&\r\n                        node.textContent.trim() !== '') ||\r\n                    node.nodeName === 'SPAN' ||\r\n                    node.nodeName === 'TAG'\r\n                );\r\n            })\r\n            .map((node) => {\r\n                // Use a regular expression to strip out numbers like '4.8 of 103'\r\n                let text = node.textContent.trim();\r\n                text = text.replace(/\\d+(\\.\\d+)?/g, '').trim(); // Remove numeric values\r\n                return text;\r\n            })\r\n            .join(' ');\r\n\r\n        if (pathname && textContent) {\r\n            acc[pathname] = textContent;\r\n        }\r\n        return acc;\r\n    }, {});\r\n}\r\nexport const WIFY_LOCAL_STROAGE_KEY = 'wify_data';\r\nexport const LOCAL_STORAGE_KEYS = {\r\n    dashboard: {\r\n        value: 'dashboard',\r\n        my_data_widget: { value: 'my_data_widget' },\r\n    },\r\n    user_settings: {\r\n        value: 'user_settings',\r\n        show_org_details: false,\r\n    },\r\n    ai_chat_bot: {\r\n        value: 'ai_chat_bot',\r\n        isOpen: false,\r\n    },\r\n};\r\n\r\nexport function writeToLocalStorage({ key, value }) {\r\n    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\r\n\r\n    // Initialize empty object if nothing is stored\r\n    if (!storedValue) {\r\n        localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify({}));\r\n    }\r\n\r\n    let updatedValue = {};\r\n    if (storedValue) {\r\n        try {\r\n            updatedValue = JSON.parse(storedValue);\r\n        } catch (error) {\r\n            console.log('Error parsing JSON from localStorage:', error);\r\n        }\r\n    }\r\n\r\n    // Initialize the key if it doesn't exist\r\n    if (!updatedValue[key]) {\r\n        updatedValue[key] = value;\r\n    } else {\r\n        // If it's already there, we want to merge/update the value based on its type\r\n        if (\r\n            typeof updatedValue[key] === 'object' &&\r\n            typeof value === 'object'\r\n        ) {\r\n            updatedValue[key] = { ...updatedValue[key], ...value }; // If both are objects, merge them\r\n        } else {\r\n            updatedValue[key] = value; // Otherwise, just replace the value\r\n        }\r\n    }\r\n\r\n    // Save the updated value back to localStorage\r\n    localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify(updatedValue));\r\n}\r\n\r\nexport const findNestedKey = (obj, searchKey) => {\r\n    if (obj === null || typeof obj !== 'object') {\r\n        return null;\r\n    }\r\n    if (obj.hasOwnProperty(searchKey)) {\r\n        return obj[searchKey];\r\n    }\r\n    for (const k in obj) {\r\n        const result = findNestedKey(obj[k], searchKey);\r\n        if (result !== null) {\r\n            return result;\r\n        }\r\n    }\r\n    return null;\r\n};\r\n\r\nexport function readFromLocalStorage({ key }) {\r\n    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\r\n\r\n    if (storedValue) {\r\n        try {\r\n            const parsedValue = JSON.parse(storedValue);\r\n            return findNestedKey(parsedValue, key);\r\n        } catch (error) {\r\n            console.log('Error parsing JSON from localStorage:', error);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    return null; // Return null if WIFY_LOCAL_STROAGE_KEY is not found\r\n}\r\nexport function deleteFromLocalStorage({ key }) {\r\n    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);\r\n\r\n    if (storedValue) {\r\n        try {\r\n            const parsedValue = JSON.parse(storedValue);\r\n\r\n            // Helper function to delete a nested key\r\n            const deleteNestedKey = (obj, searchKey) => {\r\n                if (obj === null || typeof obj !== 'object') {\r\n                    return false; // Return false if it's not an object\r\n                }\r\n\r\n                if (obj.hasOwnProperty(searchKey)) {\r\n                    delete obj[searchKey];\r\n                    return true; // Key found and deleted\r\n                }\r\n\r\n                for (const k in obj) {\r\n                    if (deleteNestedKey(obj[k], searchKey)) {\r\n                        return true; // Key found and deleted in nested object\r\n                    }\r\n                }\r\n                return false; // Key not found\r\n            };\r\n\r\n            const isDeleted = deleteNestedKey(parsedValue, key);\r\n\r\n            if (isDeleted) {\r\n                localStorage.setItem(\r\n                    WIFY_LOCAL_STROAGE_KEY,\r\n                    JSON.stringify(parsedValue)\r\n                );\r\n            } else {\r\n                console.warn(`Key \"${key}\" not found.`);\r\n            }\r\n        } catch (error) {\r\n            console.log('Error parsing JSON from localStorage:', error);\r\n        }\r\n    } else {\r\n        console.warn(`No data found in ${WIFY_LOCAL_STROAGE_KEY}.`);\r\n    }\r\n}\r\n\r\nexport const getFileExtension = (fileUrl) => {\r\n    const fileName = getFileNameFrmUrl(fileUrl);\r\n    return fileExtensionRegex.exec(fileName)[1];\r\n};\r\n\r\nexport const getFileNameFrmUrl = (fileUrl) => {\r\n    fileUrl = decodeURI(fileUrl.split('?')[0]);\r\n    return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);\r\n};\r\n\r\n// Helper function to compare two objects based on the keys of the submitted object.\r\nexport const isEqualData = (submitted, original) => {\r\n    return Object.keys(submitted).every((key) => {\r\n        const submittedValue = submitted[key];\r\n        const originalValue = original[key];\r\n\r\n        // If the value is an array, check equality with submitted original\r\n        if (Array.isArray(submittedValue) && Array.isArray(originalValue)) {\r\n            return (\r\n                JSON.stringify(submittedValue) === JSON.stringify(originalValue)\r\n            );\r\n        }\r\n\r\n        return submittedValue === originalValue;\r\n    });\r\n};\r\n\r\nexport const getUserRightsFrService = (srvc_id, userServiceAccess) => {\r\n    let foundServiceAccess = undefined;\r\n    if (userServiceAccess) {\r\n        userServiceAccess.map((singleServiceAccess) => {\r\n            if (singleServiceAccess.menu_id == srvc_id) {\r\n                foundServiceAccess = singleServiceAccess;\r\n            }\r\n        });\r\n    }\r\n    return foundServiceAccess?.rights_type;\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC/D,SAASC,OAAO,QAAQ,QAAQ;AAChC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,yBAAyB,QAAQ,+CAA+C;AACzF,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,SACIC,WAAW,EACXC,0BAA0B,EAC1BC,mBAAmB,EACnBC,6BAA6B,QAC1B,mBAAmB;AAC1B,SACIC,aAAa,EACbC,8BAA8B,QAC3B,wBAAwB;AAC/B,OAAOC,YAAY,MAAM,8CAA8C;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,MAAMC,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC3B,MAAMC,kBAAkB,GAAG,iBAAiB;AAC5C;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,eAAe,KAAK;EACxE,MAAMC,qBAAqB,GACvBJ,IAAI,KAAK,CAAC,IAAI,CAACK,2BAA2B,CAACJ,IAAI,CAACK,UAAU,EAAEJ,QAAQ,CAAC;EACzE,MAAMK,SAAS,GACXP,IAAI,KAAK,CAAC,GACJ,cAAcI,qBAAqB,GAAGD,eAAe,GAAG,EAAE,EAAE,GAC5D,EAAE;EACZ,oBAAO9B,KAAA,CAAAmC,aAAA;IAAMD,SAAS,EAAEA,SAAU;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GAAC,EAACd,IAAI,EAAC,GAAO,CAAC;AACtD,CAAC;;AAED;AACA,OAAO,MAAMK,2BAA2B,GAAGA,CAACU,GAAG,EAAEC,kBAAkB,KAAK;EAAA,IAAAC,SAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,iBAAA;EACpE,IAAIlB,QAAQ;EACZ,IAAI;IACAA,QAAQ,GAAGmB,IAAI,CAACC,KAAK,CAACN,kBAAkB,CAAC;EAC7C,CAAC,CAAC,OAAOO,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO,KAAK;EAChB;EACA,MAAME,QAAQ,IAAAR,SAAA,GAAGf,QAAQ,cAAAe,SAAA,wBAAAC,cAAA,GAARD,SAAA,CAAUS,IAAI,cAAAR,cAAA,uBAAdA,cAAA,CAAgBS,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACb,GAAG,CAAC;EAC5D,MAAMc,UAAU,IAAAV,UAAA,GAAGjB,QAAQ,cAAAiB,UAAA,wBAAAC,iBAAA,GAARD,UAAA,CAAUW,MAAM,cAAAV,iBAAA,uBAAhBA,iBAAA,CAAkBO,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACb,GAAG,CAAC;EAChE,OAAO,CAAAU,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,QAAQ,CAAChB,GAAG,CAAC,MAAIc,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEE,QAAQ,CAAChB,GAAG,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMiB,qBAAqB,GAAIC,WAAW,IAAK;EAClD,IAAIA,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI;EACf;EACA,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;;EAEd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;IACzCF,MAAM,IAAIF,WAAW,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7BD,MAAM,IAAIH,WAAW,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;EACA;EACA,IAAIC,MAAM,GAAGH,MAAM,GAAGF,WAAW,CAACC,MAAM;EACxC,IAAIK,MAAM,GAAGH,MAAM,GAAGH,WAAW,CAACC,MAAM;EAExC,OAAO,CAACI,MAAM,EAAEC,MAAM,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED,OAAO,MAAMC,2BAA2B,GAAIC,UAAU,IAAK;EACvD,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;EACvD,IAAI,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGJ,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACxCC,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;EAC7B,IAAIF,MAAM,IAAIA,MAAM,CAACK,WAAW,CAAC,CAAC,KAAK,IAAI,IAAIH,KAAK,GAAG,EAAE,EAAE;IACvDA,KAAK,IAAI,EAAE,CAAC,CAAC;EACjB,CAAC,MAAM,IAAIF,MAAM,IAAIA,MAAM,CAACK,WAAW,CAAC,CAAC,KAAK,IAAI,IAAIH,KAAK,KAAK,EAAE,EAAE;IAChEA,KAAK,GAAG,CAAC,CAAC,CAAC;EACf;EACA,OAAOA,KAAK,GAAG,EAAE,GAAGE,QAAQ,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,OAAO,SAASG,4BAA4BA,CAACC,kBAAkB,EAAEvC,QAAQ,EAAE;EACvE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI;IACA,MAAMwC,QAAQ,GAAGnE,IAAI,CAACoE,KAAK,CAACC,QAAQ,CAAC,CAAC;IACtC;IACAH,kBAAkB,CAACI,OAAO,CAAC,CAAC;MAAEC,IAAI;MAAEC;IAAM,CAAC,KAAK;MAC5C,MAAMC,SAAS,GAAGzE,IAAI,CAACoE,KAAK,CAACM,aAAa,CAACF,KAAK,CAAC;MACjDxE,IAAI,CAACoE,KAAK,CAACO,iBAAiB,CAACR,QAAQ,EAAEM,SAAS,EAAEF,IAAI,CAAC;IAC3D,CAAC,CAAC;IACF,IAAI,CAACrE,iBAAiB,CAAC,CAAC,EAAE;MACtB;MACAF,IAAI,CAAC4E,SAAS,CAACT,QAAQ,EAAExC,QAAQ,GAAG,OAAO,CAAC;IAChD,CAAC,MAAM;MACHkD,2BAA2B,CAACV,QAAQ,EAAExC,QAAQ,CAAC;IACnD;EACJ,CAAC,CAAC,OAAOY,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;EACxE;AACJ;AAEA,OAAO,SAASsC,2BAA2BA,CAACV,QAAQ,EAAExC,QAAQ,EAAE;EAC5DmD,MAAM,CAACC,OAAO,CAACC,gBAAgB,CAAC,qBAAqB,CAAC;EACtD;EACA,MAAMC,YAAY,GAAGjF,IAAI,CAACkF,KAAK,CAACf,QAAQ,EAAE;IACtCgB,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGC,IAAI,CAACL,YAAY,CAAC;EACtC,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,WAAW,CAAC,EAAE;IACjCD,IAAI,EAAE;EACV,CAAC,CAAC;EACF,MAAMK,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;EAEzC;EACAK,oBAAoB,CAACH,OAAO,EAAE,UAAUI,UAAU,EAAE;IAChD,IAAIA,UAAU,EAAE;MACZf,MAAM,CAACC,OAAO,CAACe,wBAAwB,CACnCL,OAAO,EACPI,UAAU,EACVlE,QAAQ,GAAG,OACf,CAAC;IACL,CAAC,MAAM;MACHmD,MAAM,CAACC,OAAO,CAACC,gBAAgB,CAAC,qBAAqB,CAAC;MACtDxC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAC;IAChD;EACJ,CAAC,CAAC;AACN;AAEA,SAASqD,oBAAoBA,CAACH,OAAO,EAAEM,QAAQ,EAAE;EAC7CC,KAAK,CAACP,OAAO,CAAC,CACTQ,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CACnCU,IAAI,CAAEV,IAAI,IAAK;IACZ,MAAMY,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,SAAS,GAAG,YAAY;MAC3B,MAAMR,UAAU,GAAGM,MAAM,CAACG,MAAM,CAAC1C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChDmC,QAAQ,CAACF,UAAU,CAAC;IACxB,CAAC;IACDM,MAAM,CAACI,aAAa,CAAChB,IAAI,CAAC;EAC9B,CAAC,CAAC,CACDiB,KAAK,CAAEjE,KAAK,IAAK;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChDwD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC;AACV;;AAEA;AACA,SAAST,IAAIA,CAACmB,CAAC,EAAE;EACb,MAAMC,GAAG,GAAG,IAAIC,WAAW,CAACF,CAAC,CAACvD,MAAM,CAAC;EACrC,MAAM0D,IAAI,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;EAChC,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,CAAC,CAACvD,MAAM,EAAEG,CAAC,EAAE,EAAE;IAC/BuD,IAAI,CAACvD,CAAC,CAAC,GAAGoD,CAAC,CAACK,UAAU,CAACzD,CAAC,CAAC,GAAG,IAAI;EACpC;EACA,OAAOqD,GAAG;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,kBAAkBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC1C,IAAIC,KAAK,CAACF,GAAG,CAAC,EAAE;IACZ,OAAO,EAAE;EACb;EACA,MAAMG,QAAQ,GAAG;IACbC,CAAC,EAAE,IAAI;IACPC,CAAC,EAAE,MAAM;IACTC,EAAE,EAAE;IACJ;IACA;EACJ,CAAC;EAED,IAAI,CAACH,QAAQ,CAACI,cAAc,CAACN,IAAI,CAAC,EAAE;IAChC,OAAO,cAAc;EACzB;EAEA,IAAIO,OAAO,GAAGL,QAAQ,CAACF,IAAI,CAAC;EAC5B,IAAIQ,YAAY,GAAG,CAACT,GAAG,GAAGQ,OAAO,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAGT,IAAI;EAEpD,OAAOQ,YAAY;AACvB;AAEA,OAAO,SAASE,oBAAoBA,CAACC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACzE,IAAIC,aAAa,GAAGH,aAAa,CAACN,cAAc,CAAC,wBAAwB,CAAC;EAC1E,IAAIS,aAAa,KAAIH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEI,sBAAsB,GAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACxDL,QAAQ,GAAGD,aAAa,aAAbA,aAAa,wBAAAK,qBAAA,GAAbL,aAAa,CAAEI,sBAAsB,cAAAC,qBAAA,uBAArCA,qBAAA,CAAwC,CAAC,CAAC;IACrDH,MAAM,GAAGF,aAAa,aAAbA,aAAa,wBAAAM,sBAAA,GAAbN,aAAa,CAAEI,sBAAsB,cAAAE,sBAAA,uBAArCA,sBAAA,CAAwC,CAAC,CAAC;EACvD;EACA,IAAIC,WAAW,GACXxI,MAAM,CAAC,CAAC,CAACyI,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,cAAc,CAAC,GACvC1I,MAAM,CAAC,CAAC,CACHmI,MAAM,CAAC,CAAC,CACRQ,kBAAkB,CAAC,EAAE,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEC,MAAM,EAAE;EAAU,CAAC,CAAC;EACvE,oBACIpJ,KAAA,CAAAmC,aAAA;IAAID,SAAS,EAAC,uCAAuC;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjDzC,KAAA,CAAAmC,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,GAAC,EAAC8F,KAAK,EAAC,GAAO,CAAC,EACrBI,aAAa,gBACV3I,KAAA,CAAAmC,aAAA;IAAOD,SAAS,EAAC,eAAe;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBACX,EAAC4G,uBAAuB,CAACZ,QAAQ,EAAE,IAAI,CAAC,EAAE,GAAG,EAAC,KAC7D,EAACY,uBAAuB,CAACX,MAAM,EAAE,IAAI,CAAC,EAAC,IACvC,CAAC,GACRK,WAAW,gBACX/I,KAAA,CAAAmC,aAAA;IAAOD,SAAS,EAAC,eAAe;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBACZ,EAAC,GAAG,EACpB4G,uBAAuB,CAACN,WAAW,EAAE,IAAI,CAAC,EAAC,KAAG,EAAC,GAAG,EAClDO,8BAA8B,CAAC,CAAC,EAAC,IAC/B,CAAC,GAER,EAEJ,CAAC;AAEb;AAEA,OAAO,SAASC,mBAAmBA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,EAAE;EACpD,MAAMC,SAAS,GAAGC,QAAQ,CAACC,QAAQ,CAACC,IAAI;EACxC,MAAM,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAGL,SAAS,CAAClF,KAAK,CAAC,GAAG,CAAC;EAC9C,IAAIwF,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIP,SAAS,IAAIM,MAAM,IAAIA,MAAM,CAACpG,QAAQ,CAAC8F,SAAS,CAAC,EAAE;IACnDO,SAAS,GAAGC,0BAA0B,CAAC;MAAEC,GAAG,EAAEH;IAAO,CAAC,CAAC;EAC3D;EACA,OAAOC,SAAS;AACpB;AAEA,OAAO,SAASC,0BAA0BA,CAAC;EAAEC,GAAG,GAAG;AAAM,CAAC,EAAE;EACxD,IAAI,CAACA,GAAG,EAAE;IACN9G,OAAO,CAAC+G,GAAG,CAAC,4CAA4C,CAAC;IACzD,OAAO,CAAC,CAAC;EACb;EACA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAACH,GAAG,CAAC;EACvC,MAAMI,GAAG,GAAG,CAAC,CAAC;EAEd,KAAK,MAAM,CAAC3H,GAAG,EAAE4H,KAAK,CAAC,IAAIH,MAAM,CAACI,OAAO,CAAC,CAAC,EAAE;IACzC,MAAMC,UAAU,GAAGC,kBAAkB,CAAC/H,GAAG,CAAC;IAC1C,MAAMgI,YAAY,GAAGD,kBAAkB,CAACH,KAAK,CAAC;IAC9C,IAAI;MACA,MAAMK,SAAS,GAAG3H,IAAI,CAACC,KAAK,CAACyH,YAAY,CAAC;MAC1CL,GAAG,CAACG,UAAU,CAAC,GAAGG,SAAS;IAC/B,CAAC,CAAC,OAAOzH,KAAK,EAAE;MACZmH,GAAG,CAACG,UAAU,CAAC,GAAGE,YAAY;IAClC;EACJ;EACA,OAAOL,GAAG;AACd;AAEA,OAAO,SAASO,iBAAiBA,CAAC;EAAEC,KAAK,GAAG;AAAG,CAAC,EAAE;EAC9C,MAAMpB,SAAS,GAAGC,QAAQ,CAACC,QAAQ,CAACC,IAAI;EACxC,MAAM,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAGL,SAAS,CAAClF,KAAK,CAAC,GAAG,CAAC;EAC9C,IAAIuF,MAAM,EAAE;IACR,MAAMgB,MAAM,GAAG,GAAGrB,SAAS,IAAIoB,KAAK,EAAE;IACtCpF,MAAM,CAACsF,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,MAAM,CAAC;EAC/C,CAAC,MAAM;IACH,MAAMA,MAAM,GAAG,GAAGrF,MAAM,CAACkE,QAAQ,CAACsB,QAAQ,IAAIJ,KAAK,EAAE;IACrDpF,MAAM,CAACsF,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,MAAM,CAAC;EAC/C;AACJ;AAEA,OAAO,SAASI,oBAAoBA,CAAC;EAAEC,WAAW,GAAG,CAAC;AAAE,CAAC,EAAE;EACvD,MAAM1B,SAAS,GAAG,IAAIW,eAAe,CAAC,CAAC;EACvC,KAAK,MAAM1H,GAAG,IAAIyI,WAAW,EAAE;IAC3B,IAAIC,MAAM,CAAClD,cAAc,CAACmD,IAAI,CAACF,WAAW,EAAEzI,GAAG,CAAC,EAAE;MAC9C,IAAI4I,MAAM,GAAGH,WAAW,CAACzI,GAAG,CAAC;MAC7B,IAAI,OAAO4I,MAAM,IAAI,QAAQ,EAAE;QAC3BA,MAAM,GAAGtI,IAAI,CAACuI,SAAS,CAACD,MAAM,CAAC;MACnC;MACA7B,SAAS,CAAC+B,MAAM,CAAC9I,GAAG,EAAE4I,MAAM,CAAC;IACjC;EACJ;EACA,OAAO7B,SAAS,CAACgC,QAAQ,CAAC,CAAC;AAC/B;AAEA,OAAO,MAAMC,gCAAgC,GAAGA,CAC5CC,IAAI,GAAG,GAAG,EACVC,SAAS,GAAG,CAAC,EACbC,QAAQ,GAAG,wBAAwB,KAClC;EACD3L,OAAO,CAAC4L,MAAM,CAAC;IACXC,GAAG,EAAEJ,IAAI;IAAE;IACXK,QAAQ,EAAEJ,SAAS,CAAE;EACzB,CAAC,CAAC;EACF1L,OAAO,CAAC+L,OAAO,CAACJ,QAAQ,CAAC;AAC7B,CAAC;AAED,OAAO,MAAMK,yBAAyB,GAAIC,oBAAoB,IAAK;EAC/D,IAAIC,oBAAoB,GAAG,EAAE;EAC7B,IAAIC,gBAAgB,GAAGzL,yBAAyB,CAACuL,oBAAoB,CAAC;EACtE,IAAIE,gBAAgB,EAAE;IAClBA,gBAAgB,CAACpH,OAAO,CAAEqH,qBAAqB,IAAK;MAChD,IAAIC,kBAAkB,GAAG;QACrB7J,GAAG,EAAE4J,qBAAqB,CAAC5J,GAAG,GAAG,WAAW;QAC5C8J,KAAK,EAAEF,qBAAqB,CAAC/D,KAAK,GAAG,WAAW;QAChDkE,WAAW,EAAE,UAAU;QACvBC,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE;UACT;UACAC,UAAU,EAAE,IAAI;UAChBC,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE;QACtB,CAAC;QACDC,OAAO,EAAE,CACL;UAAEP,KAAK,EAAE,KAAK;UAAElC,KAAK,EAAE,IAAI;UAAE0C,KAAK,EAAE;QAAa,CAAC,EAClD;UAAER,KAAK,EAAE,KAAK;UAAElC,KAAK,EAAE;QAAM,CAAC,EAC9B;UAAEkC,KAAK,EAAE,IAAI;UAAElC,KAAK,EAAE;QAAK,CAAC;MAEpC,CAAC;MACD8B,oBAAoB,CAACa,IAAI,CAACV,kBAAkB,CAAC;IACjD,CAAC,CAAC;EACN;EACA,OAAOH,oBAAoB;AAC/B,CAAC;AAED,MAAMc,UAAU,GAAG;AACf;AACA;EAAE5C,KAAK,EAAE,QAAQ;EAAEkC,KAAK,EAAE,QAAQ;EAAEQ,KAAK,EAAE;AAAU,CAAC,EACtD;EAAE1C,KAAK,EAAE,MAAM;EAAEkC,KAAK,EAAE,MAAM;EAAEQ,KAAK,EAAE;AAAU,CAAC,EAClD;EAAE1C,KAAK,EAAE,QAAQ;EAAEkC,KAAK,EAAE,QAAQ;EAAEQ,KAAK,EAAE;AAAU,CAAC,EACtD;EAAE1C,KAAK,EAAE,KAAK;EAAEkC,KAAK,EAAE,KAAK;EAAEQ,KAAK,EAAE;AAAU,CAAC,EAChD;EAAE1C,KAAK,EAAE,MAAM;EAAEkC,KAAK,EAAE,MAAM;EAAEQ,KAAK,EAAE;AAAU,CAAC,CACrD;AAED,OAAO,MAAMG,YAAY,GAAG,CACxB;EAAE7C,KAAK,EAAE,WAAW;EAAEkC,KAAK,EAAE;AAAY,CAAC,EAC1C;EAAElC,KAAK,EAAE,QAAQ;EAAEkC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAElC,KAAK,EAAE,QAAQ;EAAEkC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAElC,KAAK,EAAE,KAAK;EAAEkC,KAAK,EAAE;AAAM,CAAC,CACjC;AAED,OAAO,MAAMY,+BAA+B,GAAGA,CAACC,QAAQ,GAAG,CAAC,KAAK;EAC7D,IAAIC,WAAW,GAAG,EAAE;EACpB,KAAK,IAAItJ,CAAC,GAAGqJ,QAAQ,EAAErJ,CAAC,IAAI,GAAG,EAAEA,CAAC,EAAE,EAAE;IAClCsJ,WAAW,CAACL,IAAI,CAAC;MAAE3C,KAAK,EAAEtG,CAAC;MAAEwI,KAAK,EAAExI;IAAE,CAAC,CAAC;EAC5C;EACA,OAAOsJ,WAAW;AACtB,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAG,CACjC;EAAEjD,KAAK,EAAE,YAAY;EAAEkC,KAAK,EAAE;AAAa,CAAC,EAC5C;EAAElC,KAAK,EAAE,OAAO;EAAEkC,KAAK,EAAE;AAAQ,CAAC,CACrC;AAED,OAAO,MAAMgB,qBAAqB,GAAG,CACjC;EAAElD,KAAK,EAAE,iBAAiB;EAAEkC,KAAK,EAAE;AAAkB,CAAC,EACtD;EAAElC,KAAK,EAAE,aAAa;EAAEkC,KAAK,EAAE;AAAc,CAAC,CACjD;AAED,OAAO,MAAMiB,0BAA0B,GAAG,eAAe;AAEzD,OAAO,MAAMC,+BAA+B,GAAGA,CAAA,KAAM;EACjD,OAAO,CACH;IAAElB,KAAK,EAAE,YAAY;IAAElC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAEkC,KAAK,EAAE,eAAe;IAAElC,KAAK,EAAEmD;EAA2B,CAAC,CAChE;AACL,CAAC;AACD,OAAO,MAAME,+BAA+B,GAAGA,CAAA,KAAM;EACjD,OAAO,CACH;IAAEnB,KAAK,EAAE,UAAU;IAAElC,KAAK,EAAE;EAAY,CAAC,EACzC;IAAEkC,KAAK,EAAE,SAAS;IAAElC,KAAK,EAAE;EAAW,CAAC,CAC1C;AACL,CAAC;AAED,OAAO,MAAMsD,uBAAuB,GAAGA,CAACC,UAAU,EAAEC,SAAS,KAAK;EAC9D;EACAD,UAAU,GAAGA,UAAU,CAACE,MAAM,CAC1B,CAACC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,KAClBA,IAAI,CAAC5K,GAAG,CAAE6K,GAAG,IAAKA,GAAG,CAACzL,GAAG,CAAC,CAAC0L,OAAO,CAACJ,QAAQ,CAACtL,GAAG,CAAC,KAAKuL,KAC7D,CAAC;EAED,IAAII,SAAS,GAAG,CAAC,CAAC;EAClBR,UAAU,CAAC5I,OAAO,CAAEqJ,eAAe,IAAK;IACpC,IAAI5L,GAAG,GAAG4L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE5L,GAAG;IAC9B,IAAI4H,KAAK,GAAGwD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGpL,GAAG,CAAC;IAC5B,IAAIoL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGpL,GAAG,CAAC,EAAE;MAAA,IAAA6L,qBAAA;MAClB,OAAOD,eAAe,CAAC,SAAS,CAAC;MACjC,IAAIA,eAAe,CAAC5B,MAAM,IAAI,aAAa,EAAE;QACzCpC,KAAK,GAAG/J,MAAM,CAACiO,GAAG,CAAClE,KAAK,CAAC,CAACrB,MAAM,CAAC,aAAa,CAAC;MACnD,CAAC,MAAM,IAAIqF,eAAe,CAAC5B,MAAM,IAAI,QAAQ,EAAE;QAC3CpC,KAAK,GAAGmE,uBAAuB,CAACH,eAAe,CAACvB,OAAO,EAAEzC,KAAK,CAAC;MACnE,CAAC,MAAM,IAAI,EAAAiE,qBAAA,GAAAD,eAAe,CAAC5B,MAAM,cAAA6B,qBAAA,uBAAtBA,qBAAA,CAAwBG,WAAW,KAAI,MAAM,EAAE;QACtDpE,KAAK,GAAGmE,uBAAuB,CAACH,eAAe,CAACvB,OAAO,EAAEzC,KAAK,CAAC;MACnE,CAAC,MAAM,IAAIgE,eAAe,CAAC5B,MAAM,IAAI,aAAa,EAAE;QAChDpC,KAAK,GAAGmE,uBAAuB,CAACH,eAAe,CAACvB,OAAO,EAAEzC,KAAK,CAAC;MACnE,CAAC,MAAM,IAAIgE,eAAe,CAAC5B,MAAM,IAAI,gBAAgB,EAAE;QACnDpC,KAAK,GAAGmE,uBAAuB,CAACH,eAAe,CAACvB,OAAO,EAAEzC,KAAK,CAAC;MACnE,CAAC,MAAM,IAAIgE,eAAe,CAAC5B,MAAM,EAAE;QAC/B,IAAIiC,SAAS,GAAGb,SAAS,CAACpL,GAAG,CAAC,CAAC,CAAC;QAChC,IAAIiM,SAAS,CAACnC,KAAK,EAAE;UACjBlC,KAAK,GAAGqE,SAAS,CAACnC,KAAK;QAC3B;MACJ;IACJ;IACA6B,SAAS,CAAC3L,GAAG,CAAC,GAAG4H,KAAK;EAC1B,CAAC,CAAC;EACF,OAAO+D,SAAS;AACpB,CAAC;AAED,OAAO,MAAMO,oBAAoB,GAAGA,CAChCC,OAAO,EACPC,eAAe,GAAGC,SAAS,EAC3BC,IAAI,KACH;EACD;EACA,IAAIC,OAAO,GAAGJ,OAAO,CAACK,KAAK,CAAC,WAAW,CAAC;EACxCD,OAAO,CAAChK,OAAO,CAAEkK,WAAW,IAAK;IAC7B,IAAIC,IAAI,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAEF,WAAW,CAACtL,MAAM,GAAG,CAAC,CAAC;IAC3D,IAAInB,GAAG,GAAGoM,eAAe,CAACM,IAAI,CAAC;IAC/B,IAAIE,UAAU,GAAGN,IAAI,CAACtM,GAAG,CAAC,IAAI,KAAK;IACnCmM,OAAO,GAAGA,OAAO,CAACU,OAAO,CAAC,IAAIH,IAAI,GAAG,EAAEE,UAAU,CAAC;EACtD,CAAC,CAAC;EACF,OAAOT,OAAO;AAClB,CAAC;AAED,OAAO,MAAMW,mBAAmB,GAAGA,CAC/BX,OAAO,EACPC,eAAe,GAAGC,SAAS,EAC3BC,IAAI,EACJS,eAAe,GAAG,IAAI,KACrB;EACD,IAAIR,OAAO,GAAGJ,OAAO,CAACK,KAAK,CAAC,WAAW,CAAC;EACxCD,OAAO,CAAChK,OAAO,CAAEkK,WAAW,IAAK;IAC7B,IAAIC,IAAI,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAEF,WAAW,CAACtL,MAAM,GAAG,CAAC,CAAC;IAC3D,IAAInB,GAAG,GAAGoM,eAAe,CAACM,IAAI,CAAC;IAC/B,IAAIE,UAAU,GAAG5M,GAAG,IAAIsM,IAAI,GAAGA,IAAI,CAACtM,GAAG,CAAC,GAAG,WAAW;IACtDmM,OAAO,GAAGA,OAAO,CAACU,OAAO,CAAC,IAAIH,IAAI,GAAG,EAAEE,UAAU,CAAC;EACtD,CAAC,CAAC;EACF,IAAIrI,MAAM,GAAG,CAAC;EACd,IAAI;IACAA,MAAM,GAAGyI,IAAI,CAACb,OAAO,CAAC;IACtB,IAAI,CAACY,eAAe,EAAE;MAClBxI,MAAM,GAAG0I,UAAU,CAAC1I,MAAM,CAAC,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC1C;EACJ,CAAC,CAAC,OAAOnF,KAAK,EAAE;IACZC,OAAO,CAAC+G,GAAG,CAAC,2BAA2B,EAAEhH,KAAK,CAAC;EACnD;EACA,OAAO+D,MAAM;AACjB,CAAC;AAED,OAAO,MAAM2I,gBAAgB,GAAIZ,IAAI,IAAK;EACtC,IAAI1E,KAAK,GAAG,EAAE;EACd,IAAI0E,IAAI,IAAID,SAAS,EAAE;IAAA,IAAAc,OAAA;IACnB,IAAIzE,MAAM,CAAC0E,IAAI,CAACd,IAAI,CAAC,CAACnL,MAAM,IAAI,CAAC,EAAE;MAC/ByG,KAAK,GAAGA,KAAK;IACjB,CAAC,MAAM,IAAI,OAAO0E,IAAI,KAAK,QAAQ,EAAE;MACjC1E,KAAK,GAAG0E,IAAI,CAACzK,KAAK,CAAC,GAAG,CAAC;IAC3B,CAAC,MAAM;MACH+F,KAAK,GAAG0E,IAAI;IAChB;IACA1E,KAAK,IAAAuF,OAAA,GAAGvF,KAAK,cAAAuF,OAAA,uBAALA,OAAA,CAAO9B,MAAM,CAAEgC,SAAS,IAAKA,SAAS,IAAI,EAAE,CAAC;EACzD;EACA,OAAOzF,KAAK;AAChB,CAAC;;AAED;AACA,OAAO,MAAM0F,uBAAuB,GAAIC,WAAW,IAAK;EACpD,IAAIC,cAAc,GAAG,EAAE;EACvBD,WAAW,CAAC3M,GAAG,CAAEC,MAAM,IAAK;IACxB2M,cAAc,CAACjD,IAAI,CAAC;MAChBT,KAAK,EAAEjJ,MAAM,CAACgF,KAAK;MACnB+B,KAAK,EAAE/G,MAAM,CAACb,GAAG;MACjBsK,KAAK,EAAEzJ,MAAM,CAACyJ;IAClB,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOkD,cAAc;AACzB,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACtC,OAAO;IAAEC,GAAG,EAAE,UAAU;IAAEC,GAAG,EAAE;EAAW,CAAC;AAC/C,CAAC;AAED,MAAMC,4BAA4B,GAAIC,QAAQ,IAAK;EAC/C,IAAIC,eAAe,GAAG;IAClBC,MAAM,EAAE,SAAS;IACjBpN,IAAI,EAAE,SAAS;IACfI,MAAM,EAAE;EACZ,CAAC;EACD,OAAO+M,eAAe,CAACD,QAAQ,CAAC,GAAGC,eAAe,CAACD,QAAQ,CAAC,GAAG,SAAS;AAC5E,CAAC;AACD,MAAMG,sBAAsB,GAAIC,QAAQ,IAAK;EACzC,IAAIC,eAAe,GAAG;IAClBC,MAAM,EAAE,aAAa;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAE,cAAc;IACtBC,GAAG,EAAE,eAAe;IACpBC,IAAI,EAAE;EACV,CAAC;EACD,OAAOL,eAAe,CAACD,QAAQ,CAAC,GAAGC,eAAe,CAACD,QAAQ,CAAC,GAAG,EAAE;AACrE,CAAC;AACD,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,IAAIC,aAAa,GAAG,CAChB,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,oBAAoB,EACpB,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,CACjB;EACD,OAAOA,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,aAAa,CAACtN,MAAM,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM0N,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,IAAIJ,aAAa,GAAG,CAChB,aAAa,EACb,cAAc,EACd,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,YAAY,CACf;EACD,OAAOA,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,aAAa,CAACtN,MAAM,CAAC,CAAC;AAC1E,CAAC;AACD,OAAO,MAAM2N,mBAAmB,GAAGA,CAAA,KAAM;EACrC,IAAIL,aAAa,GAAG,CAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,UAAU,EACV,QAAQ,CACX;EACD,OAAOA,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,aAAa,CAACtN,MAAM,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM4N,iBAAiB,GAAGA,CAAC5N,MAAM,GAAG,CAAC,KAAK;EACtC,IAAIoD,MAAM,GAAG,EAAE;EACf,IAAIyK,UAAU,GACV,gEAAgE;EACpE,IAAIC,gBAAgB,GAAGD,UAAU,CAAC7N,MAAM;EACxC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;IAC7BiD,MAAM,CAACgG,IAAI,CACPyE,UAAU,CAACE,MAAM,CAACR,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGK,gBAAgB,CAAC,CAClE,CAAC;EACL;EACA,OAAO1K,MAAM,CAAC4K,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AAED,MAAMC,oBAAoB,GAAGA,CAACnQ,IAAI,GAAG,KAAK,KAAK;EAC3C,OAAO;IAAE4G,KAAK,EAAE5G,IAAI;IAAE6K,KAAK,EAAE7K,IAAI;IAAE2I,KAAK,EAAE,IAAI;IAAE0C,KAAK,EAAE;EAAU,CAAC;AACtE,CAAC;AAED,MAAM+E,sBAAsB,GAAGA,CAACpQ,IAAI,GAAG,OAAO,KAAK;EAC/C,OAAO;IAAE4G,KAAK,EAAE5G,IAAI;IAAE6K,KAAK,EAAE7K,IAAI;IAAE2I,KAAK,EAAE,IAAI;IAAE0C,KAAK,EAAE;EAAU,CAAC;AACtE,CAAC;AAED,OAAO,MAAMgF,yBAAyB,GAAGA,CAAA,KAAM;EAC3C,OAAO;IACHtP,GAAG,EAAE,UAAU;IACf8J,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,QAAQ;IAChBuF,KAAK,EAAE,IAAI;IACXtF,WAAW,EAAE;MACT;MACAuF,IAAI,EAAE,UAAU;MAChBpF,gBAAgB,EAAE,UAAU;MAC5BD,UAAU,EAAE;IAChB,CAAC;IACDE,OAAO,EAAEG;EACb,CAAC;AACL,CAAC;AAED,OAAO,MAAMiF,eAAe,GAAGA,CAAA,KAAM;EACjC,OAAO,CACH;IAAE3F,KAAK,EAAE,YAAY;IAAElC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAEkC,KAAK,EAAE,gBAAgB;IAAElC,KAAK,EAAE;EAAiB,CAAC,CACvD;AACL,CAAC;AAED,OAAO,MAAM8H,oBAAoB,GAAGA,CAAA,KAAM;EACtC,OAAO,CACH;IAAE5F,KAAK,EAAE,oBAAoB;IAAElC,KAAK,EAAE;EAAqB,CAAC,EAC5D;IAAEkC,KAAK,EAAE,wBAAwB;IAAElC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEkC,KAAK,EAAE,kBAAkB;IAAElC,KAAK,EAAE;EAAmB,CAAC,EACxD;IAAEkC,KAAK,EAAE,sBAAsB;IAAElC,KAAK,EAAE;EAAQ,CAAC,CACpD;AACL,CAAC;AAED,OAAO,MAAM+H,gBAAgB,GAAIC,aAAa,IAAK;EAC/C,MAAMC,KAAK,GAAG;IACVC,OAAO,EAAE;MACLC,eAAe,EAAE,gBAAgB;MACjCC,UAAU,EAAE,SAAS;MACrBlG,KAAK,EAAE;IACX,CAAC;IACD,oBAAoB,EAAE;MAClBiG,eAAe,EAAE,YAAY;MAC7BC,UAAU,EAAE,SAAS;MACrBlG,KAAK,EAAE;IACX,CAAC;IACDmG,KAAK,EAAE;MACHF,eAAe,EAAE,gBAAgB;MACjCC,UAAU,EAAE,OAAO;MACnBlG,KAAK,EAAE;IACX,CAAC;IACD,kBAAkB,EAAE;MAChBiG,eAAe,EAAE,YAAY;MAC7BC,UAAU,EAAE,OAAO;MACnBlG,KAAK,EAAE;IACX;EACJ,CAAC;EAED,OAAO+F,KAAK,CAACD,aAAa,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC;AAED,OAAO,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;EACrC,OAAO,CACH;IAAEpG,KAAK,EAAE,MAAM;IAAElC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAEkC,KAAK,EAAE,UAAU;IAAElC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEkC,KAAK,EAAE,OAAO;IAAElC,KAAK,EAAE;EAAQ,CAAC,CACrC;AACL,CAAC;AAED,OAAO,MAAMuI,oBAAoB,GAAGA,CAAA,KAAM;EACtC,OAAO,CACH;IAAErG,KAAK,EAAE,OAAO;IAAElC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAEkC,KAAK,EAAE,YAAY;IAAElC,KAAK,EAAE;EAAa,CAAC,CAC/C;AACL,CAAC;AAED,OAAO,MAAMwI,cAAc,GAAGA,CAAA,KAAM;EAChC,OAAO,CACH;IAAEtG,KAAK,EAAE,UAAU;IAAEuG,UAAU,EAAE,UAAU;IAAEzI,KAAK,EAAE;EAAW,CAAC,EAChE;IAAEkC,KAAK,EAAE,MAAM;IAAEuG,UAAU,EAAE,GAAG;IAAEzI,KAAK,EAAE;EAAO,CAAC,EACjD;IAAEkC,KAAK,EAAE,UAAU;IAAEuG,UAAU,EAAE,IAAI;IAAEzI,KAAK,EAAE;EAAW,CAAC,EAC1D;IAAEkC,KAAK,EAAE,OAAO;IAAEuG,UAAU,EAAE,GAAG;IAAEzI,KAAK,EAAE;EAAQ,CAAC,EACnD;IAAEkC,KAAK,EAAE,OAAO;IAAEuG,UAAU,EAAE,GAAG;IAAEzI,KAAK,EAAE;EAAQ,CAAC,EACnD;IAAEkC,KAAK,EAAE,YAAY;IAAEuG,UAAU,EAAE,IAAI;IAAEzI,KAAK,EAAE;EAAa,CAAC,CACjE;AACL,CAAC;AAED,OAAO,MAAM0I,oBAAoB,GAAGA,CAAA,KAAM;EACtC,OAAO;IACHtQ,GAAG,EAAE,YAAY;IACjB8J,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,QAAQ;IAChBuF,KAAK,EAAE,IAAI;IACXgB,KAAK,EAAE,IAAI;IACXtG,WAAW,EAAE;MACT;MACAuF,IAAI,EAAE,UAAU;MAChBpF,gBAAgB,EAAE;IACtB,CAAC;IACDC,OAAO,EAAE,CACL;MAAEP,KAAK,EAAE,KAAK;MAAElC,KAAK,EAAE;IAAK,CAAC,EAC7B;MAAEkC,KAAK,EAAE,IAAI;MAAElC,KAAK,EAAE;IAAM,CAAC;EAErC,CAAC;AACL,CAAC;AAED,OAAO,MAAM4I,sBAAsB,GAAGA,CAAA,KAAM;EACxC,OAAO;IACHxQ,GAAG,EAAE,YAAY;IACjB8J,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,QAAQ;IAChBuF,KAAK,EAAE,IAAI;IACXtF,WAAW,EAAE;MACT;MACAuF,IAAI,EAAE,UAAU;MAChBpF,gBAAgB,EAAE;IACtB,CAAC;IACDC,OAAO,EAAE,CACL;MAAEP,KAAK,EAAE,KAAK;MAAElC,KAAK,EAAE,IAAI;MAAE0C,KAAK,EAAE;IAAa,CAAC,EAClD;MAAER,KAAK,EAAE,KAAK;MAAElC,KAAK,EAAE;IAAK,CAAC,EAC7B;MAAEkC,KAAK,EAAE,IAAI;MAAElC,KAAK,EAAE;IAAM,CAAC;EAErC,CAAC;AACL,CAAC;AACD,OAAO,MAAM6I,sBAAsB,GAAGA,CAAA,KAAM;EACxC,OAAO,CACH;IACIzQ,GAAG,EAAE,QAAQ;IACb8J,KAAK,EAAE,iBAAiB;IACxBE,MAAM,EAAE,QAAQ;IAChBK,OAAO,EAAE;EACb,CAAC,EACD;IACIrK,GAAG,EAAE,YAAY;IACjB8J,KAAK,EAAE,qBAAqB;IAC5BE,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE;MAAEuF,IAAI,EAAE,UAAU;MAAEpF,gBAAgB,EAAE;IAAW,CAAC;IAC/DC,OAAO,EAAE,EAAE;IACXkG,KAAK,EAAE;EACX,CAAC,EACD;IACIvQ,GAAG,EAAE,UAAU;IACf8J,KAAK,EAAE,mBAAmB;IAC1BE,MAAM,EAAE,QAAQ;IAChBK,OAAO,EAAE,CACL;MAAEzC,KAAK,EAAE,IAAI;MAAEkC,KAAK,EAAE;IAAM,CAAC,EAC7B;MAAElC,KAAK,EAAE,KAAK;MAAEkC,KAAK,EAAE;IAAK,CAAC,CAChC;IACDyG,KAAK,EAAE;EACX,CAAC,CACJ;AACL,CAAC;AACD,OAAO,MAAMG,0BAA0B,GAAGA,CAAA,KAAM;EAC5C,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,oBAAoB,GAAG,EAAE;EAC7BA,oBAAoB,GAAGC,+BAA+B,CAClDF,aAAa,EACZG,MAAM,KAAM;IACTxG,KAAK,EAAEyG,6BAA6B,CAChC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IAAE;IACd,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;IAAE;IACfD,MAAM,GAAGH,aACb;EACJ,CAAC,CACL,CAAC,CAACtF,MAAM,CAAEnM,IAAI,IAAKA,IAAI,CAAC0I,KAAK,IAAI,CAAC,CAAC;EAEnC,IAAIoJ,4BAA4B,GAAG;IAC/BlH,KAAK,EAAE,KAAK;IACZlC,KAAK,EAAE,IAAI;IACX0C,KAAK,EAAE;EACX,CAAC;EACDsG,oBAAoB,CAACrG,IAAI,CAACyG,4BAA4B,CAAC;EACvD,OAAO;IACHhR,GAAG,EAAE,aAAa;IAClB8J,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,QAAQ;IAChBuF,KAAK,EAAE,IAAI;IACXtF,WAAW,EAAE;MACT;MACAuF,IAAI,EAAE,UAAU;MAChBpF,gBAAgB,EAAE,UAAU;MAC5BD,UAAU,EAAE;IAChB,CAAC;IACDE,OAAO,EAAEuG;EACb,CAAC;AACL,CAAC;AAED,MAAMK,0BAA0B,GAAGA,CAAC7F,SAAS,EAAED,UAAU,GAAGkB,SAAS,KAAK;EACtE,IAAI6E,UAAU,GAAG9F,SAAS;EAC1B,IAAI8F,UAAU,IAAI7E,SAAS,EAAE;IACzB3D,MAAM,CAAC0E,IAAI,CAAChC,SAAS,CAAC,CAACxK,GAAG,CAAEZ,GAAG,IAAK;MAChC,IAAI4M,UAAU,GAAGxB,SAAS,CAACpL,GAAG,CAAC;MAC/B,IAAImL,UAAU,EAAE;QACZ,IAAIgG,YAAY,GAAGhG,UAAU,CAACE,MAAM,CAC/B+F,SAAS,IAAKA,SAAS,CAACpR,GAAG,IAAIA,GACpC,CAAC;QACD,IAAImR,YAAY,CAAChQ,MAAM,GAAG,CAAC,EAAE;UACzB,IAAIiQ,SAAS,GAAGD,YAAY,CAAC,CAAC,CAAC;UAC/B,IACIC,SAAS,CAACpH,MAAM,IAChB,OAAOoH,SAAS,CAACpH,MAAM,KAAK,UAAU,IACtCoH,SAAS,CAACpH,MAAM,CAACxH,IAAI,IAAIjF,UAAU,CAAC8T,WAAW,CAAC7O,IAAI,EACtD;YAAA,IAAA8O,qBAAA;YACE;YACA,IACI1T,OAAO,CAACgP,UAAU,CAAC,IACnBA,UAAU,CAACzL,MAAM,IAAI,CAAC,MAAAmQ,qBAAA,GACtBF,SAAS,CAACnH,WAAW,cAAAqH,qBAAA,uBAArBA,qBAAA,CAAuBC,MAAM,GAC/B;cACEL,UAAU,CAAClR,GAAG,CAAC,GAAG,CACdnC,MAAM,CAAC+O,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAC3B/O,MAAM,CAAC+O,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAC9B;YACL;UACJ,CAAC,MAAM,IAAIwE,SAAS,CAACpH,MAAM,IAAI,aAAa,EAAE;YAC1C;YACA,IAAI4C,UAAU,IAAIA,UAAU,IAAI,EAAE,EAAE;cAChC,IAAI4E,IAAI,GAAG3T,MAAM,CAAC+O,UAAU,EAAE,IAAI,CAAC;cACnC,IAAI4E,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;gBAChB;gBACAP,UAAU,CAAClR,GAAG,CAAC,GAAGwR,IAAI;cAC1B,CAAC,MAAM;gBACHN,UAAU,CAAClR,GAAG,CAAC,GAAGnC,MAAM,CAAC,CAAC;cAC9B;YACJ,CAAC,MAAM;cACHqT,UAAU,CAAClR,GAAG,CAAC,GAAGqM,SAAS;YAC/B;UACJ,CAAC,MAAM,IACH+E,SAAS,CAACM,WAAW,KAAK,YAAY,IACrC,OAAON,SAAS,CAACpH,MAAM,KAAK,UAAU,IAAIoH,SAAS,CAACpH,MAAM,CAACxH,IAAI,KAAK,YAAa,EACpF;YACE,IAAI,CAACoK,UAAU,EAAEsE,UAAU,CAAClR,GAAG,CAAC,GAAG,IAAI,CAAC,KACnC;cAAA,IAAA2R,WAAA;cACD,IAAIC,UAAU;cACd,IAAI/T,MAAM,CAACgU,QAAQ,CAACjF,UAAU,CAAC,EAAE;gBAC7BgF,UAAU,GAAGhF,UAAU;cAC3B,CAAC,MAAM;gBACH;gBACAgF,UAAU,GAAG/T,MAAM,CAAC+O,UAAU,EAAE,CAC5B,QAAQ;gBAAK;gBACb,SAAS;gBAAI;gBACb,SAAS;gBAAI;gBACb,UAAU;gBAAG;gBACb,OAAO;gBAAM;gBACb,QAAQ;gBAAK;gBACb,MAAM;gBAAO;gBACb,OAAO;gBAAM;gBACb/O,MAAM,CAACiU,QAAQ,CAClB,EAAE,IAAI,CAAC;cACZ;cAEAZ,UAAU,CAAClR,GAAG,CAAC,GAAG,EAAA2R,WAAA,GAAAC,UAAU,cAAAD,WAAA,uBAAVA,WAAA,CAAYF,OAAO,CAAC,CAAC,IAAGG,UAAU,CAACrL,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI;YACjF;UACJ;QAEJ;MACJ,CAAC,MAAM;QACH;QACA,IAAI,OAAOqG,UAAU,KAAK,QAAQ,EAAE;UAChC,IAAI4E,IAAI,GAAG3T,MAAM,CAAC+O,UAAU,EAAE,IAAI,CAAC;UACnC,IAAI4E,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;YAChB;YACAP,UAAU,CAAClR,GAAG,CAAC,GAAGwR,IAAI;UAC1B;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAON,UAAU;AACrB,CAAC;AAED,MAAMa,YAAY,GAAIC,aAAa,IAAK;EACpC;EACA,IAAId,UAAU,GAAG,KAAK;EACtB,IAAIc,aAAa,IAAI3F,SAAS,EAAE;IAC5B,IAAI4F,SAAS,GAAGpU,MAAM,CAACiO,GAAG,CAACkG,aAAa,CAAC;IACzC;IACA,IAAIC,SAAS,CAACC,OAAO,CAAC,CAAC,GAAGrU,MAAM,CAAC,CAAC,CAACiO,GAAG,CAAC,CAAC,CAACoG,OAAO,CAAC,CAAC,EAAE;MAChDhB,UAAU,GAAG,IAAI;IACrB;EACJ;EACA,OAAOA,UAAU;AACrB,CAAC;AACD,OAAO,MAAMiB,8BAA8B,GAAIC,WAAW,IAAK;EAC3D,OAAOA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9L,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,cAAc,CAAC;AACtD,CAAC;AAED,OAAO,MAAM8L,mCAAmC,GAAGA,CAAA,KAAM;EACrD,IAAIC,QAAQ,GAAGtU,aAAa,CAACuU,eAAe,CAAC,CAAC;EAC9C,IAAIlM,WAAW,GAAGmM,yBAAyB,CAAC3U,MAAM,CAACiO,GAAG,CAAC,CAAC,CAAC;EACzD,OAAO,GAAGwG,QAAQ,IAAIjM,WAAW,EAAE;AACvC,CAAC;AAED,OAAO,MAAMmM,yBAAyB,GAAIJ,WAAW,IAAK;EACtD,OAAOA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9L,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;AACpD,CAAC;AAED,OAAO,MAAMkM,aAAa,GAAGA,CAAA,KAAM;EAC/B,IAAIjB,IAAI,GAAG3T,MAAM,CAAC,CAAC;EACnB,OAAO2T,IAAI,CAAClL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;AAC5C,CAAC;AAED,OAAO,MAAMK,8BAA8B,GAAGA,CAAA,KAAM;EAChD,IAAI4K,IAAI,GAAG3T,MAAM,CAAC,CAAC;EACnB,OACI2T,IAAI,CAAClL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,cAAc,CAAC,GACnCiL,IAAI,CACCxL,MAAM,CAAC,CAAC,CACRQ,kBAAkB,CAAC,EAAE,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEC,MAAM,EAAE;EAAU,CAAC,CAAC;AAE3E,CAAC;AAED,MAAMC,uBAAuB,GAAGA,CAACqL,aAAa,EAAEU,SAAS,EAAEC,SAAS,KAAK;EACrE,IAAIzB,UAAU,GAAG,EAAE;EACnB,IAAIc,aAAa,IAAI3F,SAAS,EAAE;IAC5B,IAAI,OAAO2F,aAAa,KAAK,QAAQ,EAAE;MACnC,IAAIR,IAAI,GAAG3T,MAAM,CAACiO,GAAG,CAACkG,aAAa,CAAC;MACpC,IAAIR,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;QAChB,IAAIiB,SAAS,EAAE;UACXxB,UAAU,GAAGM,IAAI,CAAClL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,cAAc,CAAC;QACpD,CAAC,MAAM,IAAIoM,SAAS,EAAE;UAClBzB,UAAU,GAAGM,IAAI,CAAClL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC;QAC9C,CAAC,MAAM;UACH2K,UAAU,GACNM,IAAI,CAAClL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,cAAc,CAAC,GACnCiL,IAAI,CAACxL,MAAM,CAAC,CAAC,CAACQ,kBAAkB,CAAC,EAAE,EAAE;YACjCC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACZ,CAAC,CAAC;QACV;MACJ;IACJ;EACJ;EACA,OAAOwK,UAAU;AACrB,CAAC;AAED,MAAM0B,2BAA2B,GAAGA,CAChCxH,SAAS,EACTyH,YAAY,EACZC,eAAe,GAAG,EAAE,KACnB;EACD,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIF,YAAY,EAAE;IACd,IAAIG,SAAS,GAAGtK,MAAM,CAAC0E,IAAI,CAAChC,SAAS,CAAC;IACtC4H,SAAS,CAACzQ,OAAO,CAAE0Q,SAAS,IAAK;MAC7B,IACIJ,YAAY,CAACK,cAAc,CAACD,SAAS,CAAC,IACtCH,eAAe,CAAC9R,QAAQ,CAACiS,SAAS,CAAC,EACrC;QACEF,YAAY,CAACE,SAAS,CAAC,GAAG7H,SAAS,CAAC6H,SAAS,CAAC;QAC9C,MAAMrG,UAAU,GAAGxB,SAAS,CAAC6H,SAAS,CAAC;QACvC,IAAIrG,UAAU,KAAKP,SAAS,EAAE;UAC1B;UACA,IAAIyG,eAAe,CAAC9R,QAAQ,CAACiS,SAAS,CAAC,EAAE;YACrCF,YAAY,CAACE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;UACpC;QACJ;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,MAAM;IACHF,YAAY,GAAG3H,SAAS;IACxB3K,OAAO,CAAC+G,GAAG,CACP,mCAAmC,EACnC,+CACJ,CAAC;EACL;EACA,OAAOuL,YAAY;AACvB,CAAC;AAED,SAASI,gBAAgBA,CAACC,KAAK,EAAE;EAC7B,MAAM9J,QAAQ,GAAGzL,MAAM,CAACyL,QAAQ,CAACzL,MAAM,CAACiO,GAAG,CAAC,CAAC,CAACuH,IAAI,CAACxV,MAAM,CAACiO,GAAG,CAACsH,KAAK,CAAC,CAAC,CAAC;;EAEtE;EACA,MAAME,IAAI,GAAG5E,IAAI,CAACC,KAAK,CAACrF,QAAQ,CAACiK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMC,aAAa,GAAGF,IAAI,GAAG,GAAGA,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;;EAE/C;EACA,MAAMxR,KAAK,GAAGwH,QAAQ,CAACxH,KAAK,CAAC,CAAC;EAC9B,MAAM2R,cAAc,GAAG3R,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,IAAI,GAAG,EAAE;;EAEpD;EACA,MAAMC,OAAO,GAAGuH,QAAQ,CAACvH,OAAO,CAAC,CAAC;EAClC,MAAM2R,gBAAgB,GAAG,GAAG3R,OAAO,GAAG;EAEtC,OAAO,CAACyR,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,CAAC,CAACvE,IAAI,CAAC,EAAE,CAAC;AACrE;;AAEA;AACA,SAASwE,eAAeA,CAACpM,GAAG,EAAEE,MAAM,EAAE;EAClC,IAAImM,MAAM,GAAG,EAAE;EACf,IAAI7M,SAAS,GAAG,IAAIW,eAAe,CAAC,CAAC;EACrC,KAAK,MAAM1H,GAAG,IAAIyH,MAAM,EAAE;IACtB,IAAIiB,MAAM,CAAClD,cAAc,CAACmD,IAAI,CAAClB,MAAM,EAAEzH,GAAG,CAAC,EAAE;MACzC,MAAM6T,OAAO,GAAGpM,MAAM,CAACzH,GAAG,CAAC;MAC3B,IAAI4H,KAAK,GAAGiM,OAAO;MACnB,IAAI,OAAOA,OAAO,IAAI,QAAQ,EAAE;QAC5BjM,KAAK,GAAGtH,IAAI,CAACuI,SAAS,CAACgL,OAAO,CAAC;MACnC;MACA9M,SAAS,CAAC+B,MAAM,CAAC9I,GAAG,EAAE4H,KAAK,CAAC;IAChC;EACJ;EACA,OAAO;IAAEW,QAAQ,EAAEhB,GAAG;IAAEqM,MAAM,EAAE7M,SAAS,CAACgC,QAAQ,CAAC;EAAE,CAAC;AAC1D;AAEA,SAAS+K,qBAAqBA,CAACC,QAAQ,EAAE;EACrC,OAAO;IAAE/T,GAAG,EAAE,SAAS;IAAE6F,KAAK,EAAE,aAAa;IAAEkO;EAAS,CAAC;AAC7D;AAEA,SAASC,iBAAiBA,CAACC,iBAAiB,EAAEC,iBAAiB,EAAE;EAC7D,IAAIC,UAAU,GAAG,KAAK;EACtB;EACA,IAAID,iBAAiB,IAAI7H,SAAS,IAAI4H,iBAAiB,EAAE;IACrDE,UAAU,GAAG,IAAI;EACrB,CAAC,MAAM;IACH,IAAIC,QAAQ,GAAG1L,MAAM,CAAC0E,IAAI,CAAC6G,iBAAiB,CAAC;IAE7CG,QAAQ,CAACxT,GAAG,CAAEqS,SAAS,IAAK;MACxB,IAAIoB,iBAAiB,GAAGJ,iBAAiB,CAAChB,SAAS,CAAC;MACpD,IAAIqB,iBAAiB,GAAGJ,iBAAiB,CAACjB,SAAS,CAAC;MACpD,IACIqB,iBAAiB,IAAIjI,SAAS,IAC9BgI,iBAAiB,IAAIhI,SAAS,EAChC;QACE8H,UAAU,GAAG,IAAI;MACrB,CAAC,MAAM;QACHG,iBAAiB,CAAC1T,GAAG,CAAE2T,OAAO,IAAK;UAC/B,IAAI,CAACF,iBAAiB,CAACrT,QAAQ,CAACuT,OAAO,CAAC,EAAE;YACtCJ,UAAU,GAAG,IAAI;UACrB;QACJ,CAAC,CAAC;QACFE,iBAAiB,CAACzT,GAAG,CAAE4T,OAAO,IAAK;UAC/B,IAAI,CAACF,iBAAiB,CAACtT,QAAQ,CAACwT,OAAO,CAAC,EAAE;YACtCL,UAAU,GAAG,IAAI;UACrB;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EAEA,OAAOA,UAAU;AACrB;AAEA,OAAO,MAAMM,iBAAiB,GAAIC,MAAM,IAAK;EACzC,IAAIC,SAAS,GAAGxP,KAAK,CAACuP,MAAM,CAAC,KAAK,KAAK;EACvC,IAAIC,SAAS,EAAE;IACX,IAAIC,QAAQ,GAAGlG,IAAI,CAACC,KAAK,CAAC+F,MAAM,GAAG,KAAK,CAAC;IACzC,IAAIG,SAAS,GAAGD,QAAQ,GAAG,KAAK;IAChC,IAAIE,SAAS,GAAG,IAAIC,IAAI,CAACF,SAAS,GAAG,IAAI,CAAC;IAE1C,IAAIG,cAAc,GAAGN,MAAM,GAAGhG,IAAI,CAACC,KAAK,CAAC+F,MAAM,CAAC,GAAG,SAAS;IAE5D,IAAIO,aAAa,GAAGvG,IAAI,CAACC,KAAK,CAAC,KAAK,GAAGqG,cAAc,CAAC;IAEtD,IAAIE,OAAO,GAAGD,aAAa,GAAG,EAAE;IAEhCA,aAAa,IAAIC,OAAO;IAExB,IAAIpT,KAAK,GAAG4M,IAAI,CAACC,KAAK,CAACsG,aAAa,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACjD,IAAIlT,OAAO,GAAG2M,IAAI,CAACC,KAAK,CAACsG,aAAa,GAAG,EAAE,CAAC,GAAG,EAAE;IAEjD,OAAO,IAAIF,IAAI,CACXD,SAAS,CAACK,WAAW,CAAC,CAAC,EACvBL,SAAS,CAACM,QAAQ,CAAC,CAAC,EACpBN,SAAS,CAACO,OAAO,CAAC,CAAC,EACnBvT,KAAK,EACLC,OAAO,EACPmT,OACJ,CAAC;EACL,CAAC,MAAM;IACH;IACA,IAAII,MAAM,GAAG,cAAc;IAC3B,IAAIC,YAAY,GAAGb,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE7H,OAAO,CAACyI,MAAM,EAAE,EAAE,CAAC;IAC9C,IAAIE,UAAU,GAAG3X,MAAM,CAAC0X,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC;IACzD,OAAOC,UAAU,CAAC/D,OAAO,CAAC,CAAC,GAAGsD,IAAI,CAACxU,KAAK,CAACgV,YAAY,CAAC,GAAGlJ,SAAS;EACtE;AACJ,CAAC;AAED,OAAO,MAAMoJ,4BAA4B,GAAIC,SAAS,IAAK;EACvD,IAAIC,WAAW,GAAG,EAAE;EACpBD,SAAS,CAACE,WAAW,CAAChV,GAAG,CAAC,CAAC;IAAEiV;EAAO,CAAC,KAAK;IACtCA,MAAM,CAACjV,GAAG,CAAEkV,WAAW,IAAK;MACxBH,WAAW,CAACpL,IAAI,CAACuL,WAAW,CAAC;IACjC,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOH,WAAW,CAACxG,IAAI,CAAC,GAAG,CAAC;AAChC,CAAC;AACD,OAAO,MAAMpD,uBAAuB,GAAGA,CAAC1B,OAAO,EAAEzC,KAAK,KAAK;EACvD,IAAImO,WAAW,GAAGnO,KAAK;EACvB,IAAIoO,KAAK,CAACpY,OAAO,CAACyM,OAAO,CAAC,EAAE;IACxB,IAAI2L,KAAK,CAACpY,OAAO,CAACgK,KAAK,CAAC,EAAE;MACtB;MACAmO,WAAW,GAAGnO,KAAK,CACdhH,GAAG,CAAEqV,WAAW,IAAK;QAClB,MAAMC,cAAc,GAAG7L,OAAO,CAAC8L,IAAI,CAC9BC,YAAY,IAAKA,YAAY,CAACxO,KAAK,KAAKqO,WAC7C,CAAC;QACD,OAAOC,cAAc,GAAGA,cAAc,CAACpM,KAAK,GAAGmM,WAAW;MAC9D,CAAC,CAAC,CACD9G,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACH;MACA9E,OAAO,CAACzJ,GAAG,CAAEwV,YAAY,IAAK;QAC1B,IAAIA,YAAY,CAACxO,KAAK,IAAIA,KAAK,EAAE;UAC7BmO,WAAW,GAAGK,YAAY,CAACtM,KAAK;QACpC;MACJ,CAAC,CAAC;IACN;EACJ;EACA,OAAOiM,WAAW;AACtB,CAAC;AAED,MAAMM,aAAa,GAAG,SAAAA,CAAU7E,IAAI,EAAE8B,IAAI,EAAE;EACxC9B,IAAI,CAAC8E,OAAO,CAAC9E,IAAI,CAAC6D,OAAO,CAAC,CAAC,GAAG/B,IAAI,CAAC;EACnC,OAAO9B,IAAI;AACf,CAAC;AAED,OAAO,MAAM+E,UAAU,GAAGA,CAACC,OAAO,EAAEC,MAAM,KAAK;EAC3C,IAAIjF,IAAI,GAAG,IAAIuD,IAAI,CAACyB,OAAO,CAAC;EAC5B,OAAOhF,IAAI,CAACkF,kBAAkB,CAACD,MAAM,EAAE;IAAEE,OAAO,EAAE;EAAO,CAAC,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAGA,CACjCC,SAAS,EACTC,QAAQ,EACRC,SAAS,GAAG,KAAK,KAChB;EACD,OAAOC,mBAAmB,CACtBH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE7Q,MAAM,CAAC,CAAC,EACnB8Q,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9Q,MAAM,CAAC,CAAC,EAClB+Q,SACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMC,mBAAmB,GAAGA,CAACH,SAAS,EAAEC,QAAQ,EAAEC,SAAS,GAAG,KAAK,KAAK;EAC3EtW,OAAO,CAAC+G,GAAG,CAAC,WAAW,EAAEqP,SAAS,EAAEC,QAAQ,CAAC;EAC7C,IAAIG,SAAS,GAAG,IAAIjB,KAAK,CAAC,CAAC;EAC3B,IAAI3P,WAAW,GAAGwQ,SAAS;EAC3B,OAAOxQ,WAAW,IAAIyQ,QAAQ,EAAE;IAC5BG,SAAS,CAAC1M,IAAI,CAACiI,yBAAyB,CAAC3U,MAAM,CAACwI,WAAW,CAAC,CAAC,CAAC;IAC9DA,WAAW,GAAGgQ,aAAa,CAAChQ,WAAW,EAAE,CAAC,CAAC;EAC/C;EACA,IAAI,CAAC0Q,SAAS,IAAIE,SAAS,CAAC9V,MAAM,GAAG,CAAC,EAAE;IACpC8V,SAAS,CAACC,KAAK,CAAC,CAAC;IACjBD,SAAS,CAACE,GAAG,CAAC,CAAC;EACnB;EACA,OAAOF,SAAS;AACpB,CAAC;AAED,OAAO,MAAMG,6BAA6B,GAAGA,CAAA,KAAM;EAC/C,OAAO;IACHC,KAAK,EAAE,CAACxZ,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC;IAC3BwZ,KAAK,EAAE,CAACxZ,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC;IAC3ByZ,QAAQ,EAAE,CAACzZ,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5D,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,aAAa,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACjE,cAAc,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACnE,YAAY,EAAE,CAAC1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE1Z,MAAM,CAAC,CAAC,CAAC0Z,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC;EACpE,CAAC;AACL,CAAC;AAED,OAAO,MAAMC,iCAAiC,GAAGA,CAAA,KAAM;EACnD,OAAO;IACHH,KAAK,EAAE,CAACxZ,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC;IAC3B4Z,SAAS,EAAE,CAAC5Z,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE7Z,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACvE,aAAa,EAAE,CAAC7Z,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE7Z,MAAM,CAAC,CAAC,CAAC;IACvD,WAAW,EAAE,CACTA,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,OAAO,CAAC,MAAM,CAAC,EAC7C9Z,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACE,KAAK,CAAC,MAAM,CAAC,CAC9C;IACD,eAAe,EAAE,CAAC/Z,MAAM,CAAC,CAAC,CAAC8Z,OAAO,CAAC,OAAO,CAAC,EAAE9Z,MAAM,CAAC,CAAC,CAAC;IACtD,gBAAgB,EAAE,CACdA,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,EAC/C9Z,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAACE,KAAK,CAAC,OAAO,CAAC,CAChD;IACD,cAAc,EAAE,CAAC/Z,MAAM,CAAC,CAAC,CAAC8Z,OAAO,CAAC,MAAM,CAAC,EAAE9Z,MAAM,CAAC,CAAC;EACvD,CAAC;AACL,CAAC;AAED,OAAO,MAAMga,sBAAsB,GAAGA,CAAChS,KAAK,EAAEiS,IAAI,KAAK;EACnD,MAAMC,OAAO,GAAG/Q,QAAQ,CAACgR,cAAc,CAAC,SAAS,CAAC;EAClD,IAAIF,IAAI,IAAIA,IAAI,IAAI,EAAE,EAAE;IACpBC,OAAO,CAAC7Q,IAAI,GAAG4Q,IAAI;EACvB;EACA,MAAMG,UAAU,GAAGjR,QAAQ,CAACgR,cAAc,CAAC,YAAY,CAAC;EACxD,IAAInS,KAAK,IAAIA,KAAK,IAAI,EAAE,EAAE;IACtBoS,UAAU,CAACC,SAAS,GAAGrS,KAAK;EAChC;AACJ,CAAC;AAED,OAAO,MAAMsS,uBAAuB,GAAGA,CAAA,KAAM;EACzC,IAAIC,YAAY,GAAG,IAAI1Q,eAAe,CAClCV,QAAQ,CAACC,QAAQ,CAAC2M,MAAM,CAACjH,SAAS,CAAC,CAAC,CACxC,CAAC;EACD,IAAI0L,YAAY,GAAG,CAAC,CAAC;EACrBD,YAAY,CAAC7V,OAAO,CAAC,CAACqF,KAAK,EAAE5H,GAAG,KAAMqY,YAAY,CAACrY,GAAG,CAAC,GAAG4H,KAAM,CAAC;EACjE;EACA,OAAOyQ,YAAY;AACvB,CAAC;;AAED;AACA,OAAO,MAAMtH,6BAA6B,GAAGA,CAACuH,MAAM,EAAEC,MAAM,EAAEC,UAAU,KAAK;EACzE,IAAIC,CAAC,GAAGD,UAAU;EAClB,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC;EACjB,IAAIE,EAAE,GAAG,CAACD,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;EACxB,IAAIE,EAAE,GAAG,CAAC,GAAGD,EAAE;EACf,IAAIE,GAAG,GAAG,CACNnK,IAAI,CAACoK,KAAK,CAACR,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,GAAGJ,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,CAAC,EAC3ClK,IAAI,CAACoK,KAAK,CAACR,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,GAAGJ,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,CAAC,EAC3ClK,IAAI,CAACoK,KAAK,CAACR,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,GAAGJ,MAAM,CAAC,CAAC,CAAC,GAAGK,EAAE,CAAC,CAC9C;EACD,OAAO,MAAM,GAAGC,GAAG,CAAC1J,IAAI,CAAC,CAAC,GAAG,GAAG;AACpC,CAAC;AAED,OAAO,MAAM0B,+BAA+B,GAAGA,CAC3CkI,SAAS,EACTC,oBAAoB,KACnB;EACD,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIF,SAAS,EAAE;IACX,IAAIG,YAAY,GAAGlD,KAAK,CAACmD,IAAI,CAACnD,KAAK,CAAC+C,SAAS,GAAG,CAAC,CAAC,CAAC3L,IAAI,CAAC,CAAC,CAAC;IAC1D8L,YAAY,GAAGA,YAAY,CAACtY,GAAG,CAAC,CAACgH,KAAK,EAAE2D,KAAK,KAAK;MAC9C,IAAI6N,YAAY,GAAG,CAAC,CAAC;MACrB,IAAIJ,oBAAoB,EAAE;QACtBI,YAAY,GAAGJ,oBAAoB,CAACzN,KAAK,CAAC;MAC9C;MAEA,OAAO;QAAEzB,KAAK,EAAE,EAAE,GAAGyB,KAAK;QAAE3D,KAAK,EAAE2D,KAAK;QAAE,GAAG6N;MAAa,CAAC;IAC/D,CAAC,CAAC;IACF;IACAH,kBAAkB,CAAC1O,IAAI,CAAC,GAAG2O,YAAY,CAAC;EAC5C;EACA,OAAOD,kBAAkB;AAC7B,CAAC;AAED,OAAO,MAAMI,yBAAyB,GAAGA,CAAClO,UAAU,EAAEC,SAAS,KAAK;EAChE;EACAD,UAAU,GAAGA,UAAU,CAACE,MAAM,CAC1B,CAACC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,KAClBA,IAAI,CAAC5K,GAAG,CAAE6K,GAAG,IAAKA,GAAG,CAACzL,GAAG,CAAC,CAAC0L,OAAO,CAACJ,QAAQ,CAACtL,GAAG,CAAC,KAAKuL,KAC7D,CAAC;EAED,IAAI+N,iBAAiB,GAAG,EAAE;EAC1BnO,UAAU,CAAC5I,OAAO,CAAEqJ,eAAe,IAAK;IACpC,IAAI5L,GAAG,GAAG4L,eAAe,CAAC5L,GAAG;IAC7B,IAAIoL,SAAS,CAACpL,GAAG,CAAC,EAAE;MAAA,IAAAuZ,sBAAA,EAAAC,sBAAA;MAChB,OAAO5N,eAAe,CAAC,SAAS,CAAC;MACjC,IAAIA,eAAe,CAAC5B,MAAM,IAAI,aAAa,EAAE;QACzC4B,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAMlC,MAAM,CAACiO,GAAG,CAAClE,KAAK,CAAC,CAACrB,MAAM,CAAC,aAAa,CAAO,CACtD;MACL,CAAC,MAAM,IAAIqF,eAAe,CAAC5B,MAAM,IAAI,QAAQ,EAAE;QAC3C4B,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GACKgM,uBAAuB,CACpBH,eAAe,CAACvB,OAAO,EACvBzC,KACJ,CACE,CACT;MACL,CAAC,MAAM,IAAI,EAAA2R,sBAAA,GAAA3N,eAAe,CAAC5B,MAAM,cAAAuP,sBAAA,uBAAtBA,sBAAA,CAAwBvN,WAAW,KAAI,MAAM,EAAE;QACtDJ,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA,CAAChC,IAAI;UAACmK,KAAK,GAAGgE,eAAe,CAACvB,OAAO,EAAEzC,KAAK,CAAE;UAAC8R,QAAQ;UAAAha,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAC5D;MACL,CAAC,MAAM,IAAI6L,eAAe,CAAC8F,WAAW,IAAI,YAAY,IAAI,EAAA8H,sBAAA,GAAA5N,eAAe,CAAC5B,MAAM,cAAAwP,sBAAA,uBAAtBA,sBAAA,CAAwBhX,IAAI,KAAI,YAAY,EAAE;QACpGoJ,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,IAAK;UACpC;UACA,IAAI+R,WAAW,GAAG,EAAE;UACpB,IAAI9b,MAAM,CAACgU,QAAQ,CAACjK,KAAK,CAAC,EAAE;YACxB+R,WAAW,GAAG/R,KAAK,CAACrB,MAAM,CAAC,SAAS,CAAC;UACzC,CAAC,MAAM,IAAIqB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;YAC3C,MAAMgK,UAAU,GAAG/T,MAAM,CAAC+J,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC;YAC9E+R,WAAW,GAAG/H,UAAU,CAACH,OAAO,CAAC,CAAC,GAAGG,UAAU,CAACrL,MAAM,CAAC,SAAS,CAAC,GAAGqB,KAAK;UAC7E;UACA,oBAAOtK,KAAA,CAAAmC,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAO4Z,WAAkB,CAAC;QACrC,CAAC;MACL,CAAC,MAAM,IAAI/N,eAAe,CAAC5B,MAAM,IAAI,aAAa,EAAE;QAChD4B,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GACKgM,uBAAuB,CACpBH,eAAe,CAACvB,OAAO,EACvBzC,KACJ,CACE,CACT;MACL,CAAC,MAAM,IAAIgE,eAAe,CAAC5B,MAAM,IAAI,gBAAgB,EAAE;QACnD4B,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GACKgM,uBAAuB,CACpBH,eAAe,CAACvB,OAAO,EACvBzC,KACJ,CACE,CACT;MACL,CAAC,MAAM,IAAIgE,eAAe,CAAC5B,MAAM,EAAE;QAC/B,IAAIpC,KAAK,GAAGwD,SAAS,CAACpL,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI4H,KAAK,CAACkC,KAAK,EAAE;UACb8B,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,iBAC/BtK,KAAA,CAAAmC,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAO6H,KAAK,CAACkC,KAAY,CAC5B;QACL;MACJ,CAAC,MAAM,IAAI8B,eAAe,CAAC5L,GAAG,IAAI,SAAS,EAAE;QACzC4L,eAAe,CAAC6N,UAAU,GAAI7R,KAAK,IAAK;UACpC,OAAOA,KAAK;QAChB,CAAC;MACL;MACA;MACA0R,iBAAiB,CAAC/O,IAAI,CAACqB,eAAe,CAAC;IAC3C;EACJ,CAAC,CAAC;EAEF,OAAO0N,iBAAiB;AAC5B,CAAC;AAED,OAAO,MAAMM,YAAY,GAAGA,CAAA,KAAM;EAC9B,OAAO7b,MAAM,CAAC,CAAC;AACnB,CAAC;AAED,OAAO,MAAM8b,MAAM,GAAGA,CAAA,KAAM;EACxB,oBAAOvc,KAAA,CAAAmC,aAAA,CAAC/B,KAAK;IAAC,eAAY,SAAS;IAACoc,KAAK,EAAEpc,KAAK,CAACqc,sBAAuB;IAAAra,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;AAC/E,CAAC;AAED,OAAO,MAAMia,mBAAmB,GAAGA,CAAA,KAAM;EACrC,IAAIvL,aAAa,GAAG,CAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACZ;EACD,OAAOA,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,aAAa,CAACtN,MAAM,CAAC,CAAC;AAC1E,CAAC;AAED,OAAO,MAAM8Y,qBAAqB,GAAIpM,QAAQ,IAAK;EAC/C,IAAIqM,cAAc,GAAG;IACjBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE;EACjB,CAAC;EACD,OAAOJ,cAAc,CAACrM,QAAQ,CAAC,GAAGqM,cAAc,CAACrM,QAAQ,CAAC,GAAG,SAAS;AAC1E,CAAC;AAED,OAAO,MAAM0M,YAAY,GAAGA,CAAA,KAAM;EAAA,IAAAC,UAAA;EAC9B,MAAMC,UAAU,IAAAD,UAAA,GAAGE,SAAS,cAAAF,UAAA,uBAATA,UAAA,CAAWG,SAAS;EACvCla,OAAO,CAAC+G,GAAG,CAAC,YAAY,EAAEiT,UAAU,CAAC;EAErC,OAAOA,UAAU,CAACzZ,QAAQ,CAAC,WAAW,CAAC;AAC3C,CAAC;AAED,OAAO,MAAM4Z,aAAa,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAK;EACrC;EACA,OAAOxa,IAAI,CAACuI,SAAS,CAACgS,EAAE,CAAC,IAAIva,IAAI,CAACuI,SAAS,CAACiS,EAAE,CAAC;AACnD,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAACnT,KAAK,EAAEoT,OAAO,EAAEhb,GAAG,KAAK;EACtD,IAAIib,QAAQ,GAAG,CAAC,CAAC;EACjBA,QAAQ,CAACjb,GAAG,CAAC,GAAG,EAAE;EAClB,IAAI4H,KAAK,IAAIyE,SAAS,EAAE;IAAA,IAAA6O,gBAAA;IACpB,IAAIF,OAAO,aAAPA,OAAO,wBAAAE,gBAAA,GAAPF,OAAO,CAAEG,OAAO,cAAAD,gBAAA,uBAAhBA,gBAAA,CAAkBE,cAAc,EAAE;MAClCJ,OAAO,CAACG,OAAO,CAACC,cAAc,CAACH,QAAQ,CAAC;IAC5C;EACJ;AACJ,CAAC;AAED,OAAO,MAAMI,+BAA+B,GAAGA,CAACC,eAAe,EAAEN,OAAO,KAAK;EACzE,IAAIA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,EAAE;IAAA,IAAAI,iBAAA;IAClB,MAAMC,MAAM,GAAGR,OAAO,aAAPA,OAAO,wBAAAO,iBAAA,GAAPP,OAAO,CAAEG,OAAO,cAAAI,iBAAA,uBAAhBA,iBAAA,CAAkBE,aAAa,CAAC,SAAS,CAAC;IACzD,IAAI,CAAAH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,OAAO,KAAIrP,SAAS,IAAImP,MAAM,IAAInP,SAAS,EAAE;MAC9D2O,OAAO,CAACG,OAAO,CAACC,cAAc,CAAC;QAAEM,OAAO,EAAE;MAAa,CAAC,CAAC;IAC7D;EACJ,CAAC,MAAM;IACHC,UAAU,CAAC,MAAM;MACbN,+BAA+B,CAACC,eAAe,CAAC;IACpD,CAAC,EAAE,GAAG,CAAC;EACX;AACJ,CAAC;AAED,OAAO,MAAMM,0BAA0B,GAAGA,CACtCC,2BAA2B,EAC3BC,WAAW,EACXC,kBAAkB,EAClBC,gBAAgB,EAChBhB,OAAO,EACPiB,OAAO,KACN;EACD,IAAI,CAAAJ,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAE1a,MAAM,IAAG,CAAC,EAAE;IACzC0a,2BAA2B,CAACtZ,OAAO,CAC9B2Z,mCAAmC,IAAK;MAAA,IAAAC,iBAAA;MACrC,IAAIC,UAAU,GAAG;QAAE,GAAGF;MAAoC,CAAC;MAC3DE,UAAU,CAACpc,GAAG,GACV8b,WAAW,GAAG,GAAG,GAAGI,mCAAmC,CAAClc,GAAG;MAC/Doc,UAAU,CAAC,UAAU,CAAC,GAAG,MAAMH,OAAO,CAAC,CAAC;MACxCF,kBAAkB,CAACxR,IAAI,CAAC6R,UAAU,CAAC;MACnCJ,gBAAgB,CACZ,IAAIE,mCAAmC,CAAClc,GAAG,GAAG,CACjD,GAAGgb,OAAO,aAAPA,OAAO,wBAAAmB,iBAAA,GAAPnB,OAAO,CAAEG,OAAO,cAAAgB,iBAAA,uBAAhBA,iBAAA,CAAkBV,aAAa,CAACW,UAAU,CAACpc,GAAG,CAAC;IACvD,CACJ,CAAC;EACL;AACJ,CAAC;;AAED;AACA,OAAO,MAAMqc,2BAA2B,GAAIC,OAAO,IAAK;EACpD,IAAIlP,IAAI,GAAG1E,MAAM,CAAC0E,IAAI,CAACkP,OAAO,CAAC;EAC/B,KAAK,IAAIhb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8L,IAAI,CAACjM,MAAM,EAAEG,CAAC,EAAE,EAAE;IAClC,IAAIib,SAAS,GAAGnP,IAAI,CAAC9L,CAAC,CAAC;IACvB,IAAIgb,OAAO,CAACC,SAAS,CAAC,IAAI,IAAI,IAAID,OAAO,CAACC,SAAS,CAAC,IAAIlQ,SAAS,EAAE;MAC/D,OAAOiQ,OAAO,CAACC,SAAS,CAAC;IAC7B;EACJ;EACA,OAAOD,OAAO;AAClB,CAAC;AAED,OAAO,SAASE,gCAAgCA,CAAC1L,MAAM,EAAE;EACrD,IAAI;IACA,IAAIA,MAAM,IAAI,CAAC,EAAE;MACb,MAAM2L,KAAK,GAAG3L,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE4L,cAAc,CAAC,OAAO,CAAC,CAAC7a,KAAK,CAAC,GAAG,CAAC;MACxD,MAAM8a,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC5P,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC;MAChE,IAAI4P,KAAK,CAACtb,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,GAAGwb,SAAS,IAAIF,KAAK,CAAC,CAAC,CAAC,EAAE;MACrC,CAAC,MAAM;QACH,OAAOE,SAAS;MACpB;IACJ;EACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;IACR,OAAO9L,MAAM;EACjB;AACJ;AAEA,OAAO,SAAS+L,sBAAsBA,CAAC/L,MAAM,EAAE;EAC3C,MAAMgM,QAAQ,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnE,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIjM,MAAM,IAAI,QAAQ,EAAE;IACpBiM,QAAQ,GAAG,CAAC,CAAC,CAAC;IACdjM,MAAM,GAAGA,MAAM,GAAG,QAAQ;EAC9B,CAAC,MAAM,IAAIA,MAAM,IAAI,MAAM,EAAE;IACzBiM,QAAQ,GAAG,CAAC,CAAC,CAAC;IACdjM,MAAM,GAAGA,MAAM,GAAG,MAAM;EAC5B,CAAC,MAAM,IAAIA,MAAM,IAAI,IAAI,EAAE;IACvBiM,QAAQ,GAAG,CAAC,CAAC,CAAC;IACdjM,MAAM,GAAGA,MAAM,GAAG,IAAI;EAC1B;EACA,IAAIiM,QAAQ,GAAG,CAAC,EAAE;IACd;IACA,IAAIC,MAAM,CAACC,SAAS,CAACnM,MAAM,CAAC,EAAE;MAC1B,OAAOA,MAAM,GAAGgM,QAAQ,CAACC,QAAQ,CAAC;IACtC,CAAC,MAAM;MACH,OAAOjM,MAAM,CAACnL,OAAO,CAAC,CAAC,CAAC,GAAGmX,QAAQ,CAACC,QAAQ,CAAC;IACjD;EACJ;EACA,OAAOjM,MAAM;AACjB;AAEA,OAAO,MAAMoM,YAAY,GAAGA,CACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,MAAM,KACL;EACDF,oBAAoB,GAChBA,oBAAoB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACjC,GAAG,GACHH,oBAAoB,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EAClC,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,YAAY,EAAE;IAClD,IAAIC,aAAa,GAAG,EAAE;;IAEtB;IACA,IAAIC,cAAc,GAAGC,UAAU,CAACJ,WAAW,CAAC;;IAE5C;IACA,IAAIK,eAAe,GAAGF,cAAc,GAAGF,YAAY;IACnD,IAAIK,QAAQ,GAAGC,UAAU,CAACF,eAAe,CAAC;IAC1CH,aAAa,CAACnT,IAAI,CAAC;MACfkT,YAAY,EAAEK,QAAQ;MACtBD,eAAe,EAAEJ;IACrB,CAAC,CAAC;;IAEF;IACA,OAAOA,YAAY,GAAG,CAAC,EAAE;MACrBA,YAAY,GAAG/O,IAAI,CAACsP,IAAI,CAACP,YAAY,GAAGJ,MAAM,CAAC;MAC/C,IAAIY,mBAAmB,GAAGN,cAAc,GAAGF,YAAY;MACvD,IAAIS,YAAY,GAAGH,UAAU,CAACE,mBAAmB,CAAC;MAClDP,aAAa,CAACnT,IAAI,CAAC;QACfkT,YAAY,EAAES,YAAY;QAC1BL,eAAe,EAAEJ;MACrB,CAAC,CAAC;IACN;IAEA,SAASG,UAAUA,CAACjc,IAAI,EAAE;MACtB,IAAI,CAACG,KAAK,EAAEC,OAAO,EAAEH,MAAM,CAAC,GAAGD,IAAI,CAC9B6K,KAAK,CAAC,yBAAyB,CAAC,CAChC8Q,KAAK,CAAC,CAAC,CAAC;MACbxb,KAAK,GAAGE,QAAQ,CAACF,KAAK,CAAC;MACvBC,OAAO,GAAGC,QAAQ,CAACD,OAAO,CAAC;MAE3B,IAAIH,MAAM,KAAK,IAAI,IAAIE,KAAK,KAAK,EAAE,EAAE;QACjCA,KAAK,IAAI,EAAE;MACf,CAAC,MAAM,IAAIF,MAAM,KAAK,IAAI,IAAIE,KAAK,KAAK,EAAE,EAAE;QACxCA,KAAK,GAAG,CAAC;MACb;MAEA,OAAOA,KAAK,GAAG,EAAE,GAAGC,OAAO;IAC/B;;IAEA;IACA,SAASgc,UAAUA,CAAChc,OAAO,EAAE;MACzB,IAAID,KAAK,GAAG4M,IAAI,CAACC,KAAK,CAAC5M,OAAO,GAAG,EAAE,CAAC,GAAG,EAAE;MACzC,IAAIoc,IAAI,GAAGpc,OAAO,GAAG,EAAE;MACvB,IAAIH,MAAM,GAAG8M,IAAI,CAACC,KAAK,CAAC5M,OAAO,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;MACzDD,KAAK,GAAGA,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,KAAK;MAEhC,OAAOsc,OAAO,CAACtc,KAAK,CAAC,GAAG,GAAG,GAAGsc,OAAO,CAACD,IAAI,CAAC,GAAG,GAAG,GAAGvc,MAAM;IAC9D;;IAEA;IACA,SAASwc,OAAOA,CAACtN,MAAM,EAAE;MACrB,OAAOA,MAAM,CAAC/H,QAAQ,CAAC,CAAC,CAACsV,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7C;IACA,OAAOX,aAAa;EACxB;EAEA,SAASY,gBAAgBA,CAACC,OAAO,EAAE;IAC/B,MAAM,CAAC5c,IAAI,EAAE6c,QAAQ,CAAC,GAAGD,OAAO,CAAC1c,KAAK,CAAC,GAAG,CAAC;IAC3C,IAAI,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGJ,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IACtCC,KAAK,GAAGE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC;IAC3BC,OAAO,GAAGC,QAAQ,CAACD,OAAO,EAAE,EAAE,CAAC;IAE/B,IAAIyc,QAAQ,KAAK,IAAI,IAAI1c,KAAK,KAAK,EAAE,EAAE;MACnCA,KAAK,IAAI,EAAE;IACf,CAAC,MAAM,IAAI0c,QAAQ,KAAK,IAAI,IAAI1c,KAAK,KAAK,EAAE,EAAE;MAC1CA,KAAK,GAAG,CAAC;IACb;IACA,MAAM2c,YAAY,GAAG,IAAI1J,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEjT,KAAK,EAAEC,OAAO,CAAC,CAAC2c,OAAO,CAAC,CAAC;IAChE,OAAOD,YAAY;EACvB;EACA,IAAIE,SAAS,GAAGpB,iBAAiB,CAC7BJ,oBAAoB,EACpBC,qBACJ,CAAC;EACD,OAAOuB,SAAS;AACpB,CAAC;AAED,OAAO,MAAMC,0BAA0B,GAAIjX,GAAG,IAAK;EAC/C,IAAIA,GAAG,EAAE;IACLe,MAAM,CAAC0E,IAAI,CAACzF,GAAG,CAAC,CAAC/G,GAAG,CAAEZ,GAAG,IAAK;MAC1B,MAAM4H,KAAK,GAAGD,GAAG,CAAC3H,GAAG,CAAC;MACtB,IAAI6e,WAAW,CAACjX,KAAK,CAAC,EAAE;QACpB;QACA,MAAM4N,UAAU,GAAG3X,MAAM,CAAC+J,KAAK,EAAE,IAAI,CAAC;QACtC,IAAI4N,UAAU,CAAC/D,OAAO,CAAC,CAAC,EAAE;UACtB9J,GAAG,CAAC3H,GAAG,CAAC,GAAGwV,UAAU;QACzB;MACJ;IACJ,CAAC,CAAC;EACN;EAEA,OAAO7N,GAAG;AACd,CAAC;AACD,SAASmX,qBAAqBA,CAACC,WAAW,EAAE;EACxC,OAAO,CAAC5Z,KAAK,CAAC4P,IAAI,CAACxU,KAAK,CAACwe,WAAW,CAAC,CAAC;AAC1C;AAEA,SAASF,WAAWA,CAACG,UAAU,EAAE;EAC7B,IAAI;IACA,IAAIC,KAAK,GAAG,qBAAqB;IACjC,IAAI,CAACD,UAAU,CAACxS,KAAK,CAACyS,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;IAC5C,IAAIC,CAAC,GAAG,IAAInK,IAAI,CAACiK,UAAU,CAAC;IAC5B,IAAIG,IAAI,GAAGD,CAAC,CAACR,OAAO,CAAC,CAAC;IACtB,IAAI,CAACS,IAAI,IAAIA,IAAI,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;IACvC,OAAOD,CAAC,CAACE,WAAW,CAAC,CAAC,CAAC9B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK0B,UAAU;EACtD,CAAC,CAAC,OAAOxe,KAAK,EAAE;IACZ,OAAO,KAAK;EAChB;AACJ;AAEA,OAAO,MAAM6e,yBAAyB,GAAGA,CAACjS,IAAI,EAAE/B,MAAM,KAAK;EACvD5K,OAAO,CAAC+G,GAAG,CAAC,QAAQ,EAAE4F,IAAI,EAAE/B,MAAM,CAAC;EACnC,MAAMiU,OAAO,GAAGzhB,MAAM,CAAC,CAAC;EACxB,MAAMgZ,SAAS,GAAGhZ,MAAM,CAAC,CAAC,CAAC6Z,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;EAChDrM,MAAM,CAAC+B,IAAI,CAAC,GAAG,CAACyJ,SAAS,EAAEyI,OAAO,CAAC,CAAC1e,GAAG,CAAE4Q,IAAI,IAAKA,IAAI,CAAC4N,WAAW,CAAC,CAAC,CAAC;EACrE,OAAO/T,MAAM;AACjB,CAAC;AAED,OAAO,MAAMkU,oBAAoB,GAAGA,CAACC,WAAW,GAAG,KAAK,KAAK;EACzD,IAAIC,gBAAgB,GAAGD,WAAW,GAAG,gBAAgB,GAAG,iBAAiB;EACzE,OAAO,CACH;IAAE5X,KAAK,EAAE,GAAG6X,gBAAgB,IAAI;IAAE3V,KAAK,EAAE;EAAc,CAAC,EACxD;IAAElC,KAAK,EAAE,GAAG6X,gBAAgB,IAAI;IAAE3V,KAAK,EAAE;EAAI,CAAC,EAC9C;IAAElC,KAAK,EAAE,GAAG6X,gBAAgB,IAAI;IAAE3V,KAAK,EAAE;EAAI,CAAC,EAC9C;IAAElC,KAAK,EAAE,GAAG6X,gBAAgB,IAAI;IAAE3V,KAAK,EAAE;EAAI,CAAC,EAC9C;IAAElC,KAAK,EAAE,GAAG6X,gBAAgB,IAAI;IAAE3V,KAAK,EAAE;EAAY,CAAC,CACzD;AACL,CAAC;AACD,OAAO,MAAM4V,QAAQ,GAAGA,CAAC1f,GAAG,EAAEqK,OAAO,KAAK;EACtC,IAAIP,KAAK;EACT,IAAI,CAAAO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAElJ,MAAM,IAAG,CAAC,EAAE;IACrBkJ,OAAO,CAAC9H,OAAO,CAAE6T,YAAY,IAAK;MAC9B,IAAIA,YAAY,CAACxO,KAAK,IAAI5H,GAAG,EAAE;QAC3B8J,KAAK,GAAGsM,YAAY,CAACtM,KAAK;MAC9B;IACJ,CAAC,CAAC;EACN;EACA,OAAOA,KAAK;AAChB,CAAC;AAED,OAAO,MAAM6V,WAAW,GAAIX,UAAU,IAAK;EACvC,IAAIY,SAAS,GAAG,IAAI7K,IAAI,CAACiK,UAAU,CAAC;EACpC,IAAI3Y,WAAW,GAAG,IAAI0O,IAAI,CAAC,CAAC;EAC5B,OAAO6K,SAAS,CAACC,YAAY,CAAC,CAAC,KAAKxZ,WAAW,CAACwZ,YAAY,CAAC,CAAC;AAClE,CAAC;AAED,OAAO,MAAMC,eAAe,GAAId,UAAU,IAAK;EAC3C,IAAIY,SAAS,GAAG,IAAI7K,IAAI,CAACiK,UAAU,CAAC;EACpC,IAAI3Y,WAAW,GAAG,IAAI0O,IAAI,CAAC,CAAC;EAC5B,IAAIgL,SAAS,GAAG,IAAIhL,IAAI,CAAC,CAAC;EAC1BgL,SAAS,CAACzJ,OAAO,CAACjQ,WAAW,CAACgP,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5C,OAAOuK,SAAS,CAACC,YAAY,CAAC,CAAC,KAAKE,SAAS,CAACF,YAAY,CAAC,CAAC;AAChE,CAAC;AAED,OAAO,MAAMG,qBAAqB,GAAGA,CAACC,IAAI,EAAErY,KAAK,EAAE5D,QAAQ,KAAK;EAC5D;EACA;EACA,MAAMkc,WAAW,GACb,8FAA8F;EAElG,IAAI,CAACtY,KAAK,IAAIsY,WAAW,CAACC,IAAI,CAACvY,KAAK,CAAC,EAAE;IACnC5D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,MAAM;IACHA,QAAQ,CAAC,iDAAiD,CAAC,CAAC,CAAC;EACjE;AACJ,CAAC;AAED,OAAO,MAAMoc,iBAAiB,GAAGA,CAACH,IAAI,EAAErY,KAAK,EAAE5D,QAAQ,KAAK;EACxD,MAAMqc,gBAAgB,GAClB,2DAA2D;EAC/D,IAAI,CAACzY,KAAK,IAAIyY,gBAAgB,CAACF,IAAI,CAACvY,KAAK,CAAC,EAAE;IACxC5D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,MAAM;IACHA,QAAQ,CAAC,iCAAiC,CAAC,CAAC,CAAC;EACjD;AACJ,CAAC;AAED,OAAO,MAAMsc,iBAAiB,GAAGA,CAAA,KAAM;EACnC,IAAIC,mBAAmB,GAAG,CAAC;EAC3B,IAAIC,qBAAqB,GAAG,EAAE;EAC9BA,qBAAqB,GAAG3P,+BAA+B,CACnD0P,mBAAmB,EAClBzP,MAAM,KAAM;IACTxG,KAAK,EAAEyG,6BAA6B,CAChC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IAAE;IACd,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;IAAE;IACfD,MAAM,GAAGyP,mBACb;EACJ,CAAC,CACL,CAAC,CAAClV,MAAM,CAAEnM,IAAI,IAAKA,IAAI,CAAC0I,KAAK,IAAI,CAAC,CAAC;EAEnC,OAAO;IACH5H,GAAG,EAAE,cAAc;IACnB8J,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,QAAQ;IAChBuF,KAAK,EAAE,IAAI;IACXtF,WAAW,EAAE;MACT;MACAuF,IAAI,EAAE,UAAU;MAChBpF,gBAAgB,EAAE,UAAU;MAC5BD,UAAU,EAAE;IAChB,CAAC;IACDE,OAAO,EAAEmW;EACb,CAAC;AACL,CAAC;AACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,OAAO1d,MAAM,CAAC2d,UAAU,IAAI,GAAG;AACnC,CAAC;AACD,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EACpC,OAAO5d,MAAM,CAAC6d,gBAAgB,IAAI,IAAI;AAC1C,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAGA,CAAC;EACpCzV,SAAS;EACT4P,OAAO;EACP8F,aAAa;EACbC,kBAAkB;EAClBC,oBAAoB;EACpBC,oBAAoB;EACpBC,eAAe;EACfC;AACJ,CAAC,KAAK;EAAA,IAAAC,iBAAA,EAAAC,iBAAA;EACF,MAAMC,aAAa,GACf9iB,6BAA6B,CAAC2iB,MAAM,EAAEnG,OAAO,CAAC,IAC9Ctc,8BAA8B,CAACyiB,MAAM,EAAE/V,SAAS,EAAE4P,OAAO,CAAC;EAC9D,MAAMuG,qBAAqB,GAAG,CAAC,CAACD,aAAa;EAC7C,MAAME,QAAQ,GACV,CAAAxG,OAAO,aAAPA,OAAO,wBAAAoG,iBAAA,GAAPpG,OAAO,CAAEG,OAAO,cAAAiG,iBAAA,uBAAhBA,iBAAA,CAAkB3F,aAAa,CAAC,mBAAmB,CAAC,MACpDrQ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEqW,iBAAiB;EAChC,MAAMC,SAAS,GACX,CAAA1G,OAAO,aAAPA,OAAO,wBAAAqG,iBAAA,GAAPrG,OAAO,CAAEG,OAAO,cAAAkG,iBAAA,uBAAhBA,iBAAA,CAAkB5F,aAAa,CAAC,oBAAoB,CAAC,MACrDrQ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuW,kBAAkB;EAEjC,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,iBAAA,EAAAC,iBAAA;IACpC,MAAMN,QAAQ,GAAGxG,OAAO,aAAPA,OAAO,wBAAA6G,iBAAA,GAAP7G,OAAO,CAAEG,OAAO,cAAA0G,iBAAA,uBAAhBA,iBAAA,CAAkBpG,aAAa,CAAC,mBAAmB,CAAC;IACrE,MAAMiG,SAAS,GAAG1G,OAAO,aAAPA,OAAO,wBAAA8G,iBAAA,GAAP9G,OAAO,CAAEG,OAAO,cAAA2G,iBAAA,uBAAhBA,iBAAA,CAAkBrG,aAAa,CAAC,oBAAoB,CAAC;IACvE,IAAI+F,QAAQ,IAAIE,SAAS,EAAE;MAAA,IAAAK,aAAA;MACvB,MAAMzV,IAAI,GAAG,MAAMhO,0BAA0B,CAACkjB,QAAQ,EAAEE,SAAS,CAAC;MAClErjB,WAAW,CAACI,aAAa,EAAAsjB,aAAA,GAACzV,IAAI,CAAC0V,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAe,CAAC,CAAC,CAAC,EAAE/G,OAAO,EAAEmG,MAAM,CAAC;IAClE;EACJ,CAAC;EAED,IAAIc,wBAAwB,GAAG,IAAIlN,IAAI,CAAC,CAAC,CAAC2J,OAAO,CAAC,CAAC;EACnD,MAAMwD,YAAY,GAAIlH,OAAO,IAAK;IAC9B,MAAMmH,aAAa,GAAG,CAAC,CAAC;IACxB5jB,mBAAmB,CAAC4iB,MAAM,CAAC,CAAC5e,OAAO,CAAE0Q,SAAS,IAAK;MAC/CkP,aAAa,CAAClP,SAAS,CAAC,GAAG,EAAE;IACjC,CAAC,CAAC;IACFgP,wBAAwB,GAAG,IAAIlN,IAAI,CAAC,CAAC,CAAC2J,OAAO,CAAC,CAAC;IAC/C1D,OAAO,CAACG,OAAO,CAACC,cAAc,CAAC+G,aAAa,CAAC;IAC7CrB,aAAa,CAAC,CAAC;EACnB,CAAC;EAED,MAAMsB,IAAI,GAAG;IACTC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACItiB,GAAG,EAAE,sBAAsB;MAC3BuiB,MAAM,EAAEA,CAAA,kBACJjlB,KAAA,CAAAmC,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACIzC,KAAA,CAAAmC,aAAA,CAACrB,mBAAmB;QAChB2L,WAAW,EAAC,SAAS;QACrByY,eAAe,EAAE,IAAK;QACtBC,QAAQ,EAAGC,OAAO,IAAK;UACnBrkB,WAAW,CAACqkB,OAAO,EAAE1H,OAAO,EAAEmG,MAAM,CAAC;UACrCL,aAAa,CAAC,CAAC;QACnB,CAAE;QACFI,eAAe,EAAEA,eAAgB;QACjCyB,YAAY,EAAEV,wBAAyB;QAAAviB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAC1C,CAAC,eACFzC,KAAA,CAAAmC,aAAA,CAAC9B,MAAM;QAACilB,OAAO,EAAE5B,oBAAqB;QAAAthB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,aAE/B,CAAC,EACRghB,kBAAkB,iBACfzjB,KAAA,CAAAmC,aAAA,CAACd,YAAY;QACToiB,kBAAkB,EAAEA,kBAAmB;QACvC8B,eAAe,EAAE;UACbnV,GAAG,EAAE8T,QAAQ,IAAI,UAAU;UAC3B7T,GAAG,EAAE+T,SAAS,IAAI;QACtB,CAAE;QACFe,QAAQ,EAAGC,OAAO,IAAK;UACnBrkB,WAAW,CAACqkB,OAAO,EAAE1H,OAAO,EAAEmG,MAAM,CAAC;UACrCL,aAAa,CAAC,CAAC;UACfE,oBAAoB,CAAC,CAAC;QAC1B,CAAE;QACFA,oBAAoB,EAAEA,oBAAqB;QAAAthB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAC9C,CAEJ;IAEb,CAAC,EACD;MACIC,GAAG,EAAE,cAAc;MACnB8iB,OAAO,EAAE,CAAC;MACVhZ,KAAK,EAAE,cAAc;MACrByY,MAAM,EAAEA,CAAA,KACJhB,qBAAqB,iBACjBjkB,KAAA,CAAAmC,aAAA,CAAC9B,MAAM;QACH0F,IAAI,EAAC,MAAM;QACXuf,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAAClH,OAAO,CAAE;QAAAtb,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GACxC,eAEO;IAEpB,CAAC,EACD;MACIC,GAAG,EAAE,GAAGmhB,MAAM,QAAQ;MACtB2B,OAAO,EAAE,CAAC;MACVhZ,KAAK,EAAE,SAAS;MAChB2Y,QAAQ,EAAE3B,aAAa;MACvBiC,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAG,CAAC;IACvB,CAAC,EACD;MACIhjB,GAAG,EAAE,GAAGmhB,MAAM,QAAQ;MACtB2B,OAAO,EAAE,CAAC;MACVhZ,KAAK,EAAE,yBAAyB;MAChC2Y,QAAQ,EAAE3B,aAAa;MACvBiC,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAI,CAAC;IACxB,CAAC,EACD;MACIhjB,GAAG,EAAE,GAAGmhB,MAAM,QAAQ;MACtBrX,KAAK,EAAE,QAAQ;MACfgZ,OAAO,EAAE,CAAC;MACVpJ,QAAQ,EAAE,IAAI;MACdqJ,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAK,CAAC;IACzB,CAAC,EACD;MACIhjB,GAAG,EAAE,GAAGmhB,MAAM,QAAQ;MACtBrX,KAAK,EAAE,QAAQ;MACfgZ,OAAO,EAAE,CAAC;MACVpJ,QAAQ,EAAE,IAAI;MACdqJ,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAI,CAAC;IACxB,CAAC,EACD;MACIhjB,GAAG,EAAE,GAAGmhB,MAAM,SAAS;MACvBrX,KAAK,EAAE,SAAS;MAChBgZ,OAAO,EAAE,CAAC;MACV/O,QAAQ,EAAEkN,oBAAoB;MAC9BvH,QAAQ,EAAE,IAAI;MACdzP,WAAW,EAAE;QACTuF,IAAI,EAAE,QAAQ;QACdjI,GAAG,EAAE,WAAW;QAChBE,MAAM,EAAE;UAAEwb,EAAE,EAAE;QAAa,CAAC;QAC5BhZ,WAAW,EAAE;UACTuF,IAAI,EAAE,QAAQ;UACd0T,YAAY,EAAE,KAAK;UACnB/Y,UAAU,EAAE,IAAI;UAChBgZ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAC3B;MACJ;IACJ,CAAC,EACD;MACIpjB,GAAG,EAAE,GAAGmhB,MAAM,MAAM;MACpBrX,KAAK,EAAE,MAAM;MACbgZ,OAAO,EAAE,CAAC;MACVpJ,QAAQ,EAAE,IAAI;MACdzP,WAAW,EAAE;QACTuF,IAAI,EAAE,QAAQ;QACdjI,GAAG,EAAE,WAAW;QAChBE,MAAM,EAAE;UAAEwb,EAAE,EAAE;QAAY,CAAC;QAC3BhZ,WAAW,EAAE;UACTuF,IAAI,EAAE,QAAQ;UACd0T,YAAY,EAAE,KAAK;UACnB/Y,UAAU,EAAE,IAAI;UAChBgZ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAC3B;MACJ;IACJ,CAAC,EACD;MACIpjB,GAAG,EAAE,GAAGmhB,MAAM,OAAO;MACrBrX,KAAK,EAAE,OAAO;MACdgZ,OAAO,EAAE,CAAC;MACVpJ,QAAQ,EAAE,IAAI;MACdzP,WAAW,EAAE;QACTuF,IAAI,EAAE,QAAQ;QACdjI,GAAG,EAAE,WAAW;QAChBE,MAAM,EAAE;UAAEwb,EAAE,EAAE;QAAW,CAAC;QAC1BhZ,WAAW,EAAE;UACTuF,IAAI,EAAE,QAAQ;UACd0T,YAAY,EAAE,KAAK;UACnB/Y,UAAU,EAAE,IAAI;UAChBgZ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAC3B;MACJ;IACJ,CAAC,EACD;MACIpjB,GAAG,EAAE,mBAAmB;MACxB8J,KAAK,EAAE,UAAU;MACjBgZ,OAAO,EAAE,CAAC;MACV;MACA/Y,WAAW,EAAE,YAAY;MACzB0Y,QAAQ,EAAG7F,CAAC,IAAK;QACbgF,mBAAmB,CAAC,CAAC;QACrBd,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC,EACD;MACI9gB,GAAG,EAAE,oBAAoB;MACzB8J,KAAK,EAAE,WAAW;MAClBgZ,OAAO,EAAE,CAAC;MACV;MACA/Y,WAAW,EAAE,cAAc;MAC3B0Y,QAAQ,EAAG7F,CAAC,IAAK;QACbgF,mBAAmB,CAAC,CAAC;QACrBd,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC,EACD,IAAIU,QAAQ,IAAIE,SAAS,GACnB,CACI;MACI1hB,GAAG,EAAE,eAAe;MACpBuiB,MAAM,EAAEA,CAAA,KAAM;QACV,MAAMhb,GAAG,GAAG,mDAAmDia,QAAQ,IAAIE,SAAS,EAAE;QACtF,oBACIpkB,KAAA,CAAAmC,aAAA;UAAGyH,IAAI,EAAEK,GAAI;UAAC8b,MAAM,EAAC,QAAQ;UAAA3jB,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACzBzC,KAAA,CAAAmC,aAAA;UAAGD,SAAS,EAAC,oBAAoB;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAC,GAAI,CAAC,sBAExC,CAAC;MAEZ;IACJ,CAAC,CACJ,GACD,EAAE,CAAC;EAEjB,CAAC;EACD,OAAOqiB,IAAI;AACf,CAAC;AAED,SACIpO,iBAAiB,EACjBF,qBAAqB,EACrBH,eAAe,EACfR,gBAAgB,EAChBP,2BAA2B,EAC3Bb,YAAY,EACZnE,4BAA4B,EAC5BpD,UAAU,EACVwD,sBAAsB,EACtBrH,uBAAuB,EACvBsK,0BAA0B,EAC1BzC,oBAAoB,EACpBK,gBAAgB,EAChBE,iBAAiB,EACjBK,oBAAoB,EACpBC,sBAAsB,EACtBoR,YAAY,EACZE,yBAAyB;AAE7B,OAAO,MAAM2C,gBAAgB,GAAGA,CAACC,IAAI,GAAG,GAAG,EAAEC,WAAW,GAAG,EAAE,KAAK;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,YAAA;EAC9DH,WAAW,GACP,OAAOA,WAAW,KAAK,QAAQ,GACzBA,WAAW,GACXI,MAAM,CAACJ,WAAW,IAAI,EAAE,CAAC;EACnCD,IAAI,GAAGA,IAAI,CAAC1W,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACgX,IAAI,CAAC,CAAC;EACrC,MAAMC,aAAa,GAAGC,qBAAqB,CAAC,CAAC;EAC7C,MAAMC,WAAW,GAAGhmB,aAAa,CAACimB,4BAA4B,CAAC,CAAC;EAChE,MAAMC,YAAY,GAAGF,WAAW,aAAXA,WAAW,wBAAAP,gBAAA,GAAXO,WAAW,CAAEG,GAAG,cAAAV,gBAAA,uBAAhBA,gBAAA,CAAkB3L,IAAI;EAC3C,MAAMsM,WAAW,GAAG,CAAAJ,WAAW,aAAXA,WAAW,wBAAAN,iBAAA,GAAXM,WAAW,CAAEG,GAAG,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAkBW,QAAQ,KAAI,EAAE;;EAEpD;EACA,MAAMC,WAAW,GAAIC,WAAW,IAAK;IACjC,MAAMC,QAAQ,GAAGD,WAAW,CAAC1iB,KAAK,CAAC,GAAG,CAAC;IACvC;IACA,OAAO2iB,QAAQ,CAACrjB,MAAM,GAAG,CAAC,EAAE;MACxB,MAAMsjB,aAAa,GAAGD,QAAQ,CAACrV,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;MAC/C,IAAI2U,aAAa,CAACW,aAAa,CAAC,EAAE;QAC9B,OAAOA,aAAa;MACxB;MACAD,QAAQ,CAACrN,GAAG,CAAC,CAAC;IAClB;IACA,OAAO,GAAG,CAAC,CAAC;EAChB,CAAC;EACD,MAAMuN,QAAQ,GAAGJ,WAAW,CAACf,IAAI,CAAC;EAClC,MAAMoB,aAAa,GAAGb,aAAa,CAACY,QAAQ,CAAC,IAAI,EAAE;EAEnD,IAAIE,SAAS,GAAG,GAAGR,WAAW,IAAIO,aAAa,EAAE;EACjD;EACA,IAAI,EAAAhB,YAAA,GAAAH,WAAW,cAAAG,YAAA,uBAAXA,YAAA,CAAaE,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;IAC5Be,SAAS,IAAI,MAAMpB,WAAW,EAAE;EACpC;EACAoB,SAAS,IAAI,MAAM;EAEnB/M,sBAAsB,CAAC+M,SAAS,EAAEV,YAAY,CAAC;AACnD,CAAC;AAED,OAAO,SAASH,qBAAqBA,CAAA,EAAG;EACpC,MAAMc,KAAK,GAAG7d,QAAQ,CAAC8d,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;;EAE9C,OAAO9O,KAAK,CAACmD,IAAI,CAAC0L,KAAK,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IAC3C,MAAM/d,IAAI,GAAG+d,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC;IAEtC,MAAM3d,GAAG,GAAG,IAAI5D,GAAG,CAACuD,IAAI,EAAEnE,MAAM,CAACkE,QAAQ,CAACke,MAAM,CAAC,CAAC,CAAC;IACnD,MAAM5c,QAAQ,GAAGhB,GAAG,CAACgB,QAAQ,CAAC,CAAC;;IAE/B,MAAM6c,WAAW,GAAGpP,KAAK,CAACmD,IAAI,CAAC8L,IAAI,CAACI,UAAU,CAAC,CAC1Cha,MAAM,CAAEia,IAAI,IAAK;MACd,OACKA,IAAI,CAACC,QAAQ,KAAKC,IAAI,CAACC,SAAS,IAC7BH,IAAI,CAACF,WAAW,CAACvB,IAAI,CAAC,CAAC,KAAK,EAAE,IAClCyB,IAAI,CAACI,QAAQ,KAAK,MAAM,IACxBJ,IAAI,CAACI,QAAQ,KAAK,KAAK;IAE/B,CAAC,CAAC,CACD9kB,GAAG,CAAE0kB,IAAI,IAAK;MACX;MACA,IAAIrmB,IAAI,GAAGqmB,IAAI,CAACF,WAAW,CAACvB,IAAI,CAAC,CAAC;MAClC5kB,IAAI,GAAGA,IAAI,CAAC4N,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACgX,IAAI,CAAC,CAAC,CAAC,CAAC;MAChD,OAAO5kB,IAAI;IACf,CAAC,CAAC,CACDkQ,IAAI,CAAC,GAAG,CAAC;IAEd,IAAI5G,QAAQ,IAAI6c,WAAW,EAAE;MACzBJ,GAAG,CAACzc,QAAQ,CAAC,GAAG6c,WAAW;IAC/B;IACA,OAAOJ,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,OAAO,MAAMW,sBAAsB,GAAG,WAAW;AACjD,OAAO,MAAMC,kBAAkB,GAAG;EAC9BC,SAAS,EAAE;IACPje,KAAK,EAAE,WAAW;IAClBke,cAAc,EAAE;MAAEle,KAAK,EAAE;IAAiB;EAC9C,CAAC;EACDme,aAAa,EAAE;IACXne,KAAK,EAAE,eAAe;IACtBoe,gBAAgB,EAAE;EACtB,CAAC;EACDC,WAAW,EAAE;IACTre,KAAK,EAAE,aAAa;IACpBse,MAAM,EAAE;EACZ;AACJ,CAAC;AAED,OAAO,SAASC,mBAAmBA,CAAC;EAAEnmB,GAAG;EAAE4H;AAAM,CAAC,EAAE;EAChD,MAAMwe,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACX,sBAAsB,CAAC;;EAEhE;EACA,IAAI,CAACS,WAAW,EAAE;IACdC,YAAY,CAACE,OAAO,CAACZ,sBAAsB,EAAErlB,IAAI,CAACuI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE;EAEA,IAAI2d,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIJ,WAAW,EAAE;IACb,IAAI;MACAI,YAAY,GAAGlmB,IAAI,CAACC,KAAK,CAAC6lB,WAAW,CAAC;IAC1C,CAAC,CAAC,OAAO5lB,KAAK,EAAE;MACZC,OAAO,CAAC+G,GAAG,CAAC,uCAAuC,EAAEhH,KAAK,CAAC;IAC/D;EACJ;;EAEA;EACA,IAAI,CAACgmB,YAAY,CAACxmB,GAAG,CAAC,EAAE;IACpBwmB,YAAY,CAACxmB,GAAG,CAAC,GAAG4H,KAAK;EAC7B,CAAC,MAAM;IACH;IACA,IACI,OAAO4e,YAAY,CAACxmB,GAAG,CAAC,KAAK,QAAQ,IACrC,OAAO4H,KAAK,KAAK,QAAQ,EAC3B;MACE4e,YAAY,CAACxmB,GAAG,CAAC,GAAG;QAAE,GAAGwmB,YAAY,CAACxmB,GAAG,CAAC;QAAE,GAAG4H;MAAM,CAAC,CAAC,CAAC;IAC5D,CAAC,MAAM;MACH4e,YAAY,CAACxmB,GAAG,CAAC,GAAG4H,KAAK,CAAC,CAAC;IAC/B;EACJ;;EAEA;EACAye,YAAY,CAACE,OAAO,CAACZ,sBAAsB,EAAErlB,IAAI,CAACuI,SAAS,CAAC2d,YAAY,CAAC,CAAC;AAC9E;AAEA,OAAO,MAAMC,aAAa,GAAGA,CAAC9e,GAAG,EAAE+e,SAAS,KAAK;EAC7C,IAAI/e,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzC,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,CAACnC,cAAc,CAACkhB,SAAS,CAAC,EAAE;IAC/B,OAAO/e,GAAG,CAAC+e,SAAS,CAAC;EACzB;EACA,KAAK,MAAMC,CAAC,IAAIhf,GAAG,EAAE;IACjB,MAAMpD,MAAM,GAAGkiB,aAAa,CAAC9e,GAAG,CAACgf,CAAC,CAAC,EAAED,SAAS,CAAC;IAC/C,IAAIniB,MAAM,KAAK,IAAI,EAAE;MACjB,OAAOA,MAAM;IACjB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AAED,OAAO,SAASqiB,oBAAoBA,CAAC;EAAE5mB;AAAI,CAAC,EAAE;EAC1C,MAAMomB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACX,sBAAsB,CAAC;EAEhE,IAAIS,WAAW,EAAE;IACb,IAAI;MACA,MAAMS,WAAW,GAAGvmB,IAAI,CAACC,KAAK,CAAC6lB,WAAW,CAAC;MAC3C,OAAOK,aAAa,CAACI,WAAW,EAAE7mB,GAAG,CAAC;IAC1C,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZC,OAAO,CAAC+G,GAAG,CAAC,uCAAuC,EAAEhH,KAAK,CAAC;MAC3D,OAAO,IAAI;IACf;EACJ;EAEA,OAAO,IAAI,CAAC,CAAC;AACjB;AACA,OAAO,SAASsmB,sBAAsBA,CAAC;EAAE9mB;AAAI,CAAC,EAAE;EAC5C,MAAMomB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAACX,sBAAsB,CAAC;EAEhE,IAAIS,WAAW,EAAE;IACb,IAAI;MACA,MAAMS,WAAW,GAAGvmB,IAAI,CAACC,KAAK,CAAC6lB,WAAW,CAAC;;MAE3C;MACA,MAAMW,eAAe,GAAGA,CAACpf,GAAG,EAAE+e,SAAS,KAAK;QACxC,IAAI/e,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACzC,OAAO,KAAK,CAAC,CAAC;QAClB;QAEA,IAAIA,GAAG,CAACnC,cAAc,CAACkhB,SAAS,CAAC,EAAE;UAC/B,OAAO/e,GAAG,CAAC+e,SAAS,CAAC;UACrB,OAAO,IAAI,CAAC,CAAC;QACjB;QAEA,KAAK,MAAMC,CAAC,IAAIhf,GAAG,EAAE;UACjB,IAAIof,eAAe,CAACpf,GAAG,CAACgf,CAAC,CAAC,EAAED,SAAS,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,CAAC;UACjB;QACJ;QACA,OAAO,KAAK,CAAC,CAAC;MAClB,CAAC;MAED,MAAMM,SAAS,GAAGD,eAAe,CAACF,WAAW,EAAE7mB,GAAG,CAAC;MAEnD,IAAIgnB,SAAS,EAAE;QACXX,YAAY,CAACE,OAAO,CAChBZ,sBAAsB,EACtBrlB,IAAI,CAACuI,SAAS,CAACge,WAAW,CAC9B,CAAC;MACL,CAAC,MAAM;QACHpmB,OAAO,CAACwmB,IAAI,CAAC,QAAQjnB,GAAG,cAAc,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZC,OAAO,CAAC+G,GAAG,CAAC,uCAAuC,EAAEhH,KAAK,CAAC;IAC/D;EACJ,CAAC,MAAM;IACHC,OAAO,CAACwmB,IAAI,CAAC,oBAAoBtB,sBAAsB,GAAG,CAAC;EAC/D;AACJ;AAEA,OAAO,MAAMuB,gBAAgB,GAAIC,OAAO,IAAK;EACzC,MAAMvnB,QAAQ,GAAGwnB,iBAAiB,CAACD,OAAO,CAAC;EAC3C,OAAOpoB,kBAAkB,CAACsoB,IAAI,CAACznB,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,OAAO,MAAMwnB,iBAAiB,GAAID,OAAO,IAAK;EAC1CA,OAAO,GAAGG,SAAS,CAACH,OAAO,CAACtlB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,OAAOslB,OAAO,CAACxa,SAAS,CAACwa,OAAO,CAACI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;EAChD,OAAOhf,MAAM,CAAC0E,IAAI,CAACqa,SAAS,CAAC,CAACE,KAAK,CAAE3nB,GAAG,IAAK;IACzC,MAAM4nB,cAAc,GAAGH,SAAS,CAACznB,GAAG,CAAC;IACrC,MAAM6nB,aAAa,GAAGH,QAAQ,CAAC1nB,GAAG,CAAC;;IAEnC;IACA,IAAIgW,KAAK,CAACpY,OAAO,CAACgqB,cAAc,CAAC,IAAI5R,KAAK,CAACpY,OAAO,CAACiqB,aAAa,CAAC,EAAE;MAC/D,OACIvnB,IAAI,CAACuI,SAAS,CAAC+e,cAAc,CAAC,KAAKtnB,IAAI,CAACuI,SAAS,CAACgf,aAAa,CAAC;IAExE;IAEA,OAAOD,cAAc,KAAKC,aAAa;EAC3C,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,OAAO,EAAEC,iBAAiB,KAAK;EAAA,IAAAC,mBAAA;EAClE,IAAIC,kBAAkB,GAAG7b,SAAS;EAClC,IAAI2b,iBAAiB,EAAE;IACnBA,iBAAiB,CAACpnB,GAAG,CAAEunB,mBAAmB,IAAK;MAC3C,IAAIA,mBAAmB,CAACC,OAAO,IAAIL,OAAO,EAAE;QACxCG,kBAAkB,GAAGC,mBAAmB;MAC5C;IACJ,CAAC,CAAC;EACN;EACA,QAAAF,mBAAA,GAAOC,kBAAkB,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBI,WAAW;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}