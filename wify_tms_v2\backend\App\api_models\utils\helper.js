const moment = require('moment');

const fs = require('fs');
const stream = require('stream');
const path = require('path');
const crypto = require('crypto');

exports.getStreamFromVariable = (var_) => {
    // Your variable with content to save
    const variableContent = JSON.stringify(var_);

    // Generate a random string for the file name
    const randomString = crypto.randomBytes(8).toString('hex');

    // Create a unique file name by combining the random string with a timestamp
    const timestamp = Date.now();
    const tempFileName = `tempfile_${timestamp}_${randomString}.txt`;

    // Set the directory where you want to create the temporary file
    const tempDirectory = './temp_files';

    // Full path to the temporary file
    const tempFilePath = path.join(tempDirectory, tempFileName);

    // Write the variable content to the temporary file
    fs.writeFileSync(tempFilePath, variableContent);

    // Create a writable stream (for example, you can pipe it to the HTTP response)
    const writableStream = new stream.Writable();

    writableStream._write = (chunk, encoding, next) => {
        // Do something with the streamed data, such as sending it over an HTTP response
        // console.log(chunk.toString());
        next();
    };

    // Create a readable stream from the temporary file
    const readStream = fs.createReadStream(tempFilePath);
    // Pipe the readable stream (file content) to the writable stream
    readStream.pipe(writableStream);

    // Delete the temporary file when done
    readStream.on('end', () => {
        setTimeout(() => {
            fs.unlink(tempFilePath, (err) => {
                if (err) {
                    console.error(`Error deleting temporary file: ${err}`);
                } else {
                    console.log('Temporary file deleted.');
                }
            });
        }, 20000); // Adding a buffer of 20 secs for the job to execute
    });
    return readStream;
};

exports.getCurrentDateTime = () => {
    const currentDateAndTime = moment.utc();
    const date = currentDateAndTime.format('YYYY-MM-DD'); //'YYYY-MM-DD'
    return { date, currentDateAndTime };
};

exports.getTmrwDate = () => {
    const currentDateAndTime = moment.utc().add(1, 'days');
    const date = currentDateAndTime.format('YYYY-MM-DD'); //'YYYY-MM-DD'
    return date;
};

exports.getYesterdayDate = () => {
    const currentDateAndTime = moment.utc().add(-1, 'days');
    const date = currentDateAndTime.format('YYYY-MM-DD'); //'YYYY-MM-DD'
    return date;
};

exports.getMandatoryFieldsKeyNotFilled = (srvc_form_data, mandatory_fields) => {
    // Recursively checks if a given key is present in nested objects and arrays.
    //True if the key is present and not empty, false otherwise.
    let missingKeys = [];
    function isKeyPresentInNestedObjects(obj, keyToCheck) {
        for (const key in obj) {
            if (key == keyToCheck) {
                if (obj[key] != null && obj[key] != '') {
                    return true;
                }
                // Check if the value is an array with at least one element.
                if (Array.isArray(obj[key]) && obj[key]?.length > 0) {
                    return true;
                }
            }
            // Recurse into nested objects.
            if (typeof obj[key] == 'object' && obj[key] != null) {
                if (isKeyPresentInNestedObjects(obj[key], keyToCheck)) {
                    return true;
                }
            }
        }
        return false;
    }
    //  Check if mandatory_fields array is provided.
    if (mandatory_fields) {
        // Iterate through each mandatory field key.
        mandatory_fields.map((key) => {
            if (!isKeyPresentInNestedObjects(srvc_form_data, key)) {
                missingKeys.push(key);
            }
        });
    }
    return missingKeys;
};

exports.getTheLabelFrMandatoryFields = (customFields, mandatoryFieldsKey) => {
    let labelFrFields = [];
    let selectedFieldsFrReadiness = [];
    if (mandatoryFieldsKey) {
        let getFieldsMetaFrReadiness = [...staticFields];
        if (customFields) {
            getFieldsMetaFrReadiness.push(...customFields);
        }
        mandatoryFieldsKey.forEach((singleKey) => {
            let filteredSelectedFields = getFieldsMetaFrReadiness.filter(
                (singlefield) => singlefield.key == singleKey
            )?.[0];
            if (filteredSelectedFields) {
                selectedFieldsFrReadiness.push(filteredSelectedFields);
            }
        });
    }
    if (selectedFieldsFrReadiness) {
        selectedFieldsFrReadiness.forEach((singleValue) => {
            labelFrFields.push(singleValue?.label);
        });
    }
    return labelFrFields;
};

const staticFields = [
    { key: 'attachments', label: 'Attachments' },
    { key: 'camera_files', label: 'Files' },
    { key: 'cust_city', label: 'City' },
    { key: 'cust_email', label: 'Email' },
    { key: 'cust_full_name', label: 'Name' },
    { key: 'cust_line_0', label: 'Flat no' },
    { key: 'cust_line_1', label: 'Building/Apartment name' },
    { key: 'cust_line_2', label: 'Line 1' },
    { key: 'cust_line_3', label: 'Line 2' },
    { key: 'cust_mobile', label: 'Mobile' },
    { key: 'cust_pincode', label: 'Pincode' },
    { key: 'cust_state', label: 'State' },
    { key: 'request_description', label: 'Description' },
    { key: 'request_req_date', label: 'Req. Service Date' },
    { key: 'request_labels', label: 'Labels' },
    { key: 'initial_status', label: 'Initial Status' },
    { key: 'creation_date', label: 'Creation Date' },
    { key: 'request_cc_users', label: 'CC users' },
    { key: 'request_priority', label: 'Priority' },
    { key: 'general', label: 'Attachments' },
];

exports.getRandomInteger = (min, max) => {
    // Ensure that min and max are integers
    min = Math.floor(min);
    max = Math.floor(max);

    // Add 1 to the range to include the maximum value
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

exports.formatDate = (dateString) => {
    try {
        const date = new Date(dateString);
        const month = date.toLocaleString('default', { month: 'short' });
        const day = date.getDate();
        const year = date.getFullYear();
        // Replace spaces with hyphens
        const formattedDate = `${month}-${day}-${year}`;
        return formattedDate;
    } catch (error) {
        //swallow
    }
    return dateString;
};

exports.removeAndAppendSixDigitIntegers = (inputString) => {
    try {
        const regex = /\b\d{6}, /; // Matches consecutive 6-digit numbers followed by a comma and a space
        const matches = inputString.match(regex);
        if (!matches) {
            return inputString; // No matches found, return the input as is
        }
        // Remove the matched 6-digit integers from the input string
        const stringWithoutIntegers = inputString.replace(regex, '').trim();
        // Remove any double commas, remove the comma following the 6-digit numbers, and then append the matched 6-digit integers at the end, separated by a single comma
        const cleanedString = stringWithoutIntegers.replace(/,+/g, ',').trim();
        const appendedString =
            cleanedString.replace(/,\s*$/, '') + ', ' + matches.join(', ');
        return appendedString.slice(0, -2);
    } catch (error) {
        //swallow
    }
    return inputString;
};

exports.get5MonthsDateRangeFilter = () => {
    const currentDate = new Date();
    const pastDate = new Date();

    // Subtract 5 months from the current date
    pastDate.setMonth(currentDate.getMonth() - 5);

    // Format dates as ISO strings
    const currentISODate = currentDate.toISOString();
    const pastISODate = pastDate.toISOString();

    return [pastISODate, currentISODate];
};

exports.cleanJsonString = (args) => {
    return args.text
        .replace(/```json|```/g, '') // Remove markdown code block markers
        .replace(/\\n/g, '') // Remove existing escaped newlines (optional)
        .replace(/\n/g, '') // Remove newlines
        .replace(/\\\"/g, '"') // Unescape double quotes if necessary
        .replace(/,\s*([\]}])/g, '$1') // Remove trailing commas before } or ]
        .replace(/([{[])\s*,/g, '$1') // Remove leading commas after { or [
        .replace(/\s*:\s*/g, ':') // Remove spaces around colons
        .replace(/\s*,\s*/g, ',') // Remove spaces around commas
        .replace(/\t/g, '\\t') // Escape tab characters
        .replace(/{\s+/g, '{') // Remove spaces after opening brace
        .replace(/\s+}/g, '}') // Remove spaces before closing brace
        .trim();
};

function parseJsonStrings(args) {
    console.log('parseJsonStrings :: args :: ', args);
    const { text } = args;
    try {
        return JSON.parse(text, (_, value) =>
            typeof value === 'string'
                ? parseJsonStrings({ text: value })
                : value
        );
    } catch (error) {
        console.log('parseJsonStrings :: error :: ', error?.message);
        return text;
    }
}
exports.parseJsonStrings = parseJsonStrings;

function encodeInnerJsonStrings(data) {
    for (const key in data) {
        if (typeof data[key] === 'string') {
            data[key] = Buffer.from(data[key]).toString('base64');
        } else if (typeof data[key] === 'object' && data[key] !== null) {
            encodeInnerJsonStrings(data[key]);
        }
    }
    return data;
}

exports.encodeInnerJsonStrings = encodeInnerJsonStrings;

exports.deleteFileFromLocal = ({ filePath }) => {
    return new Promise((resolve, reject) => {
        // Check if the file exists
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                console.error(`File not found: ${filePath}`);
                return reject(`File not found: ${filePath}`);
            }

            // Attempt to delete the file
            fs.unlink(filePath, (err) => {
                if (err) {
                    console.error(`Error deleting file: ${filePath}`, err);
                    return reject(`Error deleting file: ${filePath}`);
                }

                console.log(`File deleted: ${filePath}`);
                resolve();
            });
        });
    });
};

exports.sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

exports.isValidJson = (str) => {
    try {
        JSON.parse(str);
        return true;
    } catch {
        return false;
    }
};

exports.moduleKeys = {
    siteAttendance: 'site-attendance',
    officeAttendance: 'office-attendance',
    dailyReport: 'daily-report',
    rangeReport: 'range-report',
    availability: 'availability',
    availabilityReport: 'availability-report',
    technicianAssignment: 'technician-assignment',
    users: 'users',
    serviceRequests: 'service-requests',
    projectsRequests: 'projects-requests',
    taskUpdates: 'task-updates',
    visitMap: 'visit-map',
    ratings: 'ratings',
    unallocatedPincode: 'unallocated-pincode',
    pincodesNotInHubs: 'pincodes-not-in-hubs',
};

exports.convertDateFieldsToMoments = (form_data, fieldsMeta = undefined) => {
    let returnData = form_data;
    if (returnData != undefined) {
        Object.keys(form_data).map((key) => {
            var fieldValue = form_data[key];
            if (fieldsMeta) {
                let matchingMeta = fieldsMeta.filter(
                    (fieldMeta) => fieldMeta.key == key
                );
                if (matchingMeta.length > 0) {
                    let fieldMeta = matchingMeta[0];
                    if (
                        fieldMeta.widget &&
                        typeof fieldMeta.widget === 'function' &&
                        fieldMeta.widget.name == DatePicker.RangePicker.name
                    ) {
                        //
                        if (
                            isArray(fieldValue) &&
                            fieldValue.length == 2 &&
                            fieldMeta.widgetProps?.ranges
                        ) {
                            returnData[key] = [
                                moment(fieldValue[0], true),
                                moment(fieldValue[1], true),
                            ];
                        }
                    } else if (fieldMeta.widget == 'date-picker') {
                        // console.log('value',value);
                        if (fieldValue && fieldValue != '') {
                            var date = moment(fieldValue, true);
                            if (date.isValid()) {
                                // console.log('Valid date field converted to moment',key,date)
                                returnData[key] = date
                                    .startOf('day')
                                    .format('YYYY-MM-DD');
                            } else {
                                returnData[key] = moment()
                                    .startOf('day')
                                    .format('YYYY-MM-DD'); // Default to today's date without time
                            }
                        } else {
                            returnData[key] = undefined;
                        }
                    }
                }
            } else {
                // Legacy
                if (typeof fieldValue === 'string') {
                    var date = moment(fieldValue, true);
                    if (date.isValid()) {
                        // console.log('Valid date field converted to moment',key,date)
                        returnData[key] = date;
                    }
                }
            }
        });
    }
    return returnData;
};

exports.parseFormulaToValue = (
    formula,
    nameToIdMapping = undefined,
    data,
    isSrvcReqLocked = true
) => {
    let matches = formula.match(/{[^{}]+}/g);
    matches.forEach((matchString) => {
        let word = matchString.substring(1, matchString.length - 1);
        let key = nameToIdMapping[word];
        let fieldValue = key in data ? data[key] : 'undefined';
        formula = formula.replace(`{${word}}`, fieldValue);
    });
    let result = 0;
    try {
        result = eval(formula);
        if (!isSrvcReqLocked) {
            result = parseFloat(result).toFixed(2);
        }
    } catch (error) {
        console.log('parseFormulaToValue error', error);
    }
    return result;
};

exports.getIdVsLabelMapping = (fields) => {
    const idVsLabelMapping = {};
    fields.forEach((singleFieldMeta) => {
        idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;
    });
    return idVsLabelMapping;
};

exports.parseFormulaToString = (formula, nameToIdMapping = undefined, data) => {
    // debugger;
    let matches = formula.match(/{[^{}]+}/g);
    matches.forEach((matchString) => {
        let word = matchString.substring(1, matchString.length - 1);
        let key = nameToIdMapping[word];
        let fieldValue = data[key] || 'N/A';
        formula = formula.replace(`{${word}}`, fieldValue);
    });
    return formula;
};

exports.hasDataChanged = (originalData, currentData, keysToCheck) => {
    const isEqual = (originalValue, currentValue) => {
        if (Array.isArray(originalValue) && Array.isArray(currentValue)) {
            return (
                JSON.stringify(originalValue) === JSON.stringify(currentValue)
            );
        }
        if (
            typeof originalValue === 'object' &&
            typeof currentValue === 'object'
        ) {
            return (
                JSON.stringify(originalValue) === JSON.stringify(currentValue)
            );
        }
        return originalValue === currentValue;
    };

    return keysToCheck.some(
        (key) => !isEqual(originalData[key], currentData[key])
    );
};
