{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\wify-utils\\\\FieldCreator\\\\mapping_form_builder.js\";\nimport { Rate } from 'antd';\nimport { unset } from 'lodash';\nimport React from 'react';\nimport CKEditorWidget from '../CKEditorWidget';\nimport { SiBluetooth } from 'react-icons/si';\nimport { TimePicker } from \"../form-builder/components/FormBuilder/FormInputs\";\n\n/*Caller must do try catch */\nexport const createAntdFormMeta = json => {\n  var parsedAntdFormMeta = JSON.parse(json).translatedFields;\n  var finalAntdFormMeta = [];\n  parsedAntdFormMeta.forEach(field => {\n    if (field.cust_component != undefined) {\n      field = getReactComponentFr(field);\n    }\n    if (field.cust_widget != undefined) {\n      const originalCustWidget = field.cust_widget; // Preserve the original value\n      field.widget = getReactWidgetFr(field.cust_widget);\n      // Keep cust_widget for convertDateFieldsToMoments to detect TimePicker fields\n      field.cust_widget = originalCustWidget;\n    }\n    finalAntdFormMeta.push(field);\n  });\n  return finalAntdFormMeta;\n};\nexport const getReactWidgetFr = cust_widget => {\n  switch (cust_widget) {\n    case 'Rating':\n      return Rate;\n      break;\n    case 'TimePicker':\n      return TimePicker;\n    default:\n      return /*#__PURE__*/React.createElement(\"h5\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 20\n        }\n      }, \"Unknown component - \", cust_widget);\n  }\n};\nexport const getReactComponentFr = field => {\n  let {\n    cust_component,\n    cust_component_value\n  } = field;\n  switch (cust_component) {\n    case 'legend':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"fieldset\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"legend\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }\n        }, /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }\n        }, cust_component_value)));\n      };\n      break;\n    case 'linebreak':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"fieldset\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"legend\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }\n        }, cust_component_value));\n      };\n      break;\n    case 'Mobile':\n      field.rules = [{\n        pattern: new RegExp('^[0-9]*$'),\n        message: 'Incorrect number'\n      }, {\n        min: 10\n      }, {\n        max: 10\n      }];\n      break;\n    case 'Files':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-upload gx-mr-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 25\n          }\n        }), \"This will be a drop zone for \", field.label);\n      };\n      break;\n    case 'WIFY_MIC':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-upload gx-mr-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }\n        }), \"This will be a Mic recorder for \", field.label);\n      };\n      break;\n    case 'WIFY_CAMERA':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-upload gx-mr-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }\n        }), \"This will be a camera input for \", field.label);\n      };\n      break;\n    case 'WIFY_BLE_COMPONENT':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          className: \"gx-d-flex gx-justify-content-start gx-align-items-centre\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }\n        }, /*#__PURE__*/React.createElement(SiBluetooth, {\n          className: \"gx-mr-2 gx-fs-18 gx-text-blue\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 29\n          }\n        }), /*#__PURE__*/React.createElement(\"div\", {\n          className: \"gx-mt-auto\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 29\n          }\n        }, \"This will be a section for \", field.label)));\n      };\n      break;\n    case 'WIFY_BARCODE_SCANNER':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-upload gx-mr-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }\n        }), \"This will be a Barcode Scanner for \", field.label);\n      };\n      break;\n    case 'WIFY_RICH_TEXT_INPUT':\n      field.widget = CKEditorWidget;\n      field.forwardRef = true;\n      // field.label = <h3>{field.label}</h3>\n      break;\n    case 'Button':\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          className: \"gx-border gx-border-blue gx-border-dashed gx-p-3\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"icon icon-upload gx-mr-2\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }\n        }), \"This will be a Button for \", field.label);\n      };\n      break;\n    default:\n      field.render = () => {\n        return /*#__PURE__*/React.createElement(\"h5\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 24\n          }\n        }, \"Unknown component - \", cust_component);\n      };\n      break;\n  }\n  return field;\n};\n\n// translatedEntry.rules = [\n//     {\n//         required: true,\n//         pattern: new RegExp(\"^[0-9]*$\"),\n//         message: 'Incorrect number',\n//     },\n//     { min : 10 },\n//     { max : 10 },\n// ]\n\nconst translateFormBuilderJsonToAntdBuilder = json => {\n  try {\n    var translatedFields = [];\n    var originalFields = JSON.parse(json);\n    // var key_creator = 1;\n    originalFields.forEach(singleField => {\n      var translatedEntry = {};\n      translatedEntry.key = singleField.id;\n      if (singleField.colSpan && singleField.colSpan != '') {\n        translatedEntry.colSpan = Number(singleField.colSpan); // common fields\n      }\n      translatedEntry.required = singleField.required; // common fields\n      translatedEntry.label = extractLabelFrmSingleField(singleField.label);\n      switch (singleField.element) {\n        case 'Header':\n          // translatedEntry.\n          translatedEntry.cust_component = 'legend';\n          translatedEntry.cust_component_value = extractLabelFrmSingleField(singleField.label);\n          unset(translatedEntry, 'label');\n          break;\n        case 'Tags':\n        case 'Dropdown':\n          // translatedEntry.\n          translatedEntry.widget = 'select';\n          translatedEntry.options = [];\n          if (singleField.element == 'Tags') {\n            translatedEntry.widgetProps = {\n              mode: 'multiple'\n            };\n          }\n          singleField.options.forEach(singleOption => {\n            if (singleField.element == 'Tags') {\n              translatedEntry.options.push({\n                label: singleOption.label,\n                value: singleOption.value\n              });\n            } else {\n              translatedEntry.options.push({\n                label: singleOption.value,\n                value: singleOption.id\n              });\n            }\n          });\n          break;\n        case 'LineBreak':\n          // translatedEntry.\n          translatedEntry.cust_component = 'linebreak';\n          translatedEntry.cust_component_value = '';\n          unset(translatedEntry, 'label');\n          // for loop to get options\n          break;\n        case 'Date':\n          translatedEntry.widget = 'date-picker';\n          translatedEntry.widgetProps = {\n            style: {\n              width: '100%'\n            }\n          };\n          break;\n        case 'TimePicker':\n          translatedEntry.widget = TimePicker;\n          translatedEntry.cust_widget = \"TimePicker\";\n          translatedEntry.widgetProps = {\n            format: 'hh:mm a',\n            use12Hours: true,\n            placeholder: 'Select time',\n            style: {\n              width: '100%'\n            }\n          };\n          break;\n        case 'TextArea':\n          translatedEntry.widget = 'textarea';\n          // translatedEntry.widgetProps = {style:{width:'100%'}};\n          break;\n        case 'NumberInput':\n          translatedEntry.widget = 'number';\n          // translatedEntry.widgetProps = {style:{width:'100%'}};\n          break;\n        case 'Email':\n          translatedEntry.rules = [{\n            type: 'email',\n            max: 100\n          }];\n          break;\n        case 'Rating':\n          translatedEntry.cust_widget = singleField.element;\n          translatedEntry.widgetProps = {\n            count: singleField.numberOfStars\n          };\n          // translatedEntry.widgetProps = {style:{width:'100%'}};\n          break;\n        case 'RadioButtons':\n          translatedEntry.widget = 'radio-group';\n          translatedEntry.forwardRef = true;\n          translatedEntry.options = [];\n          singleField.options.forEach(singleOption => {\n            translatedEntry.options.push({\n              label: singleOption.label,\n              value: singleOption.id\n            });\n          });\n          break;\n        case 'Checkboxes':\n          translatedEntry.widget = 'checkbox-group';\n          translatedEntry.options = [];\n          singleField.options.forEach(singleOption => {\n            translatedEntry.options.push({\n              label: singleOption.value,\n              value: singleOption.id\n            });\n          });\n          break;\n        //ble added\n        case 'WIFY_BLE_COMPONENT':\n          translatedEntry.lambdaARN = singleField.lambdaArn;\n          translatedEntry.initalText = singleField.initialText;\n          translatedEntry.cust_component = singleField.element;\n          translatedEntry.cust_component_value = '';\n          break;\n        case 'TextInput':\n          // nothing to be done\n          break;\n        default:\n          translatedEntry.cust_component = singleField.element;\n          translatedEntry.cust_component_value = '';\n          break;\n      }\n      translatedFields.push(translatedEntry);\n      // key_creator++;\n    });\n    return JSON.stringify({\n      originalFields,\n      translatedFields\n    });\n  } catch (e) {\n    console.log('Error in translation', e);\n    return json; // error in the above string (in this case, yes)!\n  }\n};\nconst extractLabelFrmSingleField = singleField => {\n  if ((singleField === null || singleField === void 0 ? void 0 : singleField.blocks) == undefined) {\n    return '';\n  }\n  var extractedLabel = '';\n  var isFirstTime = true;\n  singleField.blocks.forEach(singleTextBlock => {\n    // console.log(extractedLabel\n    //     + (isFirstTime ? \"\":\"\\n\")\n    //     + singleTextBlock.text);\n    extractedLabel = extractedLabel + (isFirstTime ? '' : '\\n') + singleTextBlock.text;\n    isFirstTime = false;\n  });\n  // console.log(\"Returning \",extractedLabel)\n  return extractedLabel;\n};\nexport default translateFormBuilderJsonToAntdBuilder;", "map": {"version": 3, "names": ["Rate", "unset", "React", "CKEditorWidget", "SiBluetooth", "TimePicker", "createAntdFormMeta", "json", "parsedAntdFormMeta", "JSON", "parse", "<PERSON><PERSON>ields", "finalAntdFormMeta", "for<PERSON>ach", "field", "cust_component", "undefined", "getReactComponentFr", "cust_widget", "originalCustWidget", "widget", "getReactWidgetFr", "push", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cust_component_value", "render", "rules", "pattern", "RegExp", "message", "min", "max", "className", "label", "forwardRef", "translateFormBuilderJsonToAntdBuilder", "original<PERSON>ields", "singleField", "translatedEntry", "key", "id", "colSpan", "Number", "required", "extractLabelFrmSingleField", "element", "options", "widgetProps", "mode", "singleOption", "value", "style", "width", "format", "use12Hours", "placeholder", "type", "count", "numberOfStars", "lambdaARN", "lambdaArn", "initalText", "initialText", "stringify", "e", "console", "log", "blocks", "extractedLabel", "isFirstTime", "singleTextBlock", "text"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/FieldCreator/mapping_form_builder.js"], "sourcesContent": ["import { Rate } from 'antd';\r\nimport { unset } from 'lodash';\r\nimport React from 'react';\r\nimport CKEditorWidget from '../CKEditorWidget';\r\nimport { SiBluetooth } from 'react-icons/si';\r\nimport { TimePicker } from \"../form-builder/components/FormBuilder/FormInputs\"\r\n\r\n/*Caller must do try catch */\r\nexport const createAntdFormMeta = (json) => {\r\n    var parsedAntdFormMeta = JSON.parse(json).translatedFields;\r\n    var finalAntdFormMeta = [];\r\n    parsedAntdFormMeta.forEach((field) => {\r\n        if (field.cust_component != undefined) {\r\n            field = getReactComponentFr(field);\r\n        }\r\n        if (field.cust_widget != undefined) {\r\n            const originalCustWidget = field.cust_widget; // Preserve the original value\r\n            field.widget = getReactWidgetFr(field.cust_widget);\r\n            // Keep cust_widget for convertDateFieldsToMoments to detect TimePicker fields\r\n            field.cust_widget = originalCustWidget;\r\n        }\r\n        finalAntdFormMeta.push(field);\r\n    });\r\n    return finalAntdFormMeta;\r\n};\r\n\r\nexport const getReactWidgetFr = (cust_widget) => {\r\n    switch (cust_widget) {\r\n        case 'Rating':\r\n            return Rate;\r\n            break;\r\n        case 'TimePicker':\r\n            return TimePicker;\r\n\r\n        default:\r\n            return <h5>Unknown component - {cust_widget}</h5>;\r\n    }\r\n};\r\n\r\nexport const getReactComponentFr = (field) => {\r\n    let { cust_component, cust_component_value } = field;\r\n\r\n    switch (cust_component) {\r\n        case 'legend':\r\n            field.render = () => {\r\n                return (\r\n                    <fieldset>\r\n                        <legend>\r\n                            <b>{cust_component_value}</b>\r\n                        </legend>\r\n                    </fieldset>\r\n                );\r\n            };\r\n            break;\r\n        case 'linebreak':\r\n            field.render = () => {\r\n                return (\r\n                    <fieldset>\r\n                        <legend>{cust_component_value}</legend>\r\n                    </fieldset>\r\n                );\r\n            };\r\n            break;\r\n        case 'Mobile':\r\n            field.rules = [\r\n                {\r\n                    pattern: new RegExp('^[0-9]*$'),\r\n                    message: 'Incorrect number',\r\n                },\r\n                { min: 10 },\r\n                { max: 10 },\r\n            ];\r\n            break;\r\n        case 'Files':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <i className=\"icon icon-upload gx-mr-2\"></i>\r\n                        This will be a drop zone for {field.label}\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n        case 'WIFY_MIC':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <i className=\"icon icon-upload gx-mr-2\"></i>\r\n                        This will be a Mic recorder for {field.label}\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n        case 'WIFY_CAMERA':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <i className=\"icon icon-upload gx-mr-2\"></i>\r\n                        This will be a camera input for {field.label}\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n        case 'WIFY_BLE_COMPONENT':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <div className=\"gx-d-flex gx-justify-content-start gx-align-items-centre\">\r\n                            <SiBluetooth className=\"gx-mr-2 gx-fs-18 gx-text-blue\" />\r\n                            <div className=\"gx-mt-auto\">\r\n                                This will be a section for {field.label}\r\n                            </div>\r\n                        </div>\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n        case 'WIFY_BARCODE_SCANNER':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <i className=\"icon icon-upload gx-mr-2\"></i>\r\n                        This will be a Barcode Scanner for {field.label}\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n        case 'WIFY_RICH_TEXT_INPUT':\r\n            field.widget = CKEditorWidget;\r\n            field.forwardRef = true;\r\n            // field.label = <h3>{field.label}</h3>\r\n            break;\r\n        case 'Button':\r\n            field.render = () => {\r\n                return (\r\n                    <h5 className=\"gx-border gx-border-blue gx-border-dashed gx-p-3\">\r\n                        <i className=\"icon icon-upload gx-mr-2\"></i>\r\n                        This will be a Button for {field.label}\r\n                    </h5>\r\n                );\r\n            };\r\n            break;\r\n\r\n        default:\r\n            field.render = () => {\r\n                return <h5>Unknown component - {cust_component}</h5>;\r\n            };\r\n            break;\r\n    }\r\n    return field;\r\n};\r\n\r\n// translatedEntry.rules = [\r\n//     {\r\n//         required: true,\r\n//         pattern: new RegExp(\"^[0-9]*$\"),\r\n//         message: 'Incorrect number',\r\n//     },\r\n//     { min : 10 },\r\n//     { max : 10 },\r\n// ]\r\n\r\nconst translateFormBuilderJsonToAntdBuilder = (json) => {\r\n    try {\r\n        var translatedFields = [];\r\n        var originalFields = JSON.parse(json);\r\n        // var key_creator = 1;\r\n        originalFields.forEach((singleField) => {\r\n            var translatedEntry = {};\r\n            translatedEntry.key = singleField.id;\r\n            if (singleField.colSpan && singleField.colSpan != '') {\r\n                translatedEntry.colSpan = Number(singleField.colSpan); // common fields\r\n            }\r\n            translatedEntry.required = singleField.required; // common fields\r\n            translatedEntry.label = extractLabelFrmSingleField(\r\n                singleField.label\r\n            );\r\n            switch (singleField.element) {\r\n                case 'Header':\r\n                    // translatedEntry.\r\n                    translatedEntry.cust_component = 'legend';\r\n                    translatedEntry.cust_component_value =\r\n                        extractLabelFrmSingleField(singleField.label);\r\n                    unset(translatedEntry, 'label');\r\n                    break;\r\n\r\n                case 'Tags':\r\n                case 'Dropdown':\r\n                    // translatedEntry.\r\n                    translatedEntry.widget = 'select';\r\n                    translatedEntry.options = [];\r\n                    if (singleField.element == 'Tags') {\r\n                        translatedEntry.widgetProps = { mode: 'multiple' };\r\n                    }\r\n                    singleField.options.forEach((singleOption) => {\r\n                        if (singleField.element == 'Tags') {\r\n                            translatedEntry.options.push({\r\n                                label: singleOption.label,\r\n                                value: singleOption.value,\r\n                            });\r\n                        } else {\r\n                            translatedEntry.options.push({\r\n                                label: singleOption.value,\r\n                                value: singleOption.id,\r\n                            });\r\n                        }\r\n                    });\r\n                    break;\r\n\r\n                case 'LineBreak':\r\n                    // translatedEntry.\r\n                    translatedEntry.cust_component = 'linebreak';\r\n                    translatedEntry.cust_component_value = '';\r\n                    unset(translatedEntry, 'label');\r\n                    // for loop to get options\r\n                    break;\r\n\r\n                case 'Date':\r\n                    translatedEntry.widget = 'date-picker';\r\n                    translatedEntry.widgetProps = { style: { width: '100%' } };\r\n                    break;\r\n                case 'TimePicker':\r\n                    translatedEntry.widget = TimePicker;\r\n                    translatedEntry.cust_widget = \"TimePicker\"\r\n                    translatedEntry.widgetProps = {\r\n                        format: 'hh:mm a',\r\n                        use12Hours: true,\r\n                        placeholder: 'Select time',\r\n                        style: { width: '100%' },\r\n                    };\r\n                    break;\r\n                case 'TextArea':\r\n                    translatedEntry.widget = 'textarea';\r\n                    // translatedEntry.widgetProps = {style:{width:'100%'}};\r\n                    break;\r\n                case 'NumberInput':\r\n                    translatedEntry.widget = 'number';\r\n                    // translatedEntry.widgetProps = {style:{width:'100%'}};\r\n                    break;\r\n                case 'Email':\r\n                    translatedEntry.rules = [\r\n                        {\r\n                            type: 'email',\r\n                            max: 100,\r\n                        },\r\n                    ];\r\n                    break;\r\n                case 'Rating':\r\n                    translatedEntry.cust_widget = singleField.element;\r\n                    translatedEntry.widgetProps = {\r\n                        count: singleField.numberOfStars,\r\n                    };\r\n                    // translatedEntry.widgetProps = {style:{width:'100%'}};\r\n                    break;\r\n                case 'RadioButtons':\r\n                    translatedEntry.widget = 'radio-group';\r\n                    translatedEntry.forwardRef = true;\r\n                    translatedEntry.options = [];\r\n                    singleField.options.forEach((singleOption) => {\r\n                        translatedEntry.options.push({\r\n                            label: singleOption.label,\r\n                            value: singleOption.id,\r\n                        });\r\n                    });\r\n                    break;\r\n                case 'Checkboxes':\r\n                    translatedEntry.widget = 'checkbox-group';\r\n                    translatedEntry.options = [];\r\n                    singleField.options.forEach((singleOption) => {\r\n                        translatedEntry.options.push({\r\n                            label: singleOption.value,\r\n                            value: singleOption.id,\r\n                        });\r\n                    });\r\n                    break;\r\n                //ble added\r\n                case 'WIFY_BLE_COMPONENT':\r\n                    translatedEntry.lambdaARN = singleField.lambdaArn;\r\n                    translatedEntry.initalText = singleField.initialText;\r\n                    translatedEntry.cust_component = singleField.element;\r\n                    translatedEntry.cust_component_value = '';\r\n                    break;\r\n\r\n                case 'TextInput':\r\n                    // nothing to be done\r\n                    break;\r\n\r\n                default:\r\n                    translatedEntry.cust_component = singleField.element;\r\n                    translatedEntry.cust_component_value = '';\r\n                    break;\r\n            }\r\n            translatedFields.push(translatedEntry);\r\n            // key_creator++;\r\n        });\r\n        return JSON.stringify({ originalFields, translatedFields });\r\n    } catch (e) {\r\n        console.log('Error in translation', e);\r\n        return json; // error in the above string (in this case, yes)!\r\n    }\r\n};\r\n\r\nconst extractLabelFrmSingleField = (singleField) => {\r\n    if (singleField?.blocks == undefined) {\r\n        return '';\r\n    }\r\n    var extractedLabel = '';\r\n    var isFirstTime = true;\r\n    singleField.blocks.forEach((singleTextBlock) => {\r\n        // console.log(extractedLabel\r\n        //     + (isFirstTime ? \"\":\"\\n\")\r\n        //     + singleTextBlock.text);\r\n        extractedLabel =\r\n            extractedLabel + (isFirstTime ? '' : '\\n') + singleTextBlock.text;\r\n        isFirstTime = false;\r\n    });\r\n    // console.log(\"Returning \",extractedLabel)\r\n    return extractedLabel;\r\n};\r\n\r\nexport default translateFormBuilderJsonToAntdBuilder;\r\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,QAAQ;AAC9B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,mDAAmD;;AAE9E;AACA,OAAO,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;EACxC,IAAIC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAACI,gBAAgB;EAC1D,IAAIC,iBAAiB,GAAG,EAAE;EAC1BJ,kBAAkB,CAACK,OAAO,CAAEC,KAAK,IAAK;IAClC,IAAIA,KAAK,CAACC,cAAc,IAAIC,SAAS,EAAE;MACnCF,KAAK,GAAGG,mBAAmB,CAACH,KAAK,CAAC;IACtC;IACA,IAAIA,KAAK,CAACI,WAAW,IAAIF,SAAS,EAAE;MAChC,MAAMG,kBAAkB,GAAGL,KAAK,CAACI,WAAW,CAAC,CAAC;MAC9CJ,KAAK,CAACM,MAAM,GAAGC,gBAAgB,CAACP,KAAK,CAACI,WAAW,CAAC;MAClD;MACAJ,KAAK,CAACI,WAAW,GAAGC,kBAAkB;IAC1C;IACAP,iBAAiB,CAACU,IAAI,CAACR,KAAK,CAAC;EACjC,CAAC,CAAC;EACF,OAAOF,iBAAiB;AAC5B,CAAC;AAED,OAAO,MAAMS,gBAAgB,GAAIH,WAAW,IAAK;EAC7C,QAAQA,WAAW;IACf,KAAK,QAAQ;MACT,OAAOlB,IAAI;MACX;IACJ,KAAK,YAAY;MACb,OAAOK,UAAU;IAErB;MACI,oBAAOH,KAAA,CAAAqB,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAI,sBAAoB,EAACX,WAAgB,CAAC;EACzD;AACJ,CAAC;AAED,OAAO,MAAMD,mBAAmB,GAAIH,KAAK,IAAK;EAC1C,IAAI;IAAEC,cAAc;IAAEe;EAAqB,CAAC,GAAGhB,KAAK;EAEpD,QAAQC,cAAc;IAClB,KAAK,QAAQ;MACTD,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACI3B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACI3B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAIC,oBAAwB,CACxB,CACF,CAAC;MAEnB,CAAC;MACD;IACJ,KAAK,WAAW;MACZhB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACI3B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAASC,oBAA6B,CAChC,CAAC;MAEnB,CAAC;MACD;IACJ,KAAK,QAAQ;MACThB,KAAK,CAACkB,KAAK,GAAG,CACV;QACIC,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU,CAAC;QAC/BC,OAAO,EAAE;MACb,CAAC,EACD;QAAEC,GAAG,EAAE;MAAG,CAAC,EACX;QAAEC,GAAG,EAAE;MAAG,CAAC,CACd;MACD;IACJ,KAAK,OAAO;MACRvB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAGe,SAAS,EAAC,0BAA0B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAI,CAAC,iCACf,EAACf,KAAK,CAACyB,KACpC,CAAC;MAEb,CAAC;MACD;IACJ,KAAK,UAAU;MACXzB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAGe,SAAS,EAAC,0BAA0B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAI,CAAC,oCACZ,EAACf,KAAK,CAACyB,KACvC,CAAC;MAEb,CAAC;MACD;IACJ,KAAK,aAAa;MACdzB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAGe,SAAS,EAAC,0BAA0B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAI,CAAC,oCACZ,EAACf,KAAK,CAACyB,KACvC,CAAC;MAEb,CAAC;MACD;IACJ,KAAK,oBAAoB;MACrBzB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAKe,SAAS,EAAC,0DAA0D;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACrE3B,KAAA,CAAAqB,aAAA,CAACnB,WAAW;UAACkC,SAAS,EAAC,+BAA+B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,eACzD3B,KAAA,CAAAqB,aAAA;UAAKe,SAAS,EAAC,YAAY;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAC,6BACG,EAACf,KAAK,CAACyB,KACjC,CACJ,CACL,CAAC;MAEb,CAAC;MACD;IACJ,KAAK,sBAAsB;MACvBzB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAGe,SAAS,EAAC,0BAA0B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAI,CAAC,uCACT,EAACf,KAAK,CAACyB,KAC1C,CAAC;MAEb,CAAC;MACD;IACJ,KAAK,sBAAsB;MACvBzB,KAAK,CAACM,MAAM,GAAGjB,cAAc;MAC7BW,KAAK,CAAC0B,UAAU,GAAG,IAAI;MACvB;MACA;IACJ,KAAK,QAAQ;MACT1B,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBACI7B,KAAA,CAAAqB,aAAA;UAAIe,SAAS,EAAC,kDAAkD;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAC5D3B,KAAA,CAAAqB,aAAA;UAAGe,SAAS,EAAC,0BAA0B;UAAAd,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAI,CAAC,8BAClB,EAACf,KAAK,CAACyB,KACjC,CAAC;MAEb,CAAC;MACD;IAEJ;MACIzB,KAAK,CAACiB,MAAM,GAAG,MAAM;QACjB,oBAAO7B,KAAA,CAAAqB,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAI,sBAAoB,EAACd,cAAmB,CAAC;MACxD,CAAC;MACD;EACR;EACA,OAAOD,KAAK;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM2B,qCAAqC,GAAIlC,IAAI,IAAK;EACpD,IAAI;IACA,IAAII,gBAAgB,GAAG,EAAE;IACzB,IAAI+B,cAAc,GAAGjC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IACrC;IACAmC,cAAc,CAAC7B,OAAO,CAAE8B,WAAW,IAAK;MACpC,IAAIC,eAAe,GAAG,CAAC,CAAC;MACxBA,eAAe,CAACC,GAAG,GAAGF,WAAW,CAACG,EAAE;MACpC,IAAIH,WAAW,CAACI,OAAO,IAAIJ,WAAW,CAACI,OAAO,IAAI,EAAE,EAAE;QAClDH,eAAe,CAACG,OAAO,GAAGC,MAAM,CAACL,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC;MAC3D;MACAH,eAAe,CAACK,QAAQ,GAAGN,WAAW,CAACM,QAAQ,CAAC,CAAC;MACjDL,eAAe,CAACL,KAAK,GAAGW,0BAA0B,CAC9CP,WAAW,CAACJ,KAChB,CAAC;MACD,QAAQI,WAAW,CAACQ,OAAO;QACvB,KAAK,QAAQ;UACT;UACAP,eAAe,CAAC7B,cAAc,GAAG,QAAQ;UACzC6B,eAAe,CAACd,oBAAoB,GAChCoB,0BAA0B,CAACP,WAAW,CAACJ,KAAK,CAAC;UACjDtC,KAAK,CAAC2C,eAAe,EAAE,OAAO,CAAC;UAC/B;QAEJ,KAAK,MAAM;QACX,KAAK,UAAU;UACX;UACAA,eAAe,CAACxB,MAAM,GAAG,QAAQ;UACjCwB,eAAe,CAACQ,OAAO,GAAG,EAAE;UAC5B,IAAIT,WAAW,CAACQ,OAAO,IAAI,MAAM,EAAE;YAC/BP,eAAe,CAACS,WAAW,GAAG;cAAEC,IAAI,EAAE;YAAW,CAAC;UACtD;UACAX,WAAW,CAACS,OAAO,CAACvC,OAAO,CAAE0C,YAAY,IAAK;YAC1C,IAAIZ,WAAW,CAACQ,OAAO,IAAI,MAAM,EAAE;cAC/BP,eAAe,CAACQ,OAAO,CAAC9B,IAAI,CAAC;gBACzBiB,KAAK,EAAEgB,YAAY,CAAChB,KAAK;gBACzBiB,KAAK,EAAED,YAAY,CAACC;cACxB,CAAC,CAAC;YACN,CAAC,MAAM;cACHZ,eAAe,CAACQ,OAAO,CAAC9B,IAAI,CAAC;gBACzBiB,KAAK,EAAEgB,YAAY,CAACC,KAAK;gBACzBA,KAAK,EAAED,YAAY,CAACT;cACxB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;UACF;QAEJ,KAAK,WAAW;UACZ;UACAF,eAAe,CAAC7B,cAAc,GAAG,WAAW;UAC5C6B,eAAe,CAACd,oBAAoB,GAAG,EAAE;UACzC7B,KAAK,CAAC2C,eAAe,EAAE,OAAO,CAAC;UAC/B;UACA;QAEJ,KAAK,MAAM;UACPA,eAAe,CAACxB,MAAM,GAAG,aAAa;UACtCwB,eAAe,CAACS,WAAW,GAAG;YAAEI,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAAE,CAAC;UAC1D;QACJ,KAAK,YAAY;UACbd,eAAe,CAACxB,MAAM,GAAGf,UAAU;UACnCuC,eAAe,CAAC1B,WAAW,GAAG,YAAY;UAC1C0B,eAAe,CAACS,WAAW,GAAG;YAC1BM,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,IAAI;YAChBC,WAAW,EAAE,aAAa;YAC1BJ,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAC3B,CAAC;UACD;QACJ,KAAK,UAAU;UACXd,eAAe,CAACxB,MAAM,GAAG,UAAU;UACnC;UACA;QACJ,KAAK,aAAa;UACdwB,eAAe,CAACxB,MAAM,GAAG,QAAQ;UACjC;UACA;QACJ,KAAK,OAAO;UACRwB,eAAe,CAACZ,KAAK,GAAG,CACpB;YACI8B,IAAI,EAAE,OAAO;YACbzB,GAAG,EAAE;UACT,CAAC,CACJ;UACD;QACJ,KAAK,QAAQ;UACTO,eAAe,CAAC1B,WAAW,GAAGyB,WAAW,CAACQ,OAAO;UACjDP,eAAe,CAACS,WAAW,GAAG;YAC1BU,KAAK,EAAEpB,WAAW,CAACqB;UACvB,CAAC;UACD;UACA;QACJ,KAAK,cAAc;UACfpB,eAAe,CAACxB,MAAM,GAAG,aAAa;UACtCwB,eAAe,CAACJ,UAAU,GAAG,IAAI;UACjCI,eAAe,CAACQ,OAAO,GAAG,EAAE;UAC5BT,WAAW,CAACS,OAAO,CAACvC,OAAO,CAAE0C,YAAY,IAAK;YAC1CX,eAAe,CAACQ,OAAO,CAAC9B,IAAI,CAAC;cACzBiB,KAAK,EAAEgB,YAAY,CAAChB,KAAK;cACzBiB,KAAK,EAAED,YAAY,CAACT;YACxB,CAAC,CAAC;UACN,CAAC,CAAC;UACF;QACJ,KAAK,YAAY;UACbF,eAAe,CAACxB,MAAM,GAAG,gBAAgB;UACzCwB,eAAe,CAACQ,OAAO,GAAG,EAAE;UAC5BT,WAAW,CAACS,OAAO,CAACvC,OAAO,CAAE0C,YAAY,IAAK;YAC1CX,eAAe,CAACQ,OAAO,CAAC9B,IAAI,CAAC;cACzBiB,KAAK,EAAEgB,YAAY,CAACC,KAAK;cACzBA,KAAK,EAAED,YAAY,CAACT;YACxB,CAAC,CAAC;UACN,CAAC,CAAC;UACF;QACJ;QACA,KAAK,oBAAoB;UACrBF,eAAe,CAACqB,SAAS,GAAGtB,WAAW,CAACuB,SAAS;UACjDtB,eAAe,CAACuB,UAAU,GAAGxB,WAAW,CAACyB,WAAW;UACpDxB,eAAe,CAAC7B,cAAc,GAAG4B,WAAW,CAACQ,OAAO;UACpDP,eAAe,CAACd,oBAAoB,GAAG,EAAE;UACzC;QAEJ,KAAK,WAAW;UACZ;UACA;QAEJ;UACIc,eAAe,CAAC7B,cAAc,GAAG4B,WAAW,CAACQ,OAAO;UACpDP,eAAe,CAACd,oBAAoB,GAAG,EAAE;UACzC;MACR;MACAnB,gBAAgB,CAACW,IAAI,CAACsB,eAAe,CAAC;MACtC;IACJ,CAAC,CAAC;IACF,OAAOnC,IAAI,CAAC4D,SAAS,CAAC;MAAE3B,cAAc;MAAE/B;IAAiB,CAAC,CAAC;EAC/D,CAAC,CAAC,OAAO2D,CAAC,EAAE;IACRC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,CAAC,CAAC;IACtC,OAAO/D,IAAI,CAAC,CAAC;EACjB;AACJ,CAAC;AAED,MAAM2C,0BAA0B,GAAIP,WAAW,IAAK;EAChD,IAAI,CAAAA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8B,MAAM,KAAIzD,SAAS,EAAE;IAClC,OAAO,EAAE;EACb;EACA,IAAI0D,cAAc,GAAG,EAAE;EACvB,IAAIC,WAAW,GAAG,IAAI;EACtBhC,WAAW,CAAC8B,MAAM,CAAC5D,OAAO,CAAE+D,eAAe,IAAK;IAC5C;IACA;IACA;IACAF,cAAc,GACVA,cAAc,IAAIC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,GAAGC,eAAe,CAACC,IAAI;IACrEF,WAAW,GAAG,KAAK;EACvB,CAAC,CAAC;EACF;EACA,OAAOD,cAAc;AACzB,CAAC;AAED,eAAejC,qCAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}