{"ast": null, "code": "import uuid from 'uuid/v4';\nimport htmlToDraft from 'html-to-draftjs';\nimport { ContentState, EditorState, convertToRaw } from 'draft-js';\n\n// convert html to draftjs-editorState and then return raw JS\n// editorState is stored as raw JS object in the Redux store\nconst convertHtmlToRawJs = html => {\n  const contentBlock = htmlToDraft(html);\n  const contentState = ContentState.createFromBlockArray(contentBlock.contentBlocks);\n  const editorState = EditorState.createWithContent(contentState);\n  return convertToRaw(editorState.getCurrentContent());\n};\nconst html = '<div>Placeholder Label</div>';\nexport default item => {\n  switch (item) {\n    case 'Header':\n    case 'Paragraph':\n    case 'Label':\n      return {\n        label: convertHtmlToRawJs(html)\n      };\n    case 'WIFY_CAMERA':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'WIFY_BLE_COMPONENT':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'WIFY_BARCODE_SCANNER':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'WIFY_RICH_TEXT_INPUT':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'WIFY_MIC':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'Files':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'Mobile':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'Date':\n      // console.log(\"Collected element date\");\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'TimePicker':\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false\n      };\n    case 'Checkboxes':\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false,\n        options: [{\n          id: uuid(),\n          value: 'Option1',\n          checked: false\n        }, {\n          id: uuid(),\n          value: 'Option2',\n          checked: false\n        }]\n      };\n    case 'Dropdown':\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false,\n        options: [{\n          id: uuid(),\n          value: 'Option1'\n        }, {\n          id: uuid(),\n          value: 'Option2'\n        }]\n      };\n    case 'HyperLink':\n      return {\n        label: convertHtmlToRawJs(html),\n        required: false,\n        value: ''\n      };\n    case 'LineBreak':\n      return {};\n    case 'NumberInput':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        value: 0\n      };\n    case 'RadioButtons':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        options: [{\n          id: uuid(),\n          label: 'Label1',\n          value: 'Value1',\n          checked: false\n        }, {\n          id: uuid(),\n          label: 'Label2',\n          value: 'Value2',\n          checked: false\n        }]\n      };\n    case 'Tags':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        options: [{\n          id: uuid(),\n          label: 'Label1',\n          value: 'Value1'\n        }, {\n          id: uuid(),\n          label: 'Label2',\n          value: 'Value2'\n        }]\n      };\n    case 'Range':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        value: 0,\n        min: 0,\n        max: 5,\n        step: 1\n      };\n    case 'Rating':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        value: 0,\n        numberOfStars: 5\n      };\n    case 'Button':\n      return {\n        // required: false,\n        label: convertHtmlToRawJs(html),\n        value: ''\n      };\n    case 'TextInput':\n    case 'TextArea':\n    case 'Email':\n      return {\n        required: false,\n        label: convertHtmlToRawJs(html),\n        value: ''\n      };\n    default:\n      return;\n  }\n};", "map": {"version": 3, "names": ["uuid", "htmlToDraft", "ContentState", "EditorState", "convertToRaw", "convertHtmlToRawJs", "html", "contentBlock", "contentState", "createFromBlockArray", "contentBlocks", "editorState", "createWithContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item", "label", "required", "options", "id", "value", "checked", "min", "max", "step", "numberOfStars"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/form-builder/actions/addPropsToItem.js"], "sourcesContent": ["import uuid from 'uuid/v4';\r\nimport htmlToDraft from 'html-to-draftjs';\r\nimport { ContentState, EditorState, convertToRaw } from 'draft-js';\r\n\r\n// convert html to draftjs-editorState and then return raw JS\r\n// editorState is stored as raw JS object in the Redux store\r\nconst convertHtmlToRawJs = (html) => {\r\n    const contentBlock = htmlToDraft(html);\r\n    const contentState = ContentState.createFromBlockArray(\r\n        contentBlock.contentBlocks\r\n    );\r\n    const editorState = EditorState.createWithContent(contentState);\r\n    return convertToRaw(editorState.getCurrentContent());\r\n};\r\n\r\nconst html = '<div>Placeholder Label</div>';\r\n\r\nexport default (item) => {\r\n    switch (item) {\r\n        case 'Header':\r\n        case 'Paragraph':\r\n        case 'Label':\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n            };\r\n        case 'WIFY_CAMERA':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'WIFY_BLE_COMPONENT':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'WIFY_BARCODE_SCANNER':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'WIFY_RICH_TEXT_INPUT':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'WIFY_MIC':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'Files':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'Mobile':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'Date':\r\n            // console.log(\"Collected element date\");\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n        case 'TimePicker':\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n            };\r\n\r\n        case 'Checkboxes':\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n                options: [\r\n                    {\r\n                        id: uuid(),\r\n                        value: 'Option1',\r\n                        checked: false,\r\n                    },\r\n                    {\r\n                        id: uuid(),\r\n                        value: 'Option2',\r\n                        checked: false,\r\n                    },\r\n                ],\r\n            };\r\n\r\n        case 'Dropdown':\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n                options: [\r\n                    {\r\n                        id: uuid(),\r\n                        value: 'Option1',\r\n                    },\r\n                    {\r\n                        id: uuid(),\r\n                        value: 'Option2',\r\n                    },\r\n                ],\r\n            };\r\n\r\n        case 'HyperLink':\r\n            return {\r\n                label: convertHtmlToRawJs(html),\r\n                required: false,\r\n                value: '',\r\n            };\r\n\r\n        case 'LineBreak':\r\n            return {};\r\n\r\n        case 'NumberInput':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                value: 0,\r\n            };\r\n\r\n        case 'RadioButtons':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                options: [\r\n                    {\r\n                        id: uuid(),\r\n                        label: 'Label1',\r\n                        value: 'Value1',\r\n                        checked: false,\r\n                    },\r\n                    {\r\n                        id: uuid(),\r\n                        label: 'Label2',\r\n                        value: 'Value2',\r\n                        checked: false,\r\n                    },\r\n                ],\r\n            };\r\n        case 'Tags':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                options: [\r\n                    {\r\n                        id: uuid(),\r\n                        label: 'Label1',\r\n                        value: 'Value1',\r\n                    },\r\n                    {\r\n                        id: uuid(),\r\n                        label: 'Label2',\r\n                        value: 'Value2',\r\n                    },\r\n                ],\r\n            };\r\n\r\n        case 'Range':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                value: 0,\r\n                min: 0,\r\n                max: 5,\r\n                step: 1,\r\n            };\r\n\r\n        case 'Rating':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                value: 0,\r\n                numberOfStars: 5,\r\n            };\r\n        case 'Button':\r\n            return {\r\n                // required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                value: '',\r\n            };\r\n        case 'TextInput':\r\n        case 'TextArea':\r\n        case 'Email':\r\n            return {\r\n                required: false,\r\n                label: convertHtmlToRawJs(html),\r\n                value: '',\r\n            };\r\n        default:\r\n            return;\r\n    }\r\n};\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,SAASC,YAAY,EAAEC,WAAW,EAAEC,YAAY,QAAQ,UAAU;;AAElE;AACA;AACA,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;EACjC,MAAMC,YAAY,GAAGN,WAAW,CAACK,IAAI,CAAC;EACtC,MAAME,YAAY,GAAGN,YAAY,CAACO,oBAAoB,CAClDF,YAAY,CAACG,aACjB,CAAC;EACD,MAAMC,WAAW,GAAGR,WAAW,CAACS,iBAAiB,CAACJ,YAAY,CAAC;EAC/D,OAAOJ,YAAY,CAACO,WAAW,CAACE,iBAAiB,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,MAAMP,IAAI,GAAG,8BAA8B;AAE3C,eAAgBQ,IAAI,IAAK;EACrB,QAAQA,IAAI;IACR,KAAK,QAAQ;IACb,KAAK,WAAW;IAChB,KAAK,OAAO;MACR,OAAO;QACHC,KAAK,EAAEV,kBAAkB,CAACC,IAAI;MAClC,CAAC;IACL,KAAK,aAAa;MACd;MACA,OAAO;QACHS,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,oBAAoB;MACrB;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,sBAAsB;MACvB;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,sBAAsB;MACvB;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,UAAU;MACX;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,OAAO;MACR;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,QAAQ;MACT;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,MAAM;MACP;MACA,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IACL,KAAK,YAAY;MACb,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE;MACd,CAAC;IAEL,KAAK,YAAY;MACb,OAAO;QACHD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,CACL;UACIC,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVmB,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE;QACb,CAAC,EACD;UACIF,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVmB,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE;QACb,CAAC;MAET,CAAC;IAEL,KAAK,UAAU;MACX,OAAO;QACHL,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,CACL;UACIC,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVmB,KAAK,EAAE;QACX,CAAC,EACD;UACID,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVmB,KAAK,EAAE;QACX,CAAC;MAET,CAAC;IAEL,KAAK,WAAW;MACZ,OAAO;QACHJ,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BU,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE;MACX,CAAC;IAEL,KAAK,WAAW;MACZ,OAAO,CAAC,CAAC;IAEb,KAAK,aAAa;MACd,OAAO;QACHH,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/Ba,KAAK,EAAE;MACX,CAAC;IAEL,KAAK,cAAc;MACf,OAAO;QACHH,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BW,OAAO,EAAE,CACL;UACIC,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVe,KAAK,EAAE,QAAQ;UACfI,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACb,CAAC,EACD;UACIF,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVe,KAAK,EAAE,QAAQ;UACfI,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACb,CAAC;MAET,CAAC;IACL,KAAK,MAAM;MACP,OAAO;QACHJ,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/BW,OAAO,EAAE,CACL;UACIC,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVe,KAAK,EAAE,QAAQ;UACfI,KAAK,EAAE;QACX,CAAC,EACD;UACID,EAAE,EAAElB,IAAI,CAAC,CAAC;UACVe,KAAK,EAAE,QAAQ;UACfI,KAAK,EAAE;QACX,CAAC;MAET,CAAC;IAEL,KAAK,OAAO;MACR,OAAO;QACHH,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/Ba,KAAK,EAAE,CAAC;QACRE,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE;MACV,CAAC;IAEL,KAAK,QAAQ;MACT,OAAO;QACHP,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/Ba,KAAK,EAAE,CAAC;QACRK,aAAa,EAAE;MACnB,CAAC;IACL,KAAK,QAAQ;MACT,OAAO;QACH;QACAT,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/Ba,KAAK,EAAE;MACX,CAAC;IACL,KAAK,WAAW;IAChB,KAAK,UAAU;IACf,KAAK,OAAO;MACR,OAAO;QACHH,QAAQ,EAAE,KAAK;QACfD,KAAK,EAAEV,kBAAkB,CAACC,IAAI,CAAC;QAC/Ba,KAAK,EAAE;MACX,CAAC;IACL;MACI;EACR;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}