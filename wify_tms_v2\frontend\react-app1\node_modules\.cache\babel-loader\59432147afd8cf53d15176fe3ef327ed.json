{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\wify-utils\\\\BulkUploader.js\";\nimport { Alert, Button, Col, Input, List, message, Modal, Progress, Row, Spin, Table, Tag, Typography } from 'antd';\nimport FormBuilder from 'antd-form-builder';\nimport { OutTable, ExcelRenderer } from 'react-excel-renderer';\nimport React, { Component, useEffect, useState } from 'react';\nimport { convertDateFieldsToMoments, decodeAntdFormErrorsToString, ExcelDateToJSDate } from '../../util/helpers';\nimport moment from 'moment';\nimport { Form } from 'antd';\nimport { defaultStyles, FileIcon } from 'react-file-icon';\nimport http_utils from '../../util/http_utils';\nimport ReactExport from 'react-export-excel';\nimport { DownloadOutlined } from '@ant-design/icons';\nimport LocalStorageManager from '../../util/LocalStorageManager';\nimport ConfigHelpers from '../../util/ConfigHelpers';\nimport ruleValidator from './AntdRuleValidator';\nconst {\n  Paragraph\n} = Typography;\nconst ExcelFile = ReactExport.ExcelFile;\nconst ExcelSheet = ReactExport.ExcelFile.ExcelSheet;\nconst ExcelColumn = ReactExport.ExcelFile.ExcelColumn;\nconst pageSize = 10;\n// const defaultChunkSize = 1000;\nconst defaultMaxRows = 1000;\nconst dataProtoFrBulkUpload = [{\n  label: 'Name',\n  required: true,\n  key: 'user_name'\n}, {\n  key: 'user_number',\n  label: 'Mobile(+91)',\n  required: true,\n  rules: [{\n    pattern: new RegExp('^[0-9]*$'),\n    message: 'Incorrect number'\n  }, {\n    min: 10\n  }, {\n    max: 10\n  }]\n}, {\n  label: 'Gender',\n  required: true,\n  key: 'user_gender',\n  widget: 'select',\n  options: [{\n    label: 'Male',\n    value: 'male'\n  }, {\n    label: 'Female',\n    value: 'female'\n  }]\n}, {\n  label: 'Weight',\n  required: false,\n  key: 'user_weight',\n  widget: 'number'\n}, {\n  label: 'DOB',\n  required: false,\n  key: 'user_dob',\n  widget: 'date-picker'\n}];\nconst SingleRowFormFrBuilder = props => {\n  // const [form] = Form.useForm();\n  const {\n    meta,\n    initialValues,\n    onValidationError,\n    onReadyToUpload,\n    renderFullForm\n  } = props;\n  const [error, setError] = useState(undefined);\n  const [checked, setChecked] = useState(false);\n  // useEffect(() => {\n  //     if (!checked) {\n  //         // console.log('BulkAssign Checked row',initialValues)\n  //         setChecked(true);\n  //         setTimeout(async () => {\n  //             let formData = form.getFieldsValue();\n  //             form.validateFields()\n  //                 .then((values) => {\n  //                     setError('success');\n  //                     onReadyToUpload(formData);\n  //                 })\n  //                 .catch((err) => {\n  //                     // console.log('err',err)\n  //                     if (err.errorFields?.length > 0) {\n  //                         setError(decodeAntdFormErrorsToString(err));\n  //                         // setError(JSON.stringify(err.errors))\n  //                         onValidationError(err);\n  //                     } else {\n  //                         onReadyToUpload(formData);\n  //                     }\n  //                 });\n  //         }, 10);\n  //     }\n  // });\n  if (!checked) {\n    setChecked(true);\n    setTimeout(async () => {\n      const validate = await ruleValidator(meta.fields, initialValues, props.isBulkAssignComp);\n      if (validate.length > 0) {\n        var _validate$;\n        const errorString = validate === null || validate === void 0 ? void 0 : (_validate$ = validate[0]) === null || _validate$ === void 0 ? void 0 : _validate$.errors.join(',');\n        setError(errorString || 'Error');\n        onValidationError(validate);\n      } else {\n        setError('success');\n        onReadyToUpload(initialValues);\n      }\n    }, 10);\n  }\n  return !renderFullForm && error ? error == 'success' ? /*#__PURE__*/React.createElement(Tag, {\n    color: \"success\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 13\n    }\n  }, \"Ready\") :\n  /*#__PURE__*/\n  // <Tag color=\"error\">{error}</Tag>\n  React.createElement(\"span\", {\n    className: \"gx-text-red\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }\n  }, error) :\n  /*#__PURE__*/\n  // <>\n  //     <Spin></Spin>\n  //     <Form\n  //         layout=\"vertical\"\n  //         form={form}\n  //         className=\"gx-d-none\"\n  //         initialValues={initialValues}\n  //     >\n  //         <FormBuilder form={form} meta={meta} />\n  //     </Form>\n  // </>\n  React.createElement(React.Fragment, null);\n};\nclass BulkUploader extends Component {\n  initFileCheckVars(resetMajorErroricRowsKeys = true) {\n    this.readyToUploadRowKeys = {};\n    this.readyToUploadRows = [];\n    this.erroricRows = 0;\n    this.erroricRowsKeys = {};\n    if (resetMajorErroricRowsKeys) {\n      this.majorErroricRowsKeys = {};\n    }\n  }\n  constructor(props) {\n    super(props);\n    // console.log('Rxd props', this.props);\n    this.initState = {\n      cols: [],\n      rows: [],\n      bulkUploadInProgress: false,\n      bulkUploadProgress: 0,\n      clearFileInput: false,\n      uploadComplete: false,\n      errorInfile: false,\n      erroricRows: 0,\n      majorErrors: [],\n      readyToUploadRows: [],\n      readyToUploadRowKeys: [],\n      uploadResp: '',\n      fileCheckingInProg: false,\n      currentPage: 1,\n      fileName: '',\n      isExcelUploaded: false,\n      multipleFieldDataModals: {}\n    };\n    this.state = this.initState;\n    this.showSelectFieldOptionsModal = key => {\n      let multipleFieldDataModals = this.state.multipleFieldDataModals;\n      multipleFieldDataModals[key] = true;\n      this.setState({\n        multipleFieldDataModals\n      });\n    };\n    this.hideSelectFieldOptionsModal = key => {\n      let multipleFieldDataModals = this.state.multipleFieldDataModals;\n      multipleFieldDataModals[key] = false;\n      this.setState({\n        multipleFieldDataModals\n      });\n    };\n    this.initFileCheckVars();\n  }\n  getMeta() {\n    let finalFields = [];\n    let dataProto = this.getDataProto();\n    dataProto.map(singleFieldMeta => {\n      if (singleFieldMeta.label) {\n        singleFieldMeta.colSpan = 2;\n        singleFieldMeta.renderView = () => /*#__PURE__*/React.createElement(\"div\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 52\n          }\n        });\n        finalFields.push(singleFieldMeta);\n      }\n    });\n    const meta = {\n      columns: this.state.rows[0].length,\n      formItemLayout: null,\n      fields: finalFields\n    };\n    return meta;\n  }\n  getSingleColumFieldMeta(columnText) {\n    let returnMeta = {};\n    let dataProto = this.getDataProto();\n    dataProto.map(singleFieldMeta => {\n      if (singleFieldMeta.label == columnText) {\n        returnMeta = singleFieldMeta;\n      }\n    });\n    return returnMeta;\n  }\n\n  // Helper function to convert TimePicker values from Excel format\n  convertTimePickerValue(value) {\n    if (value != '' && value != null && value != undefined) {\n      if (typeof value === 'number' && value > 0 && value < 1) {\n        // Excel converts time to decimal (0.5 = 12:00 PM, 0.93 ≈ 10:20 PM)\n        const milliseconds = value * 24 * 60 * 60 * 1000;\n        return moment.utc(milliseconds).format('hh:mm A');\n      } else if (typeof value === 'string') {\n        // Handle string time formats with various spacing\n        const parsed = moment(value, ['h:mm A', 'hh:mm A', 'h:mm  A', 'hh:mm  A', 'H:mm', 'HH:mm', 'h:mmA', 'hh:mmA'], true);\n        if (parsed.isValid()) {\n          return parsed.format('hh:mm A');\n        }\n      }\n    }\n    return value;\n  }\n  getColumns(columsArray) {\n    // this will be from an excel\n    let firstRow = columsArray || this.state.rows[0];\n    let columns = [];\n    if (firstRow) {\n      columns.push({\n        title: 'Upload state',\n        dataIndex: this.getUploadStateFieldIndex(),\n        key: this.getUploadStateFieldIndex(),\n        render: (text, record) => {\n          return /*#__PURE__*/React.createElement(SingleRowFormFrBuilder, {\n            key: record.batch_data_row_key,\n            meta: this.getMeta(),\n            renderFullForm: this.props.renderFormsForRows,\n            initialValues: this.getFormDataForRow(record),\n            onValidationError: e => this.handleFailedRow(record.batch_data_row_key),\n            onReadyToUpload: formData => this.handleReadyToUpload(formData, record.batch_data_row_key),\n            isBulkAssignComp: this.props.isBulkAssignComp,\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 25\n            }\n          });\n        }\n      });\n      !this.props.renderFormsForRows && firstRow.map((singleCellValue, index) => {\n        let fieldMeta = this.getSingleColumFieldMeta(singleCellValue);\n        let singleColumnData = {\n          title: singleCellValue,\n          dataIndex: index,\n          key: index,\n          className: fieldMeta.required ? 'gx-bg-amber-light' : ''\n        };\n        if (fieldMeta.widget == 'date-picker') {\n          singleColumnData.render = (text, record) => {\n            // this is a date field\n            if (text != '') {\n              let value = ExcelDateToJSDate(text);\n              if (value) {\n                value = moment(value);\n                value = value.format('MMM-DD-YYYY');\n              } else {\n                value = moment(value);\n              }\n              return value;\n            }\n            return text;\n          };\n        } else if (fieldMeta.cust_widget === 'TimePicker') {\n          singleColumnData.render = (text, record) => {\n            return this.convertTimePickerValue(text);\n          };\n        }\n        columns.push(singleColumnData);\n      });\n    }\n    return columns;\n  }\n  getUploadStateFieldIndex() {\n    // return this.state?.rows[0]?.length;\n    return 0;\n  }\n  getData(rowsInState = undefined) {\n    let rows = rowsInState || this.state.rows;\n    let finalRows = [];\n    rows.map((singleRow, index) => {\n      // first is header\n      if (index > 0) {\n        singleRow.batch_data_row_key = index;\n        finalRows.push(singleRow);\n      }\n    });\n    // console.log('rows',rows)\n    return finalRows;\n  }\n  async handleFileChange(event) {\n    let fileObj = event.target.files[0];\n    try {\n      await message.loading('Loading excel...');\n      const excelData = await new Promise((resolve, reject) => {\n        ExcelRenderer(fileObj, (err, resp) => {\n          if (err) {\n            reject('Unable to read the excel');\n          } else {\n            resolve(resp);\n          }\n        });\n      });\n      const header = excelData.rows[0];\n      const CHUNK_SIZE = 100;\n      const totalRows = excelData.rows.length;\n      let startRow = 1;\n\n      // Process the rows in chunks\n      while (startRow < totalRows) {\n        const chunkRows = excelData.rows.slice(startRow, startRow + CHUNK_SIZE);\n        const chunkWithHeader = [header, ...chunkRows];\n        const processedRows = chunkWithHeader.filter(singleRow => {\n          if (singleRow.length > 0) {\n            singleRow = singleRow.filter(cellVal => cellVal && cellVal !== '');\n          }\n          return singleRow.length > 0;\n        });\n        this.doBasicValidationsAndRereshUI({\n          rows: processedRows,\n          cols: excelData.cols\n        }, fileObj.name, startRow);\n        startRow += CHUNK_SIZE;\n      }\n      this.setState({\n        isExcelUploaded: true\n      });\n      message.success('Excel loaded successfully.');\n    } catch (e) {\n      message.error(e || 'Unable to read the excel');\n      this.initFileCheckVars();\n      this.setState(this.initState);\n    }\n  }\n  doBasicValidationsAndRereshUI(resp, fileName, startRow) {\n    let majorErrors = [];\n    let dataProto = this.getDataProto();\n    let maxRows = this.props.maxRows;\n    let currentRowsLength = resp.rows.length - 1;\n    if (maxRows && currentRowsLength > maxRows) {\n      majorErrors.push(`Total rows in excel (${currentRowsLength}) exceeds maximum allowed ${maxRows} rows`);\n    }\n    let acceptedColumns = dataProto.map(fieldMeta => fieldMeta.label);\n    let requiredFields = dataProto.filter(fieldMeta => fieldMeta.required);\n    // console.log('Required fields',requiredFields);\n    let columns = resp.rows[0];\n    let missingReqFields = requiredFields.filter(fieldMeta => !columns.includes(fieldMeta.label));\n    if (missingReqFields.length > 0) {\n      majorErrors.push('Missing column - ' + missingReqFields.map(fieldMeta => fieldMeta.label).join(','));\n    }\n    // console.log('majorErrors',majorErrors);\n    let unRecognisedFields = columns.filter(columnTitle => !acceptedColumns.includes(columnTitle));\n    // console.log('unRecognisedFields',unRecognisedFields);\n    if (unRecognisedFields.length > 0) {\n      majorErrors.push(' Unrecognised column - ' + unRecognisedFields.join(','));\n    }\n    this.validateDateCols(resp);\n    // let erroricDateCols = this.validateDateCols(resp);\n    // if(erroricDateCols.length > 0){\n    //     majorErrors.push('Invalid date format used for - ' + erroricDateCols.join(',') + ' please use YYYY-MM-DD ')\n    // }\n    // this.validatePincodeCols(resp);\n    // let erroricPincodeCols = this.validatePincodeCols(resp);\n    // if(erroricPincodeCols.length > 0){\n    //     majorErrors.push(' Invalid used for - ' + erroricPincodeCols.join(','))\n    // }\n    let uploaded_file_name = localStorage.getItem('RECENT_UPLOADED_SRVC_REQ_EXCEL');\n    if (fileName == uploaded_file_name) {\n      majorErrors.push('Duplicate excel !! This excel was already uploaded successfully');\n    }\n    // init state and then set new values to #bug fix\n    if (Object.keys(this.majorErroricRowsKeys).length > 0) {\n      this.initFileCheckVars(false);\n    } else {\n      this.initFileCheckVars();\n    }\n    if (startRow != 1) {\n      resp.rows = resp.rows.slice(1);\n    }\n    // reject();\n    // this.setState(this.initState, () => {\n    this.setState({\n      cols: resp.cols,\n      rows: [...this.state.rows, ...resp.rows],\n      errorInfile: majorErrors.length > 0 || Object.keys(this.majorErroricRowsKeys).length > 0,\n      majorErrors: majorErrors,\n      readyToUploadRows: [],\n      fileCheckingInProg: true,\n      bulkUploadProgress: 1,\n      fileName: fileName\n    });\n  }\n  validatePincodeCols(resp) {\n    let erroricColumns = [];\n    let columns = resp.rows[0];\n    let onlyRows = [...resp.rows];\n    delete onlyRows[0];\n    onlyRows.map((singleRow, rowNo) => {\n      singleRow.map((singleCellValue, index) => {\n        let fieldName = columns[index];\n        let fieldMeta = this.getSingleColumFieldMeta(fieldName);\n        let value = singleCellValue.toString().length;\n        if (fieldMeta.key == 'cust_pincode') {\n          var _this$props$orgSettin, _this$props$orgSettin2;\n          // let org_pincode_length = ConfigHelpers.getOrgPincodeLength();\n          if (singleCellValue != '' && value != ((_this$props$orgSettin = (_this$props$orgSettin2 = this.props.orgSettingsData) === null || _this$props$orgSettin2 === void 0 ? void 0 : _this$props$orgSettin2.selected_country_pincode_length) !== null && _this$props$orgSettin !== void 0 ? _this$props$orgSettin : 6)) {\n            erroricColumns.push(fieldMeta.label);\n            if (!this.majorErroricRowsKeys[rowNo]) {\n              this.majorErroricRowsKeys[rowNo] = true;\n            }\n          }\n        }\n      });\n    });\n    return erroricColumns;\n  }\n  validateDateCols(resp) {\n    let erroricColumns = [];\n    let columns = resp.rows[0];\n    let onlyRows = [...resp.rows];\n    delete onlyRows[0];\n    onlyRows.map((singleRow, rowNo) => {\n      singleRow.map((singleCellValue, index) => {\n        let fieldName = columns[index];\n        let fieldMeta = this.getSingleColumFieldMeta(fieldName);\n        if (fieldMeta.widget == 'date-picker') {\n          if (singleCellValue != '') {\n            let value = ExcelDateToJSDate(singleCellValue);\n            if (value) {\n              // value = moment(value);\n              // value = value.format('MMM-DD-YYYY');\n            } else {\n              //\n              erroricColumns.push(fieldMeta.label);\n              if (!this.majorErroricRowsKeys[rowNo]) {\n                this.majorErroricRowsKeys[rowNo] = true;\n              }\n            }\n          }\n        }\n      });\n    });\n    return erroricColumns;\n  }\n  getFormDataForRow(rowData) {\n    let columns = this.state.rows[0];\n    let formDataProtoFrRow = {};\n    columns.map((columnTitle, index) => {\n      let singleFieldMeta = this.getSingleColumFieldMeta(columnTitle);\n      let value = rowData[index];\n      if (singleFieldMeta.widget == 'date-picker') {\n        // console.log('value',value);\n        if (value) {\n          // // this is a date field\n          let valueConverted = ExcelDateToJSDate(value);\n          if (valueConverted) {\n            value = moment(valueConverted);\n            value = value.format('YYYY-MM-DD');\n          } else {\n            value = moment(value);\n          }\n        } else {\n          value = undefined;\n        }\n      } else if (singleFieldMeta.cust_widget === 'TimePicker') {\n        console.log(\"TimePicker Value\", value, typeof value);\n        if (value) {\n          const convertedValue = this.convertTimePickerValue(value);\n          // If conversion was successful (returned a different value), use it\n          if (convertedValue !== value) {\n            value = convertedValue;\n            console.log(\"TimePicker Value after\", value);\n          } else {\n            // If conversion failed, convert to string as fallback\n            value = '' + value;\n          }\n        } else {\n          value = undefined;\n        }\n      } else if (singleFieldMeta.widget != 'select' && singleFieldMeta.widget != 'number' && value) {\n        value = '' + value;\n      } else if (this.props.update && !value && singleFieldMeta.widget != 'select') {\n        value = '';\n      } else if (this.props.bulkUpdateAuthority && !value && singleFieldMeta.widget == 'select') {\n        value = '';\n      }\n      if ((value || !this.props.bulkUpdateAuthority) && singleFieldMeta.options) {\n        var _singleFieldMeta$widg, _value, _value$split;\n        value = '' + value;\n        // this has options the value should not be out of the set\n        if (((_singleFieldMeta$widg = singleFieldMeta.widgetProps) === null || _singleFieldMeta$widg === void 0 ? void 0 : _singleFieldMeta$widg.mode) == 'multiple' && ((_value = value) === null || _value === void 0 ? void 0 : (_value$split = _value.split(',')) === null || _value$split === void 0 ? void 0 : _value$split.length) >= 1) {\n          // the entry has multiple values\n          let finalValue = [];\n          value.split(',').map(singleValue => {\n            let matchingEntry = singleFieldMeta.options.filter(singleOption => singleOption.label == (singleValue === null || singleValue === void 0 ? void 0 : singleValue.trim()));\n            if (matchingEntry.length > 0) {\n              finalValue.push(matchingEntry[0].value);\n            }\n          });\n          if (finalValue.length == 0) {\n            value = undefined;\n          } else {\n            value = finalValue;\n          }\n        } else {\n          let matchingEntry;\n          if (this.props.bulkUpdateAuthority) {\n            matchingEntry = singleFieldMeta.options.filter(singleOption => {\n              var _singleOption$label, _value2;\n              return ((_singleOption$label = singleOption.label) === null || _singleOption$label === void 0 ? void 0 : _singleOption$label.trim()) == ((_value2 = value) === null || _value2 === void 0 ? void 0 : _value2.trim());\n            });\n          } else {\n            matchingEntry = singleFieldMeta.options.filter(singleOption => {\n              var _value3;\n              return singleOption.label == ((_value3 = value) === null || _value3 === void 0 ? void 0 : _value3.trim());\n            });\n          }\n          if (matchingEntry.length == 0) {\n            value = undefined;\n          } else {\n            value = matchingEntry[0].value;\n          }\n        }\n      }\n      formDataProtoFrRow[singleFieldMeta.key] = value;\n    });\n    formDataProtoFrRow = convertDateFieldsToMoments(formDataProtoFrRow, this.getDataProto());\n    return formDataProtoFrRow;\n  }\n  isReadyToUpload() {\n    var _this$getData;\n    if (((_this$getData = this.getData()) === null || _this$getData === void 0 ? void 0 : _this$getData.length) > 0) {\n      return true;\n    }\n    return false;\n  }\n  getDataProto() {\n    let {\n      dataProto,\n      demoMode,\n      debugMode,\n      multipleFieldDataModals\n    } = this.props;\n    if (demoMode && !dataProto) {\n      return dataProtoFrBulkUpload;\n    }\n\n    // console.log('dataProto==>', dataProto);\n    return dataProto;\n  }\n  onStartBulkCreation(event) {\n    // if(this.props.demoMode){\n    //     this.setState({\n    //         bulkUploadInProgress:true,\n    //         bulkUploadProgress: 0\n    //     },this.dummyProgUpload(0))\n    //     return;\n    // }\n\n    // initiate bulk upload\n    this.setState({\n      bulkUploadInProgress: true,\n      bulkUploadProgress: 50\n    });\n    let params = {};\n    params['batch_data'] = this.getPostData();\n    const onComplete = resp => {\n      // console.log('Bulk resp',resp);\n      this.setState({\n        bulkUploadInProgress: false,\n        bulkUploadProgress: 100,\n        uploadComplete: true,\n        uploadResp: `Uploaded successfully`\n      }, this.tellParent(resp.data.entry_ids));\n      // this.updateClosureToParent();\n      LocalStorageManager.setData('RECENT_UPLOADED_SRVC_REQ_EXCEL', this.state.fileName);\n    };\n    const onError = error => {\n      // compare statuses here\n      let errorDecoded = http_utils.decodeErrorToMessage(error);\n      if (typeof errorDecoded == 'string') {\n        errorDecoded = JSON.parse(errorDecoded).hint;\n      }\n      this.setState({\n        bulkUploadInProgress: false,\n        bulkUploadProgress: 100,\n        uploadComplete: true,\n        uploadResp: errorDecoded\n      });\n    };\n    http_utils.performPostCall(this.props.submitUrl, params, onComplete, onError);\n\n    // let chunkSize = this.props.uploadChunkSize || defaultChunkSize;\n    // let readyToUploadRows = this.state.readyToUploadRows;\n    // let chunksToUpload = _.chunk(readyToUploadRows,chunkSize);\n    // console.log('chunks',chunksToUpload)\n  }\n  tellParent(entry_ids) {\n    if (this.props.onDataModified) {\n      this.props.onDataModified(entry_ids);\n    }\n  }\n  dummyProgUpload(prog) {\n    if (prog > 100) {\n      this.setState({\n        bulkUploadInProgress: false,\n        bulkUploadProgress: 100,\n        uploadComplete: true\n      });\n    } else {\n      prog = prog + 10;\n      this.setState({\n        bulkUploadInProgress: true,\n        bulkUploadProgress: prog\n      }, () => setTimeout(prog => this.dummyProgUpload(prog), 1000, prog));\n    }\n  }\n  handleClear() {\n    this.initFileCheckVars();\n    this.setState({\n      uploadComplete: false,\n      clearFileInput: true\n    }, () => this.setState({\n      cols: [],\n      rows: [],\n      readyToUploadRows: [],\n      clearFileInput: false\n    }));\n  }\n  handleFailedRow(batch_data_row_key) {\n    if (!this.erroricRowsKeys[batch_data_row_key]) {\n      this.erroricRowsKeys[batch_data_row_key] = true;\n      this.erroricRows = this.erroricRows + 1;\n    }\n    if (!this.state.errorInfile) {\n      this.setState({\n        errorInfile: true\n      });\n    }\n    this.checkIfAllRowsScanningIsDone();\n  }\n  handleReadyToUpload(readyToUploadRow, batch_data_row_key) {\n    let readyToUploadRowKeys = this.readyToUploadRowKeys;\n    // console.log('readyToUploadRowKeys',readyToUploadRowKeys);\n    if (!readyToUploadRowKeys[batch_data_row_key]) {\n      let readyToUploadRows = this.readyToUploadRows;\n      readyToUploadRows.push(readyToUploadRow);\n      readyToUploadRowKeys[batch_data_row_key] = true;\n      this.readyToUploadRowKeys = readyToUploadRowKeys;\n      this.readyToUploadRows = readyToUploadRows;\n    }\n    this.checkIfAllRowsScanningIsDone();\n  }\n  checkIfAllRowsScanningIsDone() {\n    let numOfValidRows = this.readyToUploadRows.length;\n    // console.log(\"numOfValidRows\",numOfValidRows);\n    let erroricRows = this.erroricRows;\n    // console.log(\"erroricRows\",erroricRows);\n    let totalRowsScanned = numOfValidRows + erroricRows;\n    let totalRows = this.getData().length;\n    if (totalRowsScanned == totalRows && this.state.readyToUploadRows.length == 0) {\n      // this means all processing is done\\\n      // console.log('All processing done');\n      // lets update the state now\n      this.setState({\n        erroricRows: erroricRows,\n        readyToUploadRowKeys: this.readyToUploadRowKeys,\n        readyToUploadRows: this.readyToUploadRows,\n        fileCheckingInProg: false,\n        currentPage: this.state.isExcelUploaded ? 1 : this.state.currentPage,\n        bulkUploadProgress: 0,\n        isExcelUploaded: false\n      });\n    } else if (this.state.fileCheckingInProg) {\n      // we will need to see if there is a need to page\n      let numberOfPages = Math.ceil(totalRows / 10);\n      let currentPage = this.state.currentPage;\n      if (currentPage <= numberOfPages && totalRowsScanned == currentPage * 10) {\n        this.setState({\n          currentPage: currentPage + 1,\n          bulkUploadProgress: Math.round(currentPage / numberOfPages * 100)\n        });\n        // console.log('We need to change page now');\n      }\n    }\n  }\n  getPostData() {\n    return this.state.readyToUploadRows;\n  }\n  getPageNoOnTheBaseOfRow(erroricRowsKey) {\n    return Math.ceil(erroricRowsKey / pageSize);\n  }\n  getRowOnTheBaseOfPageSize(erroricRowsKey) {\n    if (erroricRowsKey > pageSize) {\n      return Math.ceil(erroricRowsKey % pageSize) ? Math.ceil(erroricRowsKey % pageSize) : pageSize;\n    }\n    return erroricRowsKey;\n  }\n  render() {\n    let {\n      demoMode,\n      debugMode,\n      maxRows,\n      renderFormsForRows\n    } = this.props;\n    maxRows = maxRows || defaultMaxRows;\n    const {\n      bulkUploadInProgress,\n      bulkUploadProgress,\n      uploadComplete,\n      clearFileInput,\n      errorInfile,\n      erroricRows,\n      majorErrors,\n      uploadResp,\n      fileCheckingInProg\n    } = this.state;\n    const clearInputFileProp = clearFileInput ? {\n      value: ''\n    } : {};\n    const readyToUpload = this.isReadyToUpload();\n    const dataProto = this.getDataProto();\n    return /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 13\n      }\n    }, demoMode && /*#__PURE__*/React.createElement(Alert, {\n      message: \"Running in demo mode\",\n      type: \"warning\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 21\n      }\n    }), debugMode && /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(Input, {\n      placeholder: \"Output JSON will show here\",\n      value: JSON.stringify(this.getPostData()),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 25\n      }\n    })), !this.props.update && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"h5\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 25\n      }\n    }, \"Accepted columns\", /*#__PURE__*/React.createElement(\"small\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 29\n      }\n    }, \"( * are mandatory )\"), /*#__PURE__*/React.createElement(ExcelFile, {\n      filename: this.props.update ? 'Bulk_update_template' : 'Bulk_upload_template',\n      element: /*#__PURE__*/React.createElement(Button, {\n        icon: /*#__PURE__*/React.createElement(DownloadOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 47\n          }\n        }),\n        className: \"gx-mb-0\",\n        type: \"link\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 37\n        }\n      }, \"Template\"),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(ExcelSheet, {\n      data: [],\n      name: \"Sheet 1\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 33\n      }\n    }, dataProto.filter(fieldMeta => fieldMeta.label).map(fieldMeta => /*#__PURE__*/React.createElement(ExcelColumn, {\n      label: fieldMeta.label,\n      value: fieldMeta.label,\n      style: {\n        fill: {\n          patternType: 'solid',\n          fgColor: {\n            rgb: 'FFFF0000'\n          }\n        }\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 45\n      }\n    }))))), /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        overflowX: 'auto'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 936,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"table\", {\n      class: \"gx-w-100\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"tbody\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 33\n      }\n    }, /*#__PURE__*/React.createElement(\"tr\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 37\n      }\n    }, dataProto.map((singleFieldMeta, index) => singleFieldMeta.label && /*#__PURE__*/React.createElement(\"td\", {\n      key: index,\n      className: singleFieldMeta.required ? 'gx-bg-amber-light' : '',\n      style: {\n        border: '2px solid #dddddd',\n        textAlign: 'center',\n        padding: '8px',\n        fontSize: '0.8rem'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 53\n      }\n    }, singleFieldMeta.label, singleFieldMeta.options && /*#__PURE__*/React.createElement(\"td\", {\n      className: \"gx-d-flex gx-justify-content-center gx-align-items-center\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 61\n      }\n    }, /*#__PURE__*/React.createElement(Button, {\n      type: \"link\",\n      onClick: () => this.showSelectFieldOptionsModal(singleFieldMeta.key),\n      size: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 964,\n        columnNumber: 65\n      }\n    }, \"View Data\"), /*#__PURE__*/React.createElement(Modal, {\n      title: `${singleFieldMeta.label} list`,\n      visible: this.state.multipleFieldDataModals[singleFieldMeta.key],\n      footer: null,\n      onCancel: () => this.hideSelectFieldOptionsModal(singleFieldMeta.key),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 65\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 69\n      }\n    }, singleFieldMeta.options.map((option, index) => /*#__PURE__*/React.createElement(Paragraph, {\n      className: \"gx-mb-1\",\n      key: index,\n      copyable: {\n        text: `${option === null || option === void 0 ? void 0 : option.label}` // Text to be copied\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1001,\n        columnNumber: 81\n      }\n    }, index + 1, \".\", ' ', option === null || option === void 0 ? void 0 : option.label))))))))))), /*#__PURE__*/React.createElement(\"hr\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1033,\n        columnNumber: 25\n      }\n    })), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-mt-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1036,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1037,\n        columnNumber: 21\n      }\n    }, \"Upload an excel\"), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-d-flex\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: '30px'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(FileIcon, Object.assign({\n      extension: \".xlsx\"\n    }, defaultStyles['xlsx'], {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1040,\n        columnNumber: 29\n      }\n    }))), /*#__PURE__*/React.createElement(\"input\", Object.assign({\n      type: \"file\"\n    }, clearInputFileProp, {\n      onClick: e => this.setState({\n        fileCheckingInProg: true\n      }),\n      disabled: bulkUploadInProgress || uploadComplete,\n      onChange: e => {\n        this.handleFileChange(e);\n      },\n      style: {\n        padding: '10px'\n      },\n      \"data-testid\": \"upload-excel-file\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 25\n      }\n    }))), bulkUploadProgress > 0 && bulkUploadProgress < 100 && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Spin, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1062,\n        columnNumber: 29\n      }\n    }), /*#__PURE__*/React.createElement(\"br\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 29\n      }\n    }), \"Checking file..\", /*#__PURE__*/React.createElement(Progress\n    // type=\"circle\"\n    , {\n      className: \"gx-ml-2\",\n      percent: bulkUploadProgress,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1065,\n        columnNumber: 29\n      }\n    })), maxRows > 0 && !this.props.update && /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 25\n      }\n    }, \"**Max \", maxRows, \" rows supported at a time\"), !this.props.update && /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1077,\n        columnNumber: 29\n      }\n    }, \"**If a column has data condition and if data is out of possible values then it will be treated as empty\", ' '), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-orange\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1082,\n        columnNumber: 29\n      }\n    }, \"**Date format should be YYYY-MM-DD\", ' ')), this.props.timeFormatMsg && !this.props.update && /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 25\n      }\n    }, \"**Time format should be HH:MM AM/PM (Eg: 09:30 AM, 2:30 PM)\", ' '), readyToUpload && /*#__PURE__*/React.createElement(React.Fragment, null, majorErrors.length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, majorErrors.map((singleLine, index) => /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      key: index,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1097,\n        columnNumber: 41\n      }\n    }, singleLine))), Object.keys(this.majorErroricRowsKeys).length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, Object.keys(this.majorErroricRowsKeys).map(majorErroricRowsKey => /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1108,\n        columnNumber: 45\n      }\n    }, \"error on page\", ' ', this.getPageNoOnTheBaseOfRow(majorErroricRowsKey), ' ', \"in row\", ' ', this.getRowOnTheBaseOfPageSize(majorErroricRowsKey)))), errorInfile && erroricRows > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, Object.keys(this.erroricRowsKeys).map(singleErroricRowsKey => /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1126,\n        columnNumber: 45\n      }\n    }, \"error on page\", ' ', this.getPageNoOnTheBaseOfRow(singleErroricRowsKey), ' ', \"in row\", ' ', this.getRowOnTheBaseOfPageSize(singleErroricRowsKey)))), /*#__PURE__*/React.createElement(Button, {\n      type: \"primary\",\n      disabled: !readyToUpload || bulkUploadInProgress || uploadComplete || errorInfile || fileCheckingInProg,\n      onClick: e => {\n        this.onStartBulkCreation(e);\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 29\n      }\n    }, this.props.update ? 'Start bulk updation' : 'Start bulk creation'),\n    /*#__PURE__*/\n    // uploadComplete &&\n    React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Button, {\n      onClick: e => this.handleClear(),\n      type: \"link\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1160,\n        columnNumber: 37\n      }\n    }, \"Reset/Clear\"), /*#__PURE__*/React.createElement(\"br\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1166,\n        columnNumber: 37\n      }\n    }), uploadComplete && /*#__PURE__*/React.createElement(Alert, {\n      type: \"info\",\n      message: /*#__PURE__*/React.createElement(\"div\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1171,\n          columnNumber: 49\n        }\n      }, /*#__PURE__*/React.createElement(\"h2\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1172,\n          columnNumber: 53\n        }\n      }, \"Bulk upload results\"), /*#__PURE__*/React.createElement(\"br\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1173,\n          columnNumber: 53\n        }\n      }), /*#__PURE__*/React.createElement(\"p\", {\n        style: this.props.errorHasLineBreaks ? {\n          whiteSpace: 'pre-wrap'\n        } : {},\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1174,\n          columnNumber: 53\n        }\n      }, uploadResp)),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1168,\n        columnNumber: 41\n      }\n    })), bulkUploadInProgress && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Spin, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1195,\n        columnNumber: 37\n      }\n    }), /*#__PURE__*/React.createElement(\"br\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1196,\n        columnNumber: 37\n      }\n    }), \"Upload in progress..\"), renderFormsForRows ? /*#__PURE__*/React.createElement(List, {\n      itemLayout: \"itemLayout\",\n      size: \"large\",\n      bordered: true,\n      pagination: {\n        onChange: page => {\n          console.log(page);\n          this.setState({\n            currentPage: page\n          });\n        },\n        pageSize: 10,\n        current: this.state.currentPage\n      },\n      dataSource: this.getData(),\n      renderItem: item => /*#__PURE__*/React.createElement(List.Item, {\n        key: item.batch_data_row_key,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1218,\n          columnNumber: 41\n        }\n      }, /*#__PURE__*/React.createElement(SingleRowFormFrBuilder, {\n        meta: this.getMeta(),\n        renderFullForm: true,\n        initialValues: this.getFormDataForRow(item),\n        onValidationError: e => this.handleFailedRow(item.batch_data_row_key),\n        onReadyToUpload: formData => this.handleReadyToUpload(formData),\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1221,\n          columnNumber: 45\n        }\n      })),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1202,\n        columnNumber: 33\n      }\n    }) : /*#__PURE__*/React.createElement(Table, {\n      className: \"gx-table-responsive\",\n      bordered: true,\n      size: \"small\",\n      pagination: {\n        onChange: page => {\n          console.log(page);\n          this.setState({\n            currentPage: page\n          });\n        },\n        pageSize: 10,\n        current: this.state.currentPage\n      },\n      columns: this.getColumns(),\n      dataSource: this.getData()\n      // dataSource={[]}\n      ,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1242,\n        columnNumber: 33\n      }\n    }))));\n  }\n}\nexport default BulkUploader;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Col", "Input", "List", "message", "Modal", "Progress", "Row", "Spin", "Table", "Tag", "Typography", "FormBuilder", "OutTable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "Component", "useEffect", "useState", "convertDateFieldsToMoments", "decodeAntdFormErrorsToString", "ExcelDateToJSDate", "moment", "Form", "defaultStyles", "FileIcon", "http_utils", "ReactExport", "DownloadOutlined", "LocalStorageManager", "ConfigHelpers", "ruleValidator", "Paragraph", "ExcelFile", "ExcelSheet", "ExcelColumn", "pageSize", "defaultMaxRows", "dataProtoFrBulkUpload", "label", "required", "key", "rules", "pattern", "RegExp", "min", "max", "widget", "options", "value", "SingleRowFormFrBuilder", "props", "meta", "initialValues", "onValidationError", "onReadyToUpload", "renderFullForm", "error", "setError", "undefined", "checked", "setChecked", "setTimeout", "validate", "fields", "isBulkAssignComp", "length", "_validate$", "errorString", "errors", "join", "createElement", "color", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "Fragment", "BulkUploader", "initFileCheckVars", "resetMajorErroricRowsKeys", "readyToUploadRowKeys", "readyToUploadRows", "erroricRows", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "majorErroricRowsKeys", "constructor", "initState", "cols", "rows", "bulkUploadInProgress", "bulkUploadProgress", "clearFileInput", "uploadComplete", "errorInfile", "majorErrors", "uploadResp", "fileCheckingInProg", "currentPage", "isExcelUploaded", "multipleFieldDataModals", "state", "showSelectFieldOptionsModal", "setState", "hideSelectFieldOptionsModal", "getMeta", "finalFields", "dataProto", "getDataProto", "map", "singleFieldMeta", "colSpan", "render<PERSON>iew", "push", "columns", "formItemLayout", "getSingleColumFieldMeta", "columnText", "returnMeta", "convertTimePickerValue", "milliseconds", "utc", "format", "parsed", "<PERSON><PERSON><PERSON><PERSON>", "getColumns", "columsArray", "firstRow", "title", "dataIndex", "getUploadStateFieldIndex", "render", "text", "record", "batch_data_row_key", "renderFormsForRows", "getFormDataForRow", "e", "handleFailedRow", "formData", "handleReadyToUpload", "singleCellValue", "index", "fieldMeta", "singleColumnData", "cust_widget", "getData", "rowsInState", "finalRows", "singleRow", "handleFileChange", "event", "fileObj", "target", "files", "loading", "excelData", "Promise", "resolve", "reject", "err", "resp", "header", "CHUNK_SIZE", "totalRows", "startRow", "chunkRows", "slice", "chunkWithHeader", "processedRows", "filter", "cellVal", "doBasicValidationsAndRereshUI", "name", "success", "maxRows", "currentRowsLength", "acceptedColumns", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "includes", "unR<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "columnTitle", "validateDateCols", "uploaded_file_name", "localStorage", "getItem", "Object", "keys", "validatePincodeCols", "erroricColumns", "onlyRows", "rowNo", "fieldName", "toString", "_this$props$orgSettin", "_this$props$orgSettin2", "orgSettingsData", "selected_country_pincode_length", "rowData", "formDataProtoFrRow", "valueConverted", "console", "log", "convertedValue", "update", "bulkUpdateAuthority", "_singleFieldMeta$widg", "_value", "_value$split", "widgetProps", "mode", "split", "finalValue", "singleValue", "matchingEntry", "singleOption", "trim", "_singleOption$label", "_value2", "_value3", "isReadyToUpload", "_this$getData", "demoMode", "debugMode", "onStartBulkCreation", "params", "getPostData", "onComplete", "tellParent", "data", "entry_ids", "setData", "onError", "errorDecoded", "decodeErrorToMessage", "JSON", "parse", "hint", "performPostCall", "submitUrl", "onDataModified", "dummyProgUpload", "prog", "handleClear", "checkIfAllRowsScanningIsDone", "readyToUploadRow", "numOfValidRows", "totalRowsScanned", "numberOfPages", "Math", "ceil", "round", "getPageNoOnTheBaseOfRow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getRowOnTheBaseOfPageSize", "clearInputFileProp", "readyToUpload", "type", "placeholder", "stringify", "filename", "element", "icon", "style", "fill", "patternType", "fgColor", "rgb", "overflowX", "class", "border", "textAlign", "padding", "fontSize", "onClick", "size", "visible", "footer", "onCancel", "option", "copyable", "width", "assign", "extension", "disabled", "onChange", "percent", "timeFormatMsg", "singleLine", "majorErroricRowsKey", "singleErroricRowsKey", "errorHasLineBreaks", "whiteSpace", "itemLayout", "bordered", "pagination", "page", "current", "dataSource", "renderItem", "item", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/BulkUploader.js"], "sourcesContent": ["import {\r\n    <PERSON><PERSON>,\r\n    Button,\r\n    Col,\r\n    Input,\r\n    List,\r\n    message,\r\n    Modal,\r\n    Progress,\r\n    Row,\r\n    Spin,\r\n    Table,\r\n    Tag,\r\n    Typography,\r\n} from 'antd';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport { OutTable, ExcelRenderer } from 'react-excel-renderer';\r\nimport React, { Component, useEffect, useState } from 'react';\r\nimport {\r\n    convertDateFieldsToMoments,\r\n    decodeAntdFormErrorsToString,\r\n    ExcelDateToJSDate,\r\n} from '../../util/helpers';\r\nimport moment from 'moment';\r\nimport { Form } from 'antd';\r\nimport { defaultStyles, FileIcon } from 'react-file-icon';\r\nimport http_utils from '../../util/http_utils';\r\nimport ReactExport from 'react-export-excel';\r\nimport { DownloadOutlined } from '@ant-design/icons';\r\nimport LocalStorageManager from '../../util/LocalStorageManager';\r\nimport ConfigHelpers from '../../util/ConfigHelpers';\r\nimport ruleValidator from './AntdRuleValidator';\r\n\r\nconst { Paragraph } = Typography;\r\n\r\nconst ExcelFile = ReactExport.ExcelFile;\r\nconst ExcelSheet = ReactExport.ExcelFile.ExcelSheet;\r\nconst ExcelColumn = ReactExport.ExcelFile.ExcelColumn;\r\nconst pageSize = 10;\r\n// const defaultChunkSize = 1000;\r\nconst defaultMaxRows = 1000;\r\nconst dataProtoFrBulkUpload = [\r\n    {\r\n        label: 'Name',\r\n        required: true,\r\n        key: 'user_name',\r\n    },\r\n    {\r\n        key: 'user_number',\r\n        label: 'Mobile(+91)',\r\n        required: true,\r\n        rules: [\r\n            {\r\n                pattern: new RegExp('^[0-9]*$'),\r\n                message: 'Incorrect number',\r\n            },\r\n            { min: 10 },\r\n            { max: 10 },\r\n        ],\r\n    },\r\n    {\r\n        label: 'Gender',\r\n        required: true,\r\n        key: 'user_gender',\r\n        widget: 'select',\r\n        options: [\r\n            {\r\n                label: 'Male',\r\n                value: 'male',\r\n            },\r\n            {\r\n                label: 'Female',\r\n                value: 'female',\r\n            },\r\n        ],\r\n    },\r\n    {\r\n        label: 'Weight',\r\n        required: false,\r\n        key: 'user_weight',\r\n        widget: 'number',\r\n    },\r\n    {\r\n        label: 'DOB',\r\n        required: false,\r\n        key: 'user_dob',\r\n        widget: 'date-picker',\r\n    },\r\n];\r\n\r\nconst SingleRowFormFrBuilder = (props) => {\r\n    // const [form] = Form.useForm();\r\n    const {\r\n        meta,\r\n        initialValues,\r\n        onValidationError,\r\n        onReadyToUpload,\r\n        renderFullForm,\r\n    } = props;\r\n    const [error, setError] = useState(undefined);\r\n    const [checked, setChecked] = useState(false);\r\n    // useEffect(() => {\r\n    //     if (!checked) {\r\n    //         // console.log('BulkAssign Checked row',initialValues)\r\n    //         setChecked(true);\r\n    //         setTimeout(async () => {\r\n    //             let formData = form.getFieldsValue();\r\n    //             form.validateFields()\r\n    //                 .then((values) => {\r\n    //                     setError('success');\r\n    //                     onReadyToUpload(formData);\r\n    //                 })\r\n    //                 .catch((err) => {\r\n    //                     // console.log('err',err)\r\n    //                     if (err.errorFields?.length > 0) {\r\n    //                         setError(decodeAntdFormErrorsToString(err));\r\n    //                         // setError(JSON.stringify(err.errors))\r\n    //                         onValidationError(err);\r\n    //                     } else {\r\n    //                         onReadyToUpload(formData);\r\n    //                     }\r\n    //                 });\r\n    //         }, 10);\r\n    //     }\r\n    // });\r\n    if (!checked) {\r\n        setChecked(true);\r\n        setTimeout(async () => {\r\n            const validate = await ruleValidator(\r\n                meta.fields,\r\n                initialValues,\r\n                props.isBulkAssignComp\r\n            );\r\n            if (validate.length > 0) {\r\n                const errorString = validate?.[0]?.errors.join(',');\r\n                setError(errorString || 'Error');\r\n                onValidationError(validate);\r\n            } else {\r\n                setError('success');\r\n                onReadyToUpload(initialValues);\r\n            }\r\n        }, 10);\r\n    }\r\n\r\n    return !renderFullForm && error ? (\r\n        error == 'success' ? (\r\n            <Tag color=\"success\">Ready</Tag>\r\n        ) : (\r\n            // <Tag color=\"error\">{error}</Tag>\r\n            <span className=\"gx-text-red\">{error}</span>\r\n        )\r\n    ) : (\r\n        // <>\r\n        //     <Spin></Spin>\r\n        //     <Form\r\n        //         layout=\"vertical\"\r\n        //         form={form}\r\n        //         className=\"gx-d-none\"\r\n        //         initialValues={initialValues}\r\n        //     >\r\n        //         <FormBuilder form={form} meta={meta} />\r\n        //     </Form>\r\n        // </>\r\n        <></>\r\n    );\r\n};\r\n\r\nclass BulkUploader extends Component {\r\n    initState = {\r\n        cols: [],\r\n        rows: [],\r\n        bulkUploadInProgress: false,\r\n        bulkUploadProgress: 0,\r\n        clearFileInput: false,\r\n        uploadComplete: false,\r\n        errorInfile: false,\r\n        erroricRows: 0,\r\n        majorErrors: [],\r\n        readyToUploadRows: [],\r\n        readyToUploadRowKeys: [],\r\n        uploadResp: '',\r\n        fileCheckingInProg: false,\r\n        currentPage: 1,\r\n        fileName: '',\r\n        isExcelUploaded: false,\r\n        multipleFieldDataModals: {},\r\n    };\r\n\r\n    state = this.initState;\r\n\r\n    showSelectFieldOptionsModal = (key) => {\r\n        let multipleFieldDataModals = this.state.multipleFieldDataModals;\r\n        multipleFieldDataModals[key] = true;\r\n        this.setState({ multipleFieldDataModals });\r\n    };\r\n    hideSelectFieldOptionsModal = (key) => {\r\n        let multipleFieldDataModals = this.state.multipleFieldDataModals;\r\n        multipleFieldDataModals[key] = false;\r\n        this.setState({ multipleFieldDataModals });\r\n    };\r\n\r\n    initFileCheckVars(resetMajorErroricRowsKeys = true) {\r\n        this.readyToUploadRowKeys = {};\r\n        this.readyToUploadRows = [];\r\n        this.erroricRows = 0;\r\n        this.erroricRowsKeys = {};\r\n        if (resetMajorErroricRowsKeys) {\r\n            this.majorErroricRowsKeys = {};\r\n        }\r\n    }\r\n\r\n    constructor(props) {\r\n        super(props);\r\n        // console.log('Rxd props', this.props);\r\n        this.initFileCheckVars();\r\n    }\r\n\r\n    getMeta() {\r\n        let finalFields = [];\r\n        let dataProto = this.getDataProto();\r\n        dataProto.map((singleFieldMeta) => {\r\n            if (singleFieldMeta.label) {\r\n                singleFieldMeta.colSpan = 2;\r\n                singleFieldMeta.renderView = () => <div></div>;\r\n                finalFields.push(singleFieldMeta);\r\n            }\r\n        });\r\n        const meta = {\r\n            columns: this.state.rows[0].length,\r\n            formItemLayout: null,\r\n            fields: finalFields,\r\n        };\r\n        return meta;\r\n    }\r\n\r\n    getSingleColumFieldMeta(columnText) {\r\n        let returnMeta = {};\r\n        let dataProto = this.getDataProto();\r\n        dataProto.map((singleFieldMeta) => {\r\n            if (singleFieldMeta.label == columnText) {\r\n                returnMeta = singleFieldMeta;\r\n            }\r\n        });\r\n        return returnMeta;\r\n    }\r\n\r\n    // Helper function to convert TimePicker values from Excel format\r\n    convertTimePickerValue(value) {\r\n        if (value != '' && value != null && value != undefined) {\r\n            if (typeof value === 'number' && value > 0 && value < 1) {\r\n                // Excel converts time to decimal (0.5 = 12:00 PM, 0.93 ≈ 10:20 PM)\r\n                const milliseconds = value * 24 * 60 * 60 * 1000;\r\n                return moment.utc(milliseconds).format('hh:mm A');\r\n            } else if (typeof value === 'string') {\r\n                // Handle string time formats with various spacing\r\n                const parsed = moment(value, ['h:mm A', 'hh:mm A', 'h:mm  A', 'hh:mm  A', 'H:mm', 'HH:mm', 'h:mmA', 'hh:mmA'], true);\r\n                if (parsed.isValid()) {\r\n                    return parsed.format('hh:mm A');\r\n                }\r\n            }\r\n        }\r\n        return value;\r\n    }\r\n\r\n    getColumns(columsArray) {\r\n        // this will be from an excel\r\n        let firstRow = columsArray || this.state.rows[0];\r\n        let columns = [];\r\n        if (firstRow) {\r\n            columns.push({\r\n                title: 'Upload state',\r\n                dataIndex: this.getUploadStateFieldIndex(),\r\n                key: this.getUploadStateFieldIndex(),\r\n                render: (text, record) => {\r\n                    return (\r\n                        <SingleRowFormFrBuilder\r\n                            key={record.batch_data_row_key}\r\n                            meta={this.getMeta()}\r\n                            renderFullForm={this.props.renderFormsForRows}\r\n                            initialValues={this.getFormDataForRow(record)}\r\n                            onValidationError={(e) =>\r\n                                this.handleFailedRow(record.batch_data_row_key)\r\n                            }\r\n                            onReadyToUpload={(formData) =>\r\n                                this.handleReadyToUpload(\r\n                                    formData,\r\n                                    record.batch_data_row_key\r\n                                )\r\n                            }\r\n                            isBulkAssignComp={this.props.isBulkAssignComp}\r\n                        />\r\n                    );\r\n                },\r\n            });\r\n            !this.props.renderFormsForRows &&\r\n                firstRow.map((singleCellValue, index) => {\r\n                    let fieldMeta =\r\n                        this.getSingleColumFieldMeta(singleCellValue);\r\n                    let singleColumnData = {\r\n                        title: singleCellValue,\r\n                        dataIndex: index,\r\n                        key: index,\r\n                        className: fieldMeta.required\r\n                            ? 'gx-bg-amber-light'\r\n                            : '',\r\n                    };\r\n                    if (fieldMeta.widget == 'date-picker') {\r\n                        singleColumnData.render = (text, record) => {\r\n                            // this is a date field\r\n                            if (text != '') {\r\n                                let value = ExcelDateToJSDate(text);\r\n                                if (value) {\r\n                                    value = moment(value);\r\n                                    value = value.format('MMM-DD-YYYY');\r\n                                } else {\r\n                                    value = moment(value);\r\n                                }\r\n                                return value;\r\n                            }\r\n                            return text;\r\n                        };\r\n                    } else if (fieldMeta.cust_widget === 'TimePicker') {\r\n                        singleColumnData.render = (text, record) => {\r\n                            return this.convertTimePickerValue(text);\r\n                        };\r\n                    }\r\n\r\n                    columns.push(singleColumnData);\r\n                });\r\n        }\r\n        return columns;\r\n    }\r\n\r\n    getUploadStateFieldIndex() {\r\n        // return this.state?.rows[0]?.length;\r\n        return 0;\r\n    }\r\n\r\n    getData(rowsInState = undefined) {\r\n        let rows = rowsInState || this.state.rows;\r\n        let finalRows = [];\r\n        rows.map((singleRow, index) => {\r\n            // first is header\r\n            if (index > 0) {\r\n                singleRow.batch_data_row_key = index;\r\n                finalRows.push(singleRow);\r\n            }\r\n        });\r\n        // console.log('rows',rows)\r\n        return finalRows;\r\n    }\r\n\r\n    async handleFileChange(event) {\r\n        let fileObj = event.target.files[0];\r\n        try {\r\n            await message.loading('Loading excel...');\r\n\r\n            const excelData = await new Promise((resolve, reject) => {\r\n                ExcelRenderer(fileObj, (err, resp) => {\r\n                    if (err) {\r\n                        reject('Unable to read the excel');\r\n                    } else {\r\n                        resolve(resp);\r\n                    }\r\n                });\r\n            });\r\n\r\n            const header = excelData.rows[0];\r\n            const CHUNK_SIZE = 100;\r\n            const totalRows = excelData.rows.length;\r\n            let startRow = 1;\r\n\r\n            // Process the rows in chunks\r\n            while (startRow < totalRows) {\r\n                const chunkRows = excelData.rows.slice(\r\n                    startRow,\r\n                    startRow + CHUNK_SIZE\r\n                );\r\n                const chunkWithHeader = [header, ...chunkRows];\r\n\r\n                const processedRows = chunkWithHeader.filter((singleRow) => {\r\n                    if (singleRow.length > 0) {\r\n                        singleRow = singleRow.filter(\r\n                            (cellVal) => cellVal && cellVal !== ''\r\n                        );\r\n                    }\r\n                    return singleRow.length > 0;\r\n                });\r\n                this.doBasicValidationsAndRereshUI(\r\n                    { rows: processedRows, cols: excelData.cols },\r\n                    fileObj.name,\r\n                    startRow\r\n                );\r\n\r\n                startRow += CHUNK_SIZE;\r\n            }\r\n            this.setState({ isExcelUploaded: true });\r\n            message.success('Excel loaded successfully.');\r\n        } catch (e) {\r\n            message.error(e || 'Unable to read the excel');\r\n            this.initFileCheckVars();\r\n            this.setState(this.initState);\r\n        }\r\n    }\r\n\r\n    doBasicValidationsAndRereshUI(resp, fileName, startRow) {\r\n        let majorErrors = [];\r\n        let dataProto = this.getDataProto();\r\n        let maxRows = this.props.maxRows;\r\n        let currentRowsLength = resp.rows.length - 1;\r\n        if (maxRows && currentRowsLength > maxRows) {\r\n            majorErrors.push(\r\n                `Total rows in excel (${currentRowsLength}) exceeds maximum allowed ${maxRows} rows`\r\n            );\r\n        }\r\n        let acceptedColumns = dataProto.map((fieldMeta) => fieldMeta.label);\r\n        let requiredFields = dataProto.filter(\r\n            (fieldMeta) => fieldMeta.required\r\n        );\r\n        // console.log('Required fields',requiredFields);\r\n        let columns = resp.rows[0];\r\n        let missingReqFields = requiredFields.filter(\r\n            (fieldMeta) => !columns.includes(fieldMeta.label)\r\n        );\r\n        if (missingReqFields.length > 0) {\r\n            majorErrors.push(\r\n                'Missing column - ' +\r\n                    missingReqFields\r\n                        .map((fieldMeta) => fieldMeta.label)\r\n                        .join(',')\r\n            );\r\n        }\r\n        // console.log('majorErrors',majorErrors);\r\n        let unRecognisedFields = columns.filter(\r\n            (columnTitle) => !acceptedColumns.includes(columnTitle)\r\n        );\r\n        // console.log('unRecognisedFields',unRecognisedFields);\r\n        if (unRecognisedFields.length > 0) {\r\n            majorErrors.push(\r\n                ' Unrecognised column - ' + unRecognisedFields.join(',')\r\n            );\r\n        }\r\n        this.validateDateCols(resp);\r\n        // let erroricDateCols = this.validateDateCols(resp);\r\n        // if(erroricDateCols.length > 0){\r\n        //     majorErrors.push('Invalid date format used for - ' + erroricDateCols.join(',') + ' please use YYYY-MM-DD ')\r\n        // }\r\n        // this.validatePincodeCols(resp);\r\n        // let erroricPincodeCols = this.validatePincodeCols(resp);\r\n        // if(erroricPincodeCols.length > 0){\r\n        //     majorErrors.push(' Invalid used for - ' + erroricPincodeCols.join(','))\r\n        // }\r\n        let uploaded_file_name = localStorage.getItem(\r\n            'RECENT_UPLOADED_SRVC_REQ_EXCEL'\r\n        );\r\n        if (fileName == uploaded_file_name) {\r\n            majorErrors.push(\r\n                'Duplicate excel !! This excel was already uploaded successfully'\r\n            );\r\n        }\r\n        // init state and then set new values to #bug fix\r\n        if (Object.keys(this.majorErroricRowsKeys).length > 0) {\r\n            this.initFileCheckVars(false);\r\n        } else {\r\n            this.initFileCheckVars();\r\n        }\r\n        if (startRow != 1) {\r\n            resp.rows = resp.rows.slice(1);\r\n        }\r\n        // reject();\r\n        // this.setState(this.initState, () => {\r\n        this.setState({\r\n            cols: resp.cols,\r\n            rows: [...this.state.rows, ...resp.rows],\r\n            errorInfile:\r\n                majorErrors.length > 0 ||\r\n                Object.keys(this.majorErroricRowsKeys).length > 0,\r\n            majorErrors: majorErrors,\r\n            readyToUploadRows: [],\r\n            fileCheckingInProg: true,\r\n            bulkUploadProgress: 1,\r\n            fileName: fileName,\r\n        });\r\n    }\r\n\r\n    validatePincodeCols(resp) {\r\n        let erroricColumns = [];\r\n        let columns = resp.rows[0];\r\n        let onlyRows = [...resp.rows];\r\n        delete onlyRows[0];\r\n        onlyRows.map((singleRow, rowNo) => {\r\n            singleRow.map((singleCellValue, index) => {\r\n                let fieldName = columns[index];\r\n                let fieldMeta = this.getSingleColumFieldMeta(fieldName);\r\n                let value = singleCellValue.toString().length;\r\n                if (fieldMeta.key == 'cust_pincode') {\r\n                    // let org_pincode_length = ConfigHelpers.getOrgPincodeLength();\r\n                    if (\r\n                        singleCellValue != '' &&\r\n                        value !=\r\n                            (this.props.orgSettingsData\r\n                                ?.selected_country_pincode_length ?? 6)\r\n                    ) {\r\n                        erroricColumns.push(fieldMeta.label);\r\n                        if (!this.majorErroricRowsKeys[rowNo]) {\r\n                            this.majorErroricRowsKeys[rowNo] = true;\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n        });\r\n        return erroricColumns;\r\n    }\r\n\r\n    validateDateCols(resp) {\r\n        let erroricColumns = [];\r\n        let columns = resp.rows[0];\r\n        let onlyRows = [...resp.rows];\r\n        delete onlyRows[0];\r\n        onlyRows.map((singleRow, rowNo) => {\r\n            singleRow.map((singleCellValue, index) => {\r\n                let fieldName = columns[index];\r\n                let fieldMeta = this.getSingleColumFieldMeta(fieldName);\r\n                if (fieldMeta.widget == 'date-picker') {\r\n                    if (singleCellValue != '') {\r\n                        let value = ExcelDateToJSDate(singleCellValue);\r\n                        if (value) {\r\n                            // value = moment(value);\r\n                            // value = value.format('MMM-DD-YYYY');\r\n                        } else {\r\n                            //\r\n                            erroricColumns.push(fieldMeta.label);\r\n                            if (!this.majorErroricRowsKeys[rowNo]) {\r\n                                this.majorErroricRowsKeys[rowNo] = true;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n        });\r\n        return erroricColumns;\r\n    }\r\n\r\n    getFormDataForRow(rowData) {\r\n        let columns = this.state.rows[0];\r\n        let formDataProtoFrRow = {};\r\n        columns.map((columnTitle, index) => {\r\n            let singleFieldMeta = this.getSingleColumFieldMeta(columnTitle);\r\n            let value = rowData[index];\r\n            if (singleFieldMeta.widget == 'date-picker') {\r\n                // console.log('value',value);\r\n                if (value) {\r\n                    // // this is a date field\r\n                    let valueConverted = ExcelDateToJSDate(value);\r\n                    if (valueConverted) {\r\n                        value = moment(valueConverted);\r\n                        value = value.format('YYYY-MM-DD');\r\n                    } else {\r\n                        value = moment(value);\r\n                    }\r\n                } else {\r\n                    value = undefined;\r\n                }\r\n            } else if (singleFieldMeta.cust_widget === 'TimePicker') {\r\n                console.log(\"TimePicker Value\", value, typeof value);\r\n                if (value) {\r\n                    const convertedValue = this.convertTimePickerValue(value);\r\n                    // If conversion was successful (returned a different value), use it\r\n                    if (convertedValue !== value) {\r\n                        value = convertedValue;\r\n                        console.log(\"TimePicker Value after\", value);\r\n                    } else {\r\n                        // If conversion failed, convert to string as fallback\r\n                        value = '' + value;\r\n                    }\r\n                } else {\r\n                    value = undefined;\r\n                }\r\n            } else if (\r\n                singleFieldMeta.widget != 'select' &&\r\n                singleFieldMeta.widget != 'number' &&\r\n                value\r\n            ) {\r\n                value = '' + value;\r\n            } else if (\r\n                this.props.update &&\r\n                !value &&\r\n                singleFieldMeta.widget != 'select'\r\n            ) {\r\n                value = '';\r\n            } else if (\r\n                this.props.bulkUpdateAuthority &&\r\n                !value &&\r\n                singleFieldMeta.widget == 'select'\r\n            ) {\r\n                value = '';\r\n            }\r\n            if (\r\n                (value || !this.props.bulkUpdateAuthority) &&\r\n                singleFieldMeta.options\r\n            ) {\r\n                value = '' + value;\r\n                // this has options the value should not be out of the set\r\n                if (\r\n                    singleFieldMeta.widgetProps?.mode == 'multiple' &&\r\n                    value?.split(',')?.length >= 1\r\n                ) {\r\n                    // the entry has multiple values\r\n                    let finalValue = [];\r\n                    value.split(',').map((singleValue) => {\r\n                        let matchingEntry = singleFieldMeta.options.filter(\r\n                            (singleOption) =>\r\n                                singleOption.label == singleValue?.trim()\r\n                        );\r\n                        if (matchingEntry.length > 0) {\r\n                            finalValue.push(matchingEntry[0].value);\r\n                        }\r\n                    });\r\n                    if (finalValue.length == 0) {\r\n                        value = undefined;\r\n                    } else {\r\n                        value = finalValue;\r\n                    }\r\n                } else {\r\n                    let matchingEntry;\r\n                    if (this.props.bulkUpdateAuthority) {\r\n                        matchingEntry = singleFieldMeta.options.filter(\r\n                            (singleOption) =>\r\n                                singleOption.label?.trim() == value?.trim()\r\n                        );\r\n                    } else {\r\n                        matchingEntry = singleFieldMeta.options.filter(\r\n                            (singleOption) =>\r\n                                singleOption.label == value?.trim()\r\n                        );\r\n                    }\r\n                    if (matchingEntry.length == 0) {\r\n                        value = undefined;\r\n                    } else {\r\n                        value = matchingEntry[0].value;\r\n                    }\r\n                }\r\n            }\r\n\r\n            formDataProtoFrRow[singleFieldMeta.key] = value;\r\n        });\r\n        formDataProtoFrRow = convertDateFieldsToMoments(\r\n            formDataProtoFrRow,\r\n            this.getDataProto()\r\n        );\r\n        return formDataProtoFrRow;\r\n    }\r\n\r\n    isReadyToUpload() {\r\n        if (this.getData()?.length > 0) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    getDataProto() {\r\n        let { dataProto, demoMode, debugMode, multipleFieldDataModals } =\r\n            this.props;\r\n        if (demoMode && !dataProto) {\r\n            return dataProtoFrBulkUpload;\r\n        }\r\n\r\n        // console.log('dataProto==>', dataProto);\r\n        return dataProto;\r\n    }\r\n\r\n    onStartBulkCreation(event) {\r\n        // if(this.props.demoMode){\r\n        //     this.setState({\r\n        //         bulkUploadInProgress:true,\r\n        //         bulkUploadProgress: 0\r\n        //     },this.dummyProgUpload(0))\r\n        //     return;\r\n        // }\r\n\r\n        // initiate bulk upload\r\n        this.setState({\r\n            bulkUploadInProgress: true,\r\n            bulkUploadProgress: 50,\r\n        });\r\n\r\n        let params = {};\r\n        params['batch_data'] = this.getPostData();\r\n        const onComplete = (resp) => {\r\n            // console.log('Bulk resp',resp);\r\n            this.setState(\r\n                {\r\n                    bulkUploadInProgress: false,\r\n                    bulkUploadProgress: 100,\r\n                    uploadComplete: true,\r\n                    uploadResp: `Uploaded successfully`,\r\n                },\r\n                this.tellParent(resp.data.entry_ids)\r\n            );\r\n            // this.updateClosureToParent();\r\n            LocalStorageManager.setData(\r\n                'RECENT_UPLOADED_SRVC_REQ_EXCEL',\r\n                this.state.fileName\r\n            );\r\n        };\r\n        const onError = (error) => {\r\n            // compare statuses here\r\n            let errorDecoded = http_utils.decodeErrorToMessage(error);\r\n            if (typeof errorDecoded == 'string') {\r\n                errorDecoded = JSON.parse(errorDecoded).hint;\r\n            }\r\n            this.setState({\r\n                bulkUploadInProgress: false,\r\n                bulkUploadProgress: 100,\r\n                uploadComplete: true,\r\n                uploadResp: errorDecoded,\r\n            });\r\n        };\r\n        http_utils.performPostCall(\r\n            this.props.submitUrl,\r\n            params,\r\n            onComplete,\r\n            onError\r\n        );\r\n\r\n        // let chunkSize = this.props.uploadChunkSize || defaultChunkSize;\r\n        // let readyToUploadRows = this.state.readyToUploadRows;\r\n        // let chunksToUpload = _.chunk(readyToUploadRows,chunkSize);\r\n        // console.log('chunks',chunksToUpload)\r\n    }\r\n\r\n    tellParent(entry_ids) {\r\n        if (this.props.onDataModified) {\r\n            this.props.onDataModified(entry_ids);\r\n        }\r\n    }\r\n\r\n    dummyProgUpload(prog) {\r\n        if (prog > 100) {\r\n            this.setState({\r\n                bulkUploadInProgress: false,\r\n                bulkUploadProgress: 100,\r\n                uploadComplete: true,\r\n            });\r\n        } else {\r\n            prog = prog + 10;\r\n            this.setState(\r\n                {\r\n                    bulkUploadInProgress: true,\r\n                    bulkUploadProgress: prog,\r\n                },\r\n                () =>\r\n                    setTimeout((prog) => this.dummyProgUpload(prog), 1000, prog)\r\n            );\r\n        }\r\n    }\r\n\r\n    handleClear() {\r\n        this.initFileCheckVars();\r\n        this.setState(\r\n            {\r\n                uploadComplete: false,\r\n                clearFileInput: true,\r\n            },\r\n            () =>\r\n                this.setState({\r\n                    cols: [],\r\n                    rows: [],\r\n                    readyToUploadRows: [],\r\n                    clearFileInput: false,\r\n                })\r\n        );\r\n    }\r\n\r\n    handleFailedRow(batch_data_row_key) {\r\n        if (!this.erroricRowsKeys[batch_data_row_key]) {\r\n            this.erroricRowsKeys[batch_data_row_key] = true;\r\n            this.erroricRows = this.erroricRows + 1;\r\n        }\r\n\r\n        if (!this.state.errorInfile) {\r\n            this.setState({\r\n                errorInfile: true,\r\n            });\r\n        }\r\n        this.checkIfAllRowsScanningIsDone();\r\n    }\r\n\r\n    handleReadyToUpload(readyToUploadRow, batch_data_row_key) {\r\n        let readyToUploadRowKeys = this.readyToUploadRowKeys;\r\n        // console.log('readyToUploadRowKeys',readyToUploadRowKeys);\r\n        if (!readyToUploadRowKeys[batch_data_row_key]) {\r\n            let readyToUploadRows = this.readyToUploadRows;\r\n            readyToUploadRows.push(readyToUploadRow);\r\n            readyToUploadRowKeys[batch_data_row_key] = true;\r\n            this.readyToUploadRowKeys = readyToUploadRowKeys;\r\n            this.readyToUploadRows = readyToUploadRows;\r\n        }\r\n\r\n        this.checkIfAllRowsScanningIsDone();\r\n    }\r\n\r\n    checkIfAllRowsScanningIsDone() {\r\n        let numOfValidRows = this.readyToUploadRows.length;\r\n        // console.log(\"numOfValidRows\",numOfValidRows);\r\n        let erroricRows = this.erroricRows;\r\n        // console.log(\"erroricRows\",erroricRows);\r\n        let totalRowsScanned = numOfValidRows + erroricRows;\r\n        let totalRows = this.getData().length;\r\n        if (\r\n            totalRowsScanned == totalRows &&\r\n            this.state.readyToUploadRows.length == 0\r\n        ) {\r\n            // this means all processing is done\\\r\n            // console.log('All processing done');\r\n            // lets update the state now\r\n            this.setState({\r\n                erroricRows: erroricRows,\r\n                readyToUploadRowKeys: this.readyToUploadRowKeys,\r\n                readyToUploadRows: this.readyToUploadRows,\r\n                fileCheckingInProg: false,\r\n                currentPage: this.state.isExcelUploaded\r\n                    ? 1\r\n                    : this.state.currentPage,\r\n                bulkUploadProgress: 0,\r\n                isExcelUploaded: false,\r\n            });\r\n        } else if (this.state.fileCheckingInProg) {\r\n            // we will need to see if there is a need to page\r\n            let numberOfPages = Math.ceil(totalRows / 10);\r\n            let currentPage = this.state.currentPage;\r\n            if (\r\n                currentPage <= numberOfPages &&\r\n                totalRowsScanned == currentPage * 10\r\n            ) {\r\n                this.setState({\r\n                    currentPage: currentPage + 1,\r\n                    bulkUploadProgress: Math.round(\r\n                        (currentPage / numberOfPages) * 100\r\n                    ),\r\n                });\r\n                // console.log('We need to change page now');\r\n            }\r\n        }\r\n    }\r\n\r\n    getPostData() {\r\n        return this.state.readyToUploadRows;\r\n    }\r\n\r\n    getPageNoOnTheBaseOfRow(erroricRowsKey) {\r\n        return Math.ceil(erroricRowsKey / pageSize);\r\n    }\r\n    getRowOnTheBaseOfPageSize(erroricRowsKey) {\r\n        if (erroricRowsKey > pageSize) {\r\n            return Math.ceil(erroricRowsKey % pageSize)\r\n                ? Math.ceil(erroricRowsKey % pageSize)\r\n                : pageSize;\r\n        }\r\n        return erroricRowsKey;\r\n    }\r\n    render() {\r\n        let { demoMode, debugMode, maxRows, renderFormsForRows } = this.props;\r\n        maxRows = maxRows || defaultMaxRows;\r\n        const {\r\n            bulkUploadInProgress,\r\n            bulkUploadProgress,\r\n            uploadComplete,\r\n            clearFileInput,\r\n            errorInfile,\r\n            erroricRows,\r\n            majorErrors,\r\n            uploadResp,\r\n            fileCheckingInProg,\r\n        } = this.state;\r\n        const clearInputFileProp = clearFileInput ? { value: '' } : {};\r\n        const readyToUpload = this.isReadyToUpload();\r\n        const dataProto = this.getDataProto();\r\n        return (\r\n            <div>\r\n                {demoMode && (\r\n                    <Alert\r\n                        message=\"Running in demo mode\"\r\n                        type=\"warning\"\r\n                    ></Alert>\r\n                )}\r\n                {debugMode && (\r\n                    <div>\r\n                        <Input\r\n                            placeholder=\"Output JSON will show here\"\r\n                            value={JSON.stringify(this.getPostData())}\r\n                        />\r\n                    </div>\r\n                )}\r\n                {!this.props.update && (\r\n                    <div className=\"\">\r\n                        <h5>\r\n                            Accepted columns\r\n                            <small>( * are mandatory )</small>\r\n                            <ExcelFile\r\n                                filename={\r\n                                    this.props.update\r\n                                        ? 'Bulk_update_template'\r\n                                        : 'Bulk_upload_template'\r\n                                }\r\n                                element={\r\n                                    <Button\r\n                                        icon={<DownloadOutlined />}\r\n                                        className=\"gx-mb-0\"\r\n                                        type=\"link\"\r\n                                    >\r\n                                        Template\r\n                                    </Button>\r\n                                }\r\n                            >\r\n                                <ExcelSheet data={[]} name=\"Sheet 1\">\r\n                                    {dataProto\r\n                                        .filter((fieldMeta) => fieldMeta.label)\r\n                                        .map((fieldMeta) => (\r\n                                            <ExcelColumn\r\n                                                label={fieldMeta.label}\r\n                                                value={fieldMeta.label}\r\n                                                style={{\r\n                                                    fill: {\r\n                                                        patternType: 'solid',\r\n                                                        fgColor: {\r\n                                                            rgb: 'FFFF0000',\r\n                                                        },\r\n                                                    },\r\n                                                }}\r\n                                            />\r\n                                        ))}\r\n                                </ExcelSheet>\r\n                            </ExcelFile>\r\n                        </h5>\r\n                        <div\r\n                            style={{\r\n                                overflowX: 'auto',\r\n                            }}\r\n                        >\r\n                            <table class=\"gx-w-100\">\r\n                                <tbody>\r\n                                    <tr>\r\n                                        {dataProto.map(\r\n                                            (singleFieldMeta, index) =>\r\n                                                singleFieldMeta.label && (\r\n                                                    <td\r\n                                                        key={index}\r\n                                                        className={\r\n                                                            singleFieldMeta.required\r\n                                                                ? 'gx-bg-amber-light'\r\n                                                                : ''\r\n                                                        }\r\n                                                        style={{\r\n                                                            border: '2px solid #dddddd',\r\n                                                            textAlign: 'center',\r\n                                                            padding: '8px',\r\n                                                            fontSize: '0.8rem',\r\n                                                        }}\r\n                                                    >\r\n                                                        {singleFieldMeta.label}\r\n                                                        {singleFieldMeta.options && (\r\n                                                            <td className=\"gx-d-flex gx-justify-content-center gx-align-items-center\">\r\n                                                                <Button\r\n                                                                    type=\"link\"\r\n                                                                    onClick={() =>\r\n                                                                        this.showSelectFieldOptionsModal(\r\n                                                                            singleFieldMeta.key\r\n                                                                        )\r\n                                                                    }\r\n                                                                    size=\"small\"\r\n                                                                >\r\n                                                                    View Data\r\n                                                                </Button>\r\n                                                                <Modal\r\n                                                                    title={`${singleFieldMeta.label} list`}\r\n                                                                    visible={\r\n                                                                        this\r\n                                                                            .state\r\n                                                                            .multipleFieldDataModals[\r\n                                                                            singleFieldMeta\r\n                                                                                .key\r\n                                                                        ]\r\n                                                                    }\r\n                                                                    footer={\r\n                                                                        null\r\n                                                                    }\r\n                                                                    onCancel={() =>\r\n                                                                        this.hideSelectFieldOptionsModal(\r\n                                                                            singleFieldMeta.key\r\n                                                                        )\r\n                                                                    }\r\n                                                                >\r\n                                                                    <p>\r\n                                                                        {/* Map over the `options` array from `singleFieldMeta` */}\r\n                                                                        {singleFieldMeta.options.map(\r\n                                                                            (\r\n                                                                                option,\r\n                                                                                index\r\n                                                                            ) => (\r\n                                                                                <Paragraph\r\n                                                                                    className=\"gx-mb-1\"\r\n                                                                                    key={\r\n                                                                                        index\r\n                                                                                    }\r\n                                                                                    copyable={{\r\n                                                                                        text: `${option?.label}`, // Text to be copied\r\n                                                                                    }}\r\n                                                                                >\r\n                                                                                    {/* Display the option number (1-based index) */}\r\n                                                                                    {index +\r\n                                                                                        1}\r\n\r\n                                                                                    .{' '}\r\n                                                                                    {/* Display the label of the current option */}\r\n                                                                                    {\r\n                                                                                        option?.label\r\n                                                                                    }\r\n                                                                                </Paragraph>\r\n                                                                            )\r\n                                                                        )}\r\n                                                                    </p>\r\n                                                                </Modal>\r\n                                                            </td>\r\n                                                        )}\r\n                                                    </td>\r\n                                                )\r\n                                        )}\r\n                                    </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                        <hr></hr>\r\n                    </div>\r\n                )}\r\n                <div className=\"gx-mt-3\">\r\n                    <h3>Upload an excel</h3>\r\n                    <div className=\"gx-d-flex\">\r\n                        <div style={{ width: '30px' }}>\r\n                            <FileIcon\r\n                                extension=\".xlsx\"\r\n                                {...defaultStyles['xlsx']}\r\n                            />\r\n                        </div>\r\n                        <input\r\n                            type=\"file\"\r\n                            {...clearInputFileProp}\r\n                            onClick={(e) =>\r\n                                this.setState({ fileCheckingInProg: true })\r\n                            }\r\n                            disabled={bulkUploadInProgress || uploadComplete}\r\n                            onChange={(e) => {\r\n                                this.handleFileChange(e);\r\n                            }}\r\n                            style={{ padding: '10px' }}\r\n                            data-testid=\"upload-excel-file\"\r\n                        />\r\n                    </div>\r\n\r\n                    {bulkUploadProgress > 0 && bulkUploadProgress < 100 && (\r\n                        <>\r\n                            <Spin></Spin>\r\n                            <br></br>\r\n                            Checking file..\r\n                            <Progress\r\n                                // type=\"circle\"\r\n                                className=\"gx-ml-2\"\r\n                                percent={bulkUploadProgress}\r\n                            />\r\n                        </>\r\n                    )}\r\n                    {maxRows > 0 && !this.props.update && (\r\n                        <p>**Max {maxRows} rows supported at a time</p>\r\n                    )}\r\n                    {!this.props.update && (\r\n                        <div>\r\n                            <p>\r\n                                **If a column has data condition and if data is\r\n                                out of possible values then it will be treated\r\n                                as empty{' '}\r\n                            </p>\r\n                            <p className=\"gx-text-orange\">\r\n                                **Date format should be YYYY-MM-DD{' '}\r\n                            </p>\r\n                        </div>\r\n                    )}\r\n                    {this.props.timeFormatMsg && !this.props.update && (\r\n                        <p className=\"gx-text-red\">\r\n                            **Time format should be HH:MM AM/PM (Eg: 09:30 AM, 2:30 PM){' '}\r\n                        </p>\r\n                    )}\r\n                    {readyToUpload && (\r\n                        <>\r\n                            {majorErrors.length > 0 && (\r\n                                <>\r\n                                    {majorErrors.map((singleLine, index) => (\r\n                                        <p className=\"gx-text-red\" key={index}>\r\n                                            {singleLine}\r\n                                        </p>\r\n                                    ))}\r\n                                </>\r\n                            )}\r\n                            {Object.keys(this.majorErroricRowsKeys).length >\r\n                                0 && (\r\n                                <>\r\n                                    {Object.keys(this.majorErroricRowsKeys).map(\r\n                                        (majorErroricRowsKey) => (\r\n                                            <p className=\"gx-text-red\">\r\n                                                error on page{' '}\r\n                                                {this.getPageNoOnTheBaseOfRow(\r\n                                                    majorErroricRowsKey\r\n                                                )}{' '}\r\n                                                in row{' '}\r\n                                                {this.getRowOnTheBaseOfPageSize(\r\n                                                    majorErroricRowsKey\r\n                                                )}\r\n                                            </p>\r\n                                        )\r\n                                    )}\r\n                                </>\r\n                            )}\r\n                            {errorInfile && erroricRows > 0 && (\r\n                                <>\r\n                                    {Object.keys(this.erroricRowsKeys).map(\r\n                                        (singleErroricRowsKey) => (\r\n                                            <p className=\"gx-text-red\">\r\n                                                error on page{' '}\r\n                                                {this.getPageNoOnTheBaseOfRow(\r\n                                                    singleErroricRowsKey\r\n                                                )}{' '}\r\n                                                in row{' '}\r\n                                                {this.getRowOnTheBaseOfPageSize(\r\n                                                    singleErroricRowsKey\r\n                                                )}\r\n                                            </p>\r\n                                        )\r\n                                    )}\r\n                                </>\r\n                            )}\r\n                            <Button\r\n                                type=\"primary\"\r\n                                disabled={\r\n                                    !readyToUpload ||\r\n                                    bulkUploadInProgress ||\r\n                                    uploadComplete ||\r\n                                    errorInfile ||\r\n                                    fileCheckingInProg\r\n                                }\r\n                                onClick={(e) => {\r\n                                    this.onStartBulkCreation(e);\r\n                                }}\r\n                            >\r\n                                {this.props.update\r\n                                    ? 'Start bulk updation'\r\n                                    : 'Start bulk creation'}\r\n                            </Button>\r\n                            {\r\n                                // uploadComplete &&\r\n                                <>\r\n                                    <Button\r\n                                        onClick={(e) => this.handleClear()}\r\n                                        type=\"link\"\r\n                                    >\r\n                                        Reset/Clear\r\n                                    </Button>\r\n                                    <br></br>\r\n                                    {uploadComplete && (\r\n                                        <Alert\r\n                                            type=\"info\"\r\n                                            message={\r\n                                                <div>\r\n                                                    <h2>Bulk upload results</h2>\r\n                                                    <br></br>\r\n                                                    <p\r\n                                                        style={\r\n                                                            this.props\r\n                                                                .errorHasLineBreaks\r\n                                                                ? {\r\n                                                                      whiteSpace:\r\n                                                                          'pre-wrap',\r\n                                                                  }\r\n                                                                : {}\r\n                                                        }\r\n                                                    >\r\n                                                        {uploadResp}\r\n                                                    </p>\r\n                                                </div>\r\n                                            }\r\n                                        />\r\n                                    )}\r\n                                </>\r\n                            }\r\n                            {bulkUploadInProgress && (\r\n                                <>\r\n                                    <Spin></Spin>\r\n                                    <br></br>\r\n                                    Upload in progress..\r\n                                </>\r\n                            )}\r\n\r\n                            {renderFormsForRows ? (\r\n                                <List\r\n                                    itemLayout=\"itemLayout\"\r\n                                    size=\"large\"\r\n                                    bordered\r\n                                    pagination={{\r\n                                        onChange: (page) => {\r\n                                            console.log(page);\r\n                                            this.setState({\r\n                                                currentPage: page,\r\n                                            });\r\n                                        },\r\n                                        pageSize: 10,\r\n                                        current: this.state.currentPage,\r\n                                    }}\r\n                                    dataSource={this.getData()}\r\n                                    renderItem={(item) => (\r\n                                        <List.Item\r\n                                            key={item.batch_data_row_key}\r\n                                        >\r\n                                            <SingleRowFormFrBuilder\r\n                                                meta={this.getMeta()}\r\n                                                renderFullForm\r\n                                                initialValues={this.getFormDataForRow(\r\n                                                    item\r\n                                                )}\r\n                                                onValidationError={(e) =>\r\n                                                    this.handleFailedRow(\r\n                                                        item.batch_data_row_key\r\n                                                    )\r\n                                                }\r\n                                                onReadyToUpload={(formData) =>\r\n                                                    this.handleReadyToUpload(\r\n                                                        formData\r\n                                                    )\r\n                                                }\r\n                                            />\r\n                                        </List.Item>\r\n                                    )}\r\n                                />\r\n                            ) : (\r\n                                <Table\r\n                                    className=\"gx-table-responsive\"\r\n                                    bordered\r\n                                    size=\"small\"\r\n                                    pagination={{\r\n                                        onChange: (page) => {\r\n                                            console.log(page);\r\n                                            this.setState({\r\n                                                currentPage: page,\r\n                                            });\r\n                                        },\r\n                                        pageSize: 10,\r\n                                        current: this.state.currentPage,\r\n                                    }}\r\n                                    columns={this.getColumns()}\r\n                                    dataSource={this.getData()}\r\n                                    // dataSource={[]}\r\n                                />\r\n                            )}\r\n                        </>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n}\r\n\r\nexport default BulkUploader;\r\n"], "mappings": ";AAAA,SACIA,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,UAAU,QACP,MAAM;AACb,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,SAASC,QAAQ,EAAEC,aAAa,QAAQ,sBAAsB;AAC9D,OAAOC,KAAK,IAAIC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7D,SACIC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,iBAAiB,QACd,oBAAoB;AAC3B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,aAAa,EAAEC,QAAQ,QAAQ,iBAAiB;AACzD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,qBAAqB;AAE/C,MAAM;EAAEC;AAAU,CAAC,GAAGrB,UAAU;AAEhC,MAAMsB,SAAS,GAAGN,WAAW,CAACM,SAAS;AACvC,MAAMC,UAAU,GAAGP,WAAW,CAACM,SAAS,CAACC,UAAU;AACnD,MAAMC,WAAW,GAAGR,WAAW,CAACM,SAAS,CAACE,WAAW;AACrD,MAAMC,QAAQ,GAAG,EAAE;AACnB;AACA,MAAMC,cAAc,GAAG,IAAI;AAC3B,MAAMC,qBAAqB,GAAG,CAC1B;EACIC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,IAAI;EACdC,GAAG,EAAE;AACT,CAAC,EACD;EACIA,GAAG,EAAE,aAAa;EAClBF,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,IAAI;EACdE,KAAK,EAAE,CACH;IACIC,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU,CAAC;IAC/BxC,OAAO,EAAE;EACb,CAAC,EACD;IAAEyC,GAAG,EAAE;EAAG,CAAC,EACX;IAAEC,GAAG,EAAE;EAAG,CAAC;AAEnB,CAAC,EACD;EACIP,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,IAAI;EACdC,GAAG,EAAE,aAAa;EAClBM,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,CACL;IACIT,KAAK,EAAE,MAAM;IACbU,KAAK,EAAE;EACX,CAAC,EACD;IACIV,KAAK,EAAE,QAAQ;IACfU,KAAK,EAAE;EACX,CAAC;AAET,CAAC,EACD;EACIV,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,KAAK;EACfC,GAAG,EAAE,aAAa;EAClBM,MAAM,EAAE;AACZ,CAAC,EACD;EACIR,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,KAAK;EACfC,GAAG,EAAE,UAAU;EACfM,MAAM,EAAE;AACZ,CAAC,CACJ;AAED,MAAMG,sBAAsB,GAAIC,KAAK,IAAK;EACtC;EACA,MAAM;IACFC,IAAI;IACJC,aAAa;IACbC,iBAAiB;IACjBC,eAAe;IACfC;EACJ,CAAC,GAAGL,KAAK;EACT,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAACyC,SAAS,CAAC;EAC7C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC0C,OAAO,EAAE;IACVC,UAAU,CAAC,IAAI,CAAC;IAChBC,UAAU,CAAC,YAAY;MACnB,MAAMC,QAAQ,GAAG,MAAMhC,aAAa,CAChCqB,IAAI,CAACY,MAAM,EACXX,aAAa,EACbF,KAAK,CAACc,gBACV,CAAC;MACD,IAAIF,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QAAA,IAAAC,UAAA;QACrB,MAAMC,WAAW,GAAGL,QAAQ,aAARA,QAAQ,wBAAAI,UAAA,GAARJ,QAAQ,CAAG,CAAC,CAAC,cAAAI,UAAA,uBAAbA,UAAA,CAAeE,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;QACnDZ,QAAQ,CAACU,WAAW,IAAI,OAAO,CAAC;QAChCd,iBAAiB,CAACS,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACHL,QAAQ,CAAC,SAAS,CAAC;QACnBH,eAAe,CAACF,aAAa,CAAC;MAClC;IACJ,CAAC,EAAE,EAAE,CAAC;EACV;EAEA,OAAO,CAACG,cAAc,IAAIC,KAAK,GAC3BA,KAAK,IAAI,SAAS,gBACd1C,KAAA,CAAAwD,aAAA,CAAC7D,GAAG;IAAC8D,KAAK,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAU,CAAC;EAAA;EAEhC;EACA/D,KAAA,CAAAwD,aAAA;IAAMQ,SAAS,EAAC,aAAa;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAErB,KAAY,CAC9C;EAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA1C,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,MAAI,CACP;AACL,CAAC;AAED,MAAMC,YAAY,SAASjE,SAAS,CAAC;EAkCjCkE,iBAAiBA,CAACC,yBAAyB,GAAG,IAAI,EAAE;IAChD,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAIJ,yBAAyB,EAAE;MAC3B,IAAI,CAACK,oBAAoB,GAAG,CAAC,CAAC;IAClC;EACJ;EAEAC,WAAWA,CAACtC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IAAA,KA7CJuC,SAAS,GAAG;MACRC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,kBAAkB,EAAE,CAAC;MACrBC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,KAAK;MACrBC,WAAW,EAAE,KAAK;MAClBX,WAAW,EAAE,CAAC;MACdY,WAAW,EAAE,EAAE;MACfb,iBAAiB,EAAE,EAAE;MACrBD,oBAAoB,EAAE,EAAE;MACxBe,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,KAAK;MACzBC,WAAW,EAAE,CAAC;MACd1B,QAAQ,EAAE,EAAE;MACZ2B,eAAe,EAAE,KAAK;MACtBC,uBAAuB,EAAE,CAAC;IAC9B,CAAC;IAAA,KAEDC,KAAK,GAAG,IAAI,CAACd,SAAS;IAAA,KAEtBe,2BAA2B,GAAIhE,GAAG,IAAK;MACnC,IAAI8D,uBAAuB,GAAG,IAAI,CAACC,KAAK,CAACD,uBAAuB;MAChEA,uBAAuB,CAAC9D,GAAG,CAAC,GAAG,IAAI;MACnC,IAAI,CAACiE,QAAQ,CAAC;QAAEH;MAAwB,CAAC,CAAC;IAC9C,CAAC;IAAA,KACDI,2BAA2B,GAAIlE,GAAG,IAAK;MACnC,IAAI8D,uBAAuB,GAAG,IAAI,CAACC,KAAK,CAACD,uBAAuB;MAChEA,uBAAuB,CAAC9D,GAAG,CAAC,GAAG,KAAK;MACpC,IAAI,CAACiE,QAAQ,CAAC;QAAEH;MAAwB,CAAC,CAAC;IAC9C,CAAC;IAeG,IAAI,CAACrB,iBAAiB,CAAC,CAAC;EAC5B;EAEA0B,OAAOA,CAAA,EAAG;IACN,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnCD,SAAS,CAACE,GAAG,CAAEC,eAAe,IAAK;MAC/B,IAAIA,eAAe,CAAC1E,KAAK,EAAE;QACvB0E,eAAe,CAACC,OAAO,GAAG,CAAC;QAC3BD,eAAe,CAACE,UAAU,GAAG,mBAAMpG,KAAA,CAAAwD,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAU,CAAC;QAC9C+B,WAAW,CAACO,IAAI,CAACH,eAAe,CAAC;MACrC;IACJ,CAAC,CAAC;IACF,MAAM7D,IAAI,GAAG;MACTiE,OAAO,EAAE,IAAI,CAACb,KAAK,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC1B,MAAM;MAClCoD,cAAc,EAAE,IAAI;MACpBtD,MAAM,EAAE6C;IACZ,CAAC;IACD,OAAOzD,IAAI;EACf;EAEAmE,uBAAuBA,CAACC,UAAU,EAAE;IAChC,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIX,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnCD,SAAS,CAACE,GAAG,CAAEC,eAAe,IAAK;MAC/B,IAAIA,eAAe,CAAC1E,KAAK,IAAIiF,UAAU,EAAE;QACrCC,UAAU,GAAGR,eAAe;MAChC;IACJ,CAAC,CAAC;IACF,OAAOQ,UAAU;EACrB;;EAEA;EACAC,sBAAsBA,CAACzE,KAAK,EAAE;IAC1B,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAIU,SAAS,EAAE;MACpD,IAAI,OAAOV,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACrD;QACA,MAAM0E,YAAY,GAAG1E,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAChD,OAAO3B,MAAM,CAACsG,GAAG,CAACD,YAAY,CAAC,CAACE,MAAM,CAAC,SAAS,CAAC;MACrD,CAAC,MAAM,IAAI,OAAO5E,KAAK,KAAK,QAAQ,EAAE;QAClC;QACA,MAAM6E,MAAM,GAAGxG,MAAM,CAAC2B,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC;QACpH,IAAI6E,MAAM,CAACC,OAAO,CAAC,CAAC,EAAE;UAClB,OAAOD,MAAM,CAACD,MAAM,CAAC,SAAS,CAAC;QACnC;MACJ;IACJ;IACA,OAAO5E,KAAK;EAChB;EAEA+E,UAAUA,CAACC,WAAW,EAAE;IACpB;IACA,IAAIC,QAAQ,GAAGD,WAAW,IAAI,IAAI,CAACzB,KAAK,CAACZ,IAAI,CAAC,CAAC,CAAC;IAChD,IAAIyB,OAAO,GAAG,EAAE;IAChB,IAAIa,QAAQ,EAAE;MACVb,OAAO,CAACD,IAAI,CAAC;QACTe,KAAK,EAAE,cAAc;QACrBC,SAAS,EAAE,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAC1C5F,GAAG,EAAE,IAAI,CAAC4F,wBAAwB,CAAC,CAAC;QACpCC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;UACtB,oBACIzH,KAAA,CAAAwD,aAAA,CAACrB,sBAAsB;YACnBT,GAAG,EAAE+F,MAAM,CAACC,kBAAmB;YAC/BrF,IAAI,EAAE,IAAI,CAACwD,OAAO,CAAC,CAAE;YACrBpD,cAAc,EAAE,IAAI,CAACL,KAAK,CAACuF,kBAAmB;YAC9CrF,aAAa,EAAE,IAAI,CAACsF,iBAAiB,CAACH,MAAM,CAAE;YAC9ClF,iBAAiB,EAAGsF,CAAC,IACjB,IAAI,CAACC,eAAe,CAACL,MAAM,CAACC,kBAAkB,CACjD;YACDlF,eAAe,EAAGuF,QAAQ,IACtB,IAAI,CAACC,mBAAmB,CACpBD,QAAQ,EACRN,MAAM,CAACC,kBACX,CACH;YACDxE,gBAAgB,EAAE,IAAI,CAACd,KAAK,CAACc,gBAAiB;YAAAQ,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACjD,CAAC;QAEV;MACJ,CAAC,CAAC;MACF,CAAC,IAAI,CAAC3B,KAAK,CAACuF,kBAAkB,IAC1BR,QAAQ,CAAClB,GAAG,CAAC,CAACgC,eAAe,EAAEC,KAAK,KAAK;QACrC,IAAIC,SAAS,GACT,IAAI,CAAC3B,uBAAuB,CAACyB,eAAe,CAAC;QACjD,IAAIG,gBAAgB,GAAG;UACnBhB,KAAK,EAAEa,eAAe;UACtBZ,SAAS,EAAEa,KAAK;UAChBxG,GAAG,EAAEwG,KAAK;UACVlE,SAAS,EAAEmE,SAAS,CAAC1G,QAAQ,GACvB,mBAAmB,GACnB;QACV,CAAC;QACD,IAAI0G,SAAS,CAACnG,MAAM,IAAI,aAAa,EAAE;UACnCoG,gBAAgB,CAACb,MAAM,GAAG,CAACC,IAAI,EAAEC,MAAM,KAAK;YACxC;YACA,IAAID,IAAI,IAAI,EAAE,EAAE;cACZ,IAAItF,KAAK,GAAG5B,iBAAiB,CAACkH,IAAI,CAAC;cACnC,IAAItF,KAAK,EAAE;gBACPA,KAAK,GAAG3B,MAAM,CAAC2B,KAAK,CAAC;gBACrBA,KAAK,GAAGA,KAAK,CAAC4E,MAAM,CAAC,aAAa,CAAC;cACvC,CAAC,MAAM;gBACH5E,KAAK,GAAG3B,MAAM,CAAC2B,KAAK,CAAC;cACzB;cACA,OAAOA,KAAK;YAChB;YACA,OAAOsF,IAAI;UACf,CAAC;QACL,CAAC,MAAM,IAAIW,SAAS,CAACE,WAAW,KAAK,YAAY,EAAE;UAC/CD,gBAAgB,CAACb,MAAM,GAAG,CAACC,IAAI,EAAEC,MAAM,KAAK;YACxC,OAAO,IAAI,CAACd,sBAAsB,CAACa,IAAI,CAAC;UAC5C,CAAC;QACL;QAEAlB,OAAO,CAACD,IAAI,CAAC+B,gBAAgB,CAAC;MAClC,CAAC,CAAC;IACV;IACA,OAAO9B,OAAO;EAClB;EAEAgB,wBAAwBA,CAAA,EAAG;IACvB;IACA,OAAO,CAAC;EACZ;EAEAgB,OAAOA,CAACC,WAAW,GAAG3F,SAAS,EAAE;IAC7B,IAAIiC,IAAI,GAAG0D,WAAW,IAAI,IAAI,CAAC9C,KAAK,CAACZ,IAAI;IACzC,IAAI2D,SAAS,GAAG,EAAE;IAClB3D,IAAI,CAACoB,GAAG,CAAC,CAACwC,SAAS,EAAEP,KAAK,KAAK;MAC3B;MACA,IAAIA,KAAK,GAAG,CAAC,EAAE;QACXO,SAAS,CAACf,kBAAkB,GAAGQ,KAAK;QACpCM,SAAS,CAACnC,IAAI,CAACoC,SAAS,CAAC;MAC7B;IACJ,CAAC,CAAC;IACF;IACA,OAAOD,SAAS;EACpB;EAEA,MAAME,gBAAgBA,CAACC,KAAK,EAAE;IAC1B,IAAIC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACnC,IAAI;MACA,MAAMzJ,OAAO,CAAC0J,OAAO,CAAC,kBAAkB,CAAC;MAEzC,MAAMC,SAAS,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACrDpJ,aAAa,CAAC6I,OAAO,EAAE,CAACQ,GAAG,EAAEC,IAAI,KAAK;UAClC,IAAID,GAAG,EAAE;YACLD,MAAM,CAAC,0BAA0B,CAAC;UACtC,CAAC,MAAM;YACHD,OAAO,CAACG,IAAI,CAAC;UACjB;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGN,SAAS,CAACnE,IAAI,CAAC,CAAC,CAAC;MAChC,MAAM0E,UAAU,GAAG,GAAG;MACtB,MAAMC,SAAS,GAAGR,SAAS,CAACnE,IAAI,CAAC1B,MAAM;MACvC,IAAIsG,QAAQ,GAAG,CAAC;;MAEhB;MACA,OAAOA,QAAQ,GAAGD,SAAS,EAAE;QACzB,MAAME,SAAS,GAAGV,SAAS,CAACnE,IAAI,CAAC8E,KAAK,CAClCF,QAAQ,EACRA,QAAQ,GAAGF,UACf,CAAC;QACD,MAAMK,eAAe,GAAG,CAACN,MAAM,EAAE,GAAGI,SAAS,CAAC;QAE9C,MAAMG,aAAa,GAAGD,eAAe,CAACE,MAAM,CAAErB,SAAS,IAAK;UACxD,IAAIA,SAAS,CAACtF,MAAM,GAAG,CAAC,EAAE;YACtBsF,SAAS,GAAGA,SAAS,CAACqB,MAAM,CACvBC,OAAO,IAAKA,OAAO,IAAIA,OAAO,KAAK,EACxC,CAAC;UACL;UACA,OAAOtB,SAAS,CAACtF,MAAM,GAAG,CAAC;QAC/B,CAAC,CAAC;QACF,IAAI,CAAC6G,6BAA6B,CAC9B;UAAEnF,IAAI,EAAEgF,aAAa;UAAEjF,IAAI,EAAEoE,SAAS,CAACpE;QAAK,CAAC,EAC7CgE,OAAO,CAACqB,IAAI,EACZR,QACJ,CAAC;QAEDA,QAAQ,IAAIF,UAAU;MAC1B;MACA,IAAI,CAAC5D,QAAQ,CAAC;QAAEJ,eAAe,EAAE;MAAK,CAAC,CAAC;MACxClG,OAAO,CAAC6K,OAAO,CAAC,4BAA4B,CAAC;IACjD,CAAC,CAAC,OAAOrC,CAAC,EAAE;MACRxI,OAAO,CAACqD,KAAK,CAACmF,CAAC,IAAI,0BAA0B,CAAC;MAC9C,IAAI,CAAC1D,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACwB,QAAQ,CAAC,IAAI,CAAChB,SAAS,CAAC;IACjC;EACJ;EAEAqF,6BAA6BA,CAACX,IAAI,EAAEzF,QAAQ,EAAE6F,QAAQ,EAAE;IACpD,IAAItE,WAAW,GAAG,EAAE;IACpB,IAAIY,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnC,IAAImE,OAAO,GAAG,IAAI,CAAC/H,KAAK,CAAC+H,OAAO;IAChC,IAAIC,iBAAiB,GAAGf,IAAI,CAACxE,IAAI,CAAC1B,MAAM,GAAG,CAAC;IAC5C,IAAIgH,OAAO,IAAIC,iBAAiB,GAAGD,OAAO,EAAE;MACxChF,WAAW,CAACkB,IAAI,CACZ,wBAAwB+D,iBAAiB,6BAA6BD,OAAO,OACjF,CAAC;IACL;IACA,IAAIE,eAAe,GAAGtE,SAAS,CAACE,GAAG,CAAEkC,SAAS,IAAKA,SAAS,CAAC3G,KAAK,CAAC;IACnE,IAAI8I,cAAc,GAAGvE,SAAS,CAAC+D,MAAM,CAChC3B,SAAS,IAAKA,SAAS,CAAC1G,QAC7B,CAAC;IACD;IACA,IAAI6E,OAAO,GAAG+C,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAI0F,gBAAgB,GAAGD,cAAc,CAACR,MAAM,CACvC3B,SAAS,IAAK,CAAC7B,OAAO,CAACkE,QAAQ,CAACrC,SAAS,CAAC3G,KAAK,CACpD,CAAC;IACD,IAAI+I,gBAAgB,CAACpH,MAAM,GAAG,CAAC,EAAE;MAC7BgC,WAAW,CAACkB,IAAI,CACZ,mBAAmB,GACfkE,gBAAgB,CACXtE,GAAG,CAAEkC,SAAS,IAAKA,SAAS,CAAC3G,KAAK,CAAC,CACnC+B,IAAI,CAAC,GAAG,CACrB,CAAC;IACL;IACA;IACA,IAAIkH,kBAAkB,GAAGnE,OAAO,CAACwD,MAAM,CAClCY,WAAW,IAAK,CAACL,eAAe,CAACG,QAAQ,CAACE,WAAW,CAC1D,CAAC;IACD;IACA,IAAID,kBAAkB,CAACtH,MAAM,GAAG,CAAC,EAAE;MAC/BgC,WAAW,CAACkB,IAAI,CACZ,yBAAyB,GAAGoE,kBAAkB,CAAClH,IAAI,CAAC,GAAG,CAC3D,CAAC;IACL;IACA,IAAI,CAACoH,gBAAgB,CAACtB,IAAI,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIuB,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CACzC,gCACJ,CAAC;IACD,IAAIlH,QAAQ,IAAIgH,kBAAkB,EAAE;MAChCzF,WAAW,CAACkB,IAAI,CACZ,iEACJ,CAAC;IACL;IACA;IACA,IAAI0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,oBAAoB,CAAC,CAACtB,MAAM,GAAG,CAAC,EAAE;MACnD,IAAI,CAACgB,iBAAiB,CAAC,KAAK,CAAC;IACjC,CAAC,MAAM;MACH,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAIsF,QAAQ,IAAI,CAAC,EAAE;MACfJ,IAAI,CAACxE,IAAI,GAAGwE,IAAI,CAACxE,IAAI,CAAC8E,KAAK,CAAC,CAAC,CAAC;IAClC;IACA;IACA;IACA,IAAI,CAAChE,QAAQ,CAAC;MACVf,IAAI,EAAEyE,IAAI,CAACzE,IAAI;MACfC,IAAI,EAAE,CAAC,GAAG,IAAI,CAACY,KAAK,CAACZ,IAAI,EAAE,GAAGwE,IAAI,CAACxE,IAAI,CAAC;MACxCK,WAAW,EACPC,WAAW,CAAChC,MAAM,GAAG,CAAC,IACtB4H,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,oBAAoB,CAAC,CAACtB,MAAM,GAAG,CAAC;MACrDgC,WAAW,EAAEA,WAAW;MACxBb,iBAAiB,EAAE,EAAE;MACrBe,kBAAkB,EAAE,IAAI;MACxBN,kBAAkB,EAAE,CAAC;MACrBnB,QAAQ,EAAEA;IACd,CAAC,CAAC;EACN;EAEAqH,mBAAmBA,CAAC5B,IAAI,EAAE;IACtB,IAAI6B,cAAc,GAAG,EAAE;IACvB,IAAI5E,OAAO,GAAG+C,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAIsG,QAAQ,GAAG,CAAC,GAAG9B,IAAI,CAACxE,IAAI,CAAC;IAC7B,OAAOsG,QAAQ,CAAC,CAAC,CAAC;IAClBA,QAAQ,CAAClF,GAAG,CAAC,CAACwC,SAAS,EAAE2C,KAAK,KAAK;MAC/B3C,SAAS,CAACxC,GAAG,CAAC,CAACgC,eAAe,EAAEC,KAAK,KAAK;QACtC,IAAImD,SAAS,GAAG/E,OAAO,CAAC4B,KAAK,CAAC;QAC9B,IAAIC,SAAS,GAAG,IAAI,CAAC3B,uBAAuB,CAAC6E,SAAS,CAAC;QACvD,IAAInJ,KAAK,GAAG+F,eAAe,CAACqD,QAAQ,CAAC,CAAC,CAACnI,MAAM;QAC7C,IAAIgF,SAAS,CAACzG,GAAG,IAAI,cAAc,EAAE;UAAA,IAAA6J,qBAAA,EAAAC,sBAAA;UACjC;UACA,IACIvD,eAAe,IAAI,EAAE,IACrB/F,KAAK,MAAAqJ,qBAAA,IAAAC,sBAAA,GACA,IAAI,CAACpJ,KAAK,CAACqJ,eAAe,cAAAD,sBAAA,uBAA1BA,sBAAA,CACKE,+BAA+B,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC,EACjD;YACEL,cAAc,CAAC7E,IAAI,CAAC8B,SAAS,CAAC3G,KAAK,CAAC;YACpC,IAAI,CAAC,IAAI,CAACiD,oBAAoB,CAAC2G,KAAK,CAAC,EAAE;cACnC,IAAI,CAAC3G,oBAAoB,CAAC2G,KAAK,CAAC,GAAG,IAAI;YAC3C;UACJ;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EAEAP,gBAAgBA,CAACtB,IAAI,EAAE;IACnB,IAAI6B,cAAc,GAAG,EAAE;IACvB,IAAI5E,OAAO,GAAG+C,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAIsG,QAAQ,GAAG,CAAC,GAAG9B,IAAI,CAACxE,IAAI,CAAC;IAC7B,OAAOsG,QAAQ,CAAC,CAAC,CAAC;IAClBA,QAAQ,CAAClF,GAAG,CAAC,CAACwC,SAAS,EAAE2C,KAAK,KAAK;MAC/B3C,SAAS,CAACxC,GAAG,CAAC,CAACgC,eAAe,EAAEC,KAAK,KAAK;QACtC,IAAImD,SAAS,GAAG/E,OAAO,CAAC4B,KAAK,CAAC;QAC9B,IAAIC,SAAS,GAAG,IAAI,CAAC3B,uBAAuB,CAAC6E,SAAS,CAAC;QACvD,IAAIlD,SAAS,CAACnG,MAAM,IAAI,aAAa,EAAE;UACnC,IAAIiG,eAAe,IAAI,EAAE,EAAE;YACvB,IAAI/F,KAAK,GAAG5B,iBAAiB,CAAC2H,eAAe,CAAC;YAC9C,IAAI/F,KAAK,EAAE;cACP;cACA;YAAA,CACH,MAAM;cACH;cACAgJ,cAAc,CAAC7E,IAAI,CAAC8B,SAAS,CAAC3G,KAAK,CAAC;cACpC,IAAI,CAAC,IAAI,CAACiD,oBAAoB,CAAC2G,KAAK,CAAC,EAAE;gBACnC,IAAI,CAAC3G,oBAAoB,CAAC2G,KAAK,CAAC,GAAG,IAAI;cAC3C;YACJ;UACJ;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EAEAtD,iBAAiBA,CAAC+D,OAAO,EAAE;IACvB,IAAIrF,OAAO,GAAG,IAAI,CAACb,KAAK,CAACZ,IAAI,CAAC,CAAC,CAAC;IAChC,IAAI+G,kBAAkB,GAAG,CAAC,CAAC;IAC3BtF,OAAO,CAACL,GAAG,CAAC,CAACyE,WAAW,EAAExC,KAAK,KAAK;MAChC,IAAIhC,eAAe,GAAG,IAAI,CAACM,uBAAuB,CAACkE,WAAW,CAAC;MAC/D,IAAIxI,KAAK,GAAGyJ,OAAO,CAACzD,KAAK,CAAC;MAC1B,IAAIhC,eAAe,CAAClE,MAAM,IAAI,aAAa,EAAE;QACzC;QACA,IAAIE,KAAK,EAAE;UACP;UACA,IAAI2J,cAAc,GAAGvL,iBAAiB,CAAC4B,KAAK,CAAC;UAC7C,IAAI2J,cAAc,EAAE;YAChB3J,KAAK,GAAG3B,MAAM,CAACsL,cAAc,CAAC;YAC9B3J,KAAK,GAAGA,KAAK,CAAC4E,MAAM,CAAC,YAAY,CAAC;UACtC,CAAC,MAAM;YACH5E,KAAK,GAAG3B,MAAM,CAAC2B,KAAK,CAAC;UACzB;QACJ,CAAC,MAAM;UACHA,KAAK,GAAGU,SAAS;QACrB;MACJ,CAAC,MAAM,IAAIsD,eAAe,CAACmC,WAAW,KAAK,YAAY,EAAE;QACrDyD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE7J,KAAK,EAAE,OAAOA,KAAK,CAAC;QACpD,IAAIA,KAAK,EAAE;UACP,MAAM8J,cAAc,GAAG,IAAI,CAACrF,sBAAsB,CAACzE,KAAK,CAAC;UACzD;UACA,IAAI8J,cAAc,KAAK9J,KAAK,EAAE;YAC1BA,KAAK,GAAG8J,cAAc;YACtBF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE7J,KAAK,CAAC;UAChD,CAAC,MAAM;YACH;YACAA,KAAK,GAAG,EAAE,GAAGA,KAAK;UACtB;QACJ,CAAC,MAAM;UACHA,KAAK,GAAGU,SAAS;QACrB;MACJ,CAAC,MAAM,IACHsD,eAAe,CAAClE,MAAM,IAAI,QAAQ,IAClCkE,eAAe,CAAClE,MAAM,IAAI,QAAQ,IAClCE,KAAK,EACP;QACEA,KAAK,GAAG,EAAE,GAAGA,KAAK;MACtB,CAAC,MAAM,IACH,IAAI,CAACE,KAAK,CAAC6J,MAAM,IACjB,CAAC/J,KAAK,IACNgE,eAAe,CAAClE,MAAM,IAAI,QAAQ,EACpC;QACEE,KAAK,GAAG,EAAE;MACd,CAAC,MAAM,IACH,IAAI,CAACE,KAAK,CAAC8J,mBAAmB,IAC9B,CAAChK,KAAK,IACNgE,eAAe,CAAClE,MAAM,IAAI,QAAQ,EACpC;QACEE,KAAK,GAAG,EAAE;MACd;MACA,IACI,CAACA,KAAK,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC8J,mBAAmB,KACzChG,eAAe,CAACjE,OAAO,EACzB;QAAA,IAAAkK,qBAAA,EAAAC,MAAA,EAAAC,YAAA;QACEnK,KAAK,GAAG,EAAE,GAAGA,KAAK;QAClB;QACA,IACI,EAAAiK,qBAAA,GAAAjG,eAAe,CAACoG,WAAW,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BI,IAAI,KAAI,UAAU,IAC/C,EAAAH,MAAA,GAAAlK,KAAK,cAAAkK,MAAA,wBAAAC,YAAA,GAALD,MAAA,CAAOI,KAAK,CAAC,GAAG,CAAC,cAAAH,YAAA,uBAAjBA,YAAA,CAAmBlJ,MAAM,KAAI,CAAC,EAChC;UACE;UACA,IAAIsJ,UAAU,GAAG,EAAE;UACnBvK,KAAK,CAACsK,KAAK,CAAC,GAAG,CAAC,CAACvG,GAAG,CAAEyG,WAAW,IAAK;YAClC,IAAIC,aAAa,GAAGzG,eAAe,CAACjE,OAAO,CAAC6H,MAAM,CAC7C8C,YAAY,IACTA,YAAY,CAACpL,KAAK,KAAIkL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,IAAI,CAAC,CAAC,CACjD,CAAC;YACD,IAAIF,aAAa,CAACxJ,MAAM,GAAG,CAAC,EAAE;cAC1BsJ,UAAU,CAACpG,IAAI,CAACsG,aAAa,CAAC,CAAC,CAAC,CAACzK,KAAK,CAAC;YAC3C;UACJ,CAAC,CAAC;UACF,IAAIuK,UAAU,CAACtJ,MAAM,IAAI,CAAC,EAAE;YACxBjB,KAAK,GAAGU,SAAS;UACrB,CAAC,MAAM;YACHV,KAAK,GAAGuK,UAAU;UACtB;QACJ,CAAC,MAAM;UACH,IAAIE,aAAa;UACjB,IAAI,IAAI,CAACvK,KAAK,CAAC8J,mBAAmB,EAAE;YAChCS,aAAa,GAAGzG,eAAe,CAACjE,OAAO,CAAC6H,MAAM,CACzC8C,YAAY;cAAA,IAAAE,mBAAA,EAAAC,OAAA;cAAA,OACT,EAAAD,mBAAA,GAAAF,YAAY,CAACpL,KAAK,cAAAsL,mBAAA,uBAAlBA,mBAAA,CAAoBD,IAAI,CAAC,CAAC,OAAAE,OAAA,GAAI7K,KAAK,cAAA6K,OAAA,uBAALA,OAAA,CAAOF,IAAI,CAAC,CAAC;YAAA,CACnD,CAAC;UACL,CAAC,MAAM;YACHF,aAAa,GAAGzG,eAAe,CAACjE,OAAO,CAAC6H,MAAM,CACzC8C,YAAY;cAAA,IAAAI,OAAA;cAAA,OACTJ,YAAY,CAACpL,KAAK,MAAAwL,OAAA,GAAI9K,KAAK,cAAA8K,OAAA,uBAALA,OAAA,CAAOH,IAAI,CAAC,CAAC;YAAA,CAC3C,CAAC;UACL;UACA,IAAIF,aAAa,CAACxJ,MAAM,IAAI,CAAC,EAAE;YAC3BjB,KAAK,GAAGU,SAAS;UACrB,CAAC,MAAM;YACHV,KAAK,GAAGyK,aAAa,CAAC,CAAC,CAAC,CAACzK,KAAK;UAClC;QACJ;MACJ;MAEA0J,kBAAkB,CAAC1F,eAAe,CAACxE,GAAG,CAAC,GAAGQ,KAAK;IACnD,CAAC,CAAC;IACF0J,kBAAkB,GAAGxL,0BAA0B,CAC3CwL,kBAAkB,EAClB,IAAI,CAAC5F,YAAY,CAAC,CACtB,CAAC;IACD,OAAO4F,kBAAkB;EAC7B;EAEAqB,eAAeA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACd,IAAI,EAAAA,aAAA,OAAI,CAAC5E,OAAO,CAAC,CAAC,cAAA4E,aAAA,uBAAdA,aAAA,CAAgB/J,MAAM,IAAG,CAAC,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EAEA6C,YAAYA,CAAA,EAAG;IACX,IAAI;MAAED,SAAS;MAAEoH,QAAQ;MAAEC,SAAS;MAAE5H;IAAwB,CAAC,GAC3D,IAAI,CAACpD,KAAK;IACd,IAAI+K,QAAQ,IAAI,CAACpH,SAAS,EAAE;MACxB,OAAOxE,qBAAqB;IAChC;;IAEA;IACA,OAAOwE,SAAS;EACpB;EAEAsH,mBAAmBA,CAAC1E,KAAK,EAAE;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAI,CAAChD,QAAQ,CAAC;MACVb,oBAAoB,EAAE,IAAI;MAC1BC,kBAAkB,EAAE;IACxB,CAAC,CAAC;IAEF,IAAIuI,MAAM,GAAG,CAAC,CAAC;IACfA,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACzC,MAAMC,UAAU,GAAInE,IAAI,IAAK;MACzB;MACA,IAAI,CAAC1D,QAAQ,CACT;QACIb,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE,GAAG;QACvBE,cAAc,EAAE,IAAI;QACpBG,UAAU,EAAE;MAChB,CAAC,EACD,IAAI,CAACqI,UAAU,CAACpE,IAAI,CAACqE,IAAI,CAACC,SAAS,CACvC,CAAC;MACD;MACA7M,mBAAmB,CAAC8M,OAAO,CACvB,gCAAgC,EAChC,IAAI,CAACnI,KAAK,CAAC7B,QACf,CAAC;IACL,CAAC;IACD,MAAMiK,OAAO,GAAInL,KAAK,IAAK;MACvB;MACA,IAAIoL,YAAY,GAAGnN,UAAU,CAACoN,oBAAoB,CAACrL,KAAK,CAAC;MACzD,IAAI,OAAOoL,YAAY,IAAI,QAAQ,EAAE;QACjCA,YAAY,GAAGE,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC,CAACI,IAAI;MAChD;MACA,IAAI,CAACvI,QAAQ,CAAC;QACVb,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE,GAAG;QACvBE,cAAc,EAAE,IAAI;QACpBG,UAAU,EAAE0I;MAChB,CAAC,CAAC;IACN,CAAC;IACDnN,UAAU,CAACwN,eAAe,CACtB,IAAI,CAAC/L,KAAK,CAACgM,SAAS,EACpBd,MAAM,EACNE,UAAU,EACVK,OACJ,CAAC;;IAED;IACA;IACA;IACA;EACJ;EAEAJ,UAAUA,CAACE,SAAS,EAAE;IAClB,IAAI,IAAI,CAACvL,KAAK,CAACiM,cAAc,EAAE;MAC3B,IAAI,CAACjM,KAAK,CAACiM,cAAc,CAACV,SAAS,CAAC;IACxC;EACJ;EAEAW,eAAeA,CAACC,IAAI,EAAE;IAClB,IAAIA,IAAI,GAAG,GAAG,EAAE;MACZ,IAAI,CAAC5I,QAAQ,CAAC;QACVb,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE,GAAG;QACvBE,cAAc,EAAE;MACpB,CAAC,CAAC;IACN,CAAC,MAAM;MACHsJ,IAAI,GAAGA,IAAI,GAAG,EAAE;MAChB,IAAI,CAAC5I,QAAQ,CACT;QACIb,oBAAoB,EAAE,IAAI;QAC1BC,kBAAkB,EAAEwJ;MACxB,CAAC,EACD,MACIxL,UAAU,CAAEwL,IAAI,IAAK,IAAI,CAACD,eAAe,CAACC,IAAI,CAAC,EAAE,IAAI,EAAEA,IAAI,CACnE,CAAC;IACL;EACJ;EAEAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrK,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACwB,QAAQ,CACT;MACIV,cAAc,EAAE,KAAK;MACrBD,cAAc,EAAE;IACpB,CAAC,EACD,MACI,IAAI,CAACW,QAAQ,CAAC;MACVf,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRP,iBAAiB,EAAE,EAAE;MACrBU,cAAc,EAAE;IACpB,CAAC,CACT,CAAC;EACL;EAEA8C,eAAeA,CAACJ,kBAAkB,EAAE;IAChC,IAAI,CAAC,IAAI,CAAClD,eAAe,CAACkD,kBAAkB,CAAC,EAAE;MAC3C,IAAI,CAAClD,eAAe,CAACkD,kBAAkB,CAAC,GAAG,IAAI;MAC/C,IAAI,CAACnD,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;IAC3C;IAEA,IAAI,CAAC,IAAI,CAACkB,KAAK,CAACP,WAAW,EAAE;MACzB,IAAI,CAACS,QAAQ,CAAC;QACVT,WAAW,EAAE;MACjB,CAAC,CAAC;IACN;IACA,IAAI,CAACuJ,4BAA4B,CAAC,CAAC;EACvC;EAEAzG,mBAAmBA,CAAC0G,gBAAgB,EAAEhH,kBAAkB,EAAE;IACtD,IAAIrD,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;IACpD;IACA,IAAI,CAACA,oBAAoB,CAACqD,kBAAkB,CAAC,EAAE;MAC3C,IAAIpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC9CA,iBAAiB,CAAC+B,IAAI,CAACqI,gBAAgB,CAAC;MACxCrK,oBAAoB,CAACqD,kBAAkB,CAAC,GAAG,IAAI;MAC/C,IAAI,CAACrD,oBAAoB,GAAGA,oBAAoB;MAChD,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC9C;IAEA,IAAI,CAACmK,4BAA4B,CAAC,CAAC;EACvC;EAEAA,4BAA4BA,CAAA,EAAG;IAC3B,IAAIE,cAAc,GAAG,IAAI,CAACrK,iBAAiB,CAACnB,MAAM;IAClD;IACA,IAAIoB,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC;IACA,IAAIqK,gBAAgB,GAAGD,cAAc,GAAGpK,WAAW;IACnD,IAAIiF,SAAS,GAAG,IAAI,CAAClB,OAAO,CAAC,CAAC,CAACnF,MAAM;IACrC,IACIyL,gBAAgB,IAAIpF,SAAS,IAC7B,IAAI,CAAC/D,KAAK,CAACnB,iBAAiB,CAACnB,MAAM,IAAI,CAAC,EAC1C;MACE;MACA;MACA;MACA,IAAI,CAACwC,QAAQ,CAAC;QACVpB,WAAW,EAAEA,WAAW;QACxBF,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAC/CC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCe,kBAAkB,EAAE,KAAK;QACzBC,WAAW,EAAE,IAAI,CAACG,KAAK,CAACF,eAAe,GACjC,CAAC,GACD,IAAI,CAACE,KAAK,CAACH,WAAW;QAC5BP,kBAAkB,EAAE,CAAC;QACrBQ,eAAe,EAAE;MACrB,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACE,KAAK,CAACJ,kBAAkB,EAAE;MACtC;MACA,IAAIwJ,aAAa,GAAGC,IAAI,CAACC,IAAI,CAACvF,SAAS,GAAG,EAAE,CAAC;MAC7C,IAAIlE,WAAW,GAAG,IAAI,CAACG,KAAK,CAACH,WAAW;MACxC,IACIA,WAAW,IAAIuJ,aAAa,IAC5BD,gBAAgB,IAAItJ,WAAW,GAAG,EAAE,EACtC;QACE,IAAI,CAACK,QAAQ,CAAC;UACVL,WAAW,EAAEA,WAAW,GAAG,CAAC;UAC5BP,kBAAkB,EAAE+J,IAAI,CAACE,KAAK,CACzB1J,WAAW,GAAGuJ,aAAa,GAAI,GACpC;QACJ,CAAC,CAAC;QACF;MACJ;IACJ;EACJ;EAEAtB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9H,KAAK,CAACnB,iBAAiB;EACvC;EAEA2K,uBAAuBA,CAACC,cAAc,EAAE;IACpC,OAAOJ,IAAI,CAACC,IAAI,CAACG,cAAc,GAAG7N,QAAQ,CAAC;EAC/C;EACA8N,yBAAyBA,CAACD,cAAc,EAAE;IACtC,IAAIA,cAAc,GAAG7N,QAAQ,EAAE;MAC3B,OAAOyN,IAAI,CAACC,IAAI,CAACG,cAAc,GAAG7N,QAAQ,CAAC,GACrCyN,IAAI,CAACC,IAAI,CAACG,cAAc,GAAG7N,QAAQ,CAAC,GACpCA,QAAQ;IAClB;IACA,OAAO6N,cAAc;EACzB;EACA3H,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE4F,QAAQ;MAAEC,SAAS;MAAEjD,OAAO;MAAExC;IAAmB,CAAC,GAAG,IAAI,CAACvF,KAAK;IACrE+H,OAAO,GAAGA,OAAO,IAAI7I,cAAc;IACnC,MAAM;MACFwD,oBAAoB;MACpBC,kBAAkB;MAClBE,cAAc;MACdD,cAAc;MACdE,WAAW;MACXX,WAAW;MACXY,WAAW;MACXC,UAAU;MACVC;IACJ,CAAC,GAAG,IAAI,CAACI,KAAK;IACd,MAAM2J,kBAAkB,GAAGpK,cAAc,GAAG;MAAE9C,KAAK,EAAE;IAAG,CAAC,GAAG,CAAC,CAAC;IAC9D,MAAMmN,aAAa,GAAG,IAAI,CAACpC,eAAe,CAAC,CAAC;IAC5C,MAAMlH,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACrC,oBACIhG,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACKoJ,QAAQ,iBACLnN,KAAA,CAAAwD,aAAA,CAACxE,KAAK;MACFK,OAAO,EAAC,sBAAsB;MAC9BiQ,IAAI,EAAC,SAAS;MAAA5L,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACV,CACX,EACAqJ,SAAS,iBACNpN,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI/D,KAAA,CAAAwD,aAAA,CAACrE,KAAK;MACFoQ,WAAW,EAAC,4BAA4B;MACxCrN,KAAK,EAAE8L,IAAI,CAACwB,SAAS,CAAC,IAAI,CAACjC,WAAW,CAAC,CAAC,CAAE;MAAA7J,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAC7C,CACA,CACR,EACA,CAAC,IAAI,CAAC3B,KAAK,CAAC6J,MAAM,iBACfjM,KAAA,CAAAwD,aAAA;MAAKQ,SAAS,EAAC,EAAE;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACb/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,kBAEA,eAAA/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAO,qBAA0B,CAAC,eAClC/D,KAAA,CAAAwD,aAAA,CAACtC,SAAS;MACNuO,QAAQ,EACJ,IAAI,CAACrN,KAAK,CAAC6J,MAAM,GACX,sBAAsB,GACtB,sBACT;MACDyD,OAAO,eACH1P,KAAA,CAAAwD,aAAA,CAACvE,MAAM;QACH0Q,IAAI,eAAE3P,KAAA,CAAAwD,aAAA,CAAC3C,gBAAgB;UAAA6C,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAE;QAC3BC,SAAS,EAAC,SAAS;QACnBsL,IAAI,EAAC,MAAM;QAAA5L,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GACd,UAEO,CACX;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAED/D,KAAA,CAAAwD,aAAA,CAACrC,UAAU;MAACuM,IAAI,EAAE,EAAG;MAACzD,IAAI,EAAC,SAAS;MAAAvG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC/BgC,SAAS,CACL+D,MAAM,CAAE3B,SAAS,IAAKA,SAAS,CAAC3G,KAAK,CAAC,CACtCyE,GAAG,CAAEkC,SAAS,iBACXnI,KAAA,CAAAwD,aAAA,CAACpC,WAAW;MACRI,KAAK,EAAE2G,SAAS,CAAC3G,KAAM;MACvBU,KAAK,EAAEiG,SAAS,CAAC3G,KAAM;MACvBoO,KAAK,EAAE;QACHC,IAAI,EAAE;UACFC,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE;YACLC,GAAG,EAAE;UACT;QACJ;MACJ,CAAE;MAAAtM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACL,CACJ,CACG,CACL,CACX,CAAC,eACL/D,KAAA,CAAAwD,aAAA;MACIoM,KAAK,EAAE;QACHK,SAAS,EAAE;MACf,CAAE;MAAAvM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEF/D,KAAA,CAAAwD,aAAA;MAAO0M,KAAK,EAAC,UAAU;MAAAxM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnB/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACKgC,SAAS,CAACE,GAAG,CACV,CAACC,eAAe,EAAEgC,KAAK,KACnBhC,eAAe,CAAC1E,KAAK,iBACjBxB,KAAA,CAAAwD,aAAA;MACI9B,GAAG,EAAEwG,KAAM;MACXlE,SAAS,EACLkC,eAAe,CAACzE,QAAQ,GAClB,mBAAmB,GACnB,EACT;MACDmO,KAAK,EAAE;QACHO,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACd,CAAE;MAAA5M,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEDmC,eAAe,CAAC1E,KAAK,EACrB0E,eAAe,CAACjE,OAAO,iBACpBjC,KAAA,CAAAwD,aAAA;MAAIQ,SAAS,EAAC,2DAA2D;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrE/D,KAAA,CAAAwD,aAAA,CAACvE,MAAM;MACHqQ,IAAI,EAAC,MAAM;MACXiB,OAAO,EAAEA,CAAA,KACL,IAAI,CAAC7K,2BAA2B,CAC5BQ,eAAe,CAACxE,GACpB,CACH;MACD8O,IAAI,EAAC,OAAO;MAAA9M,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACf,WAEO,CAAC,eACT/D,KAAA,CAAAwD,aAAA,CAAClE,KAAK;MACF8H,KAAK,EAAE,GAAGlB,eAAe,CAAC1E,KAAK,OAAQ;MACvCiP,OAAO,EACH,IAAI,CACChL,KAAK,CACLD,uBAAuB,CACxBU,eAAe,CACVxE,GAAG,CAEf;MACDgP,MAAM,EACF,IACH;MACDC,QAAQ,EAAEA,CAAA,KACN,IAAI,CAAC/K,2BAA2B,CAC5BM,eAAe,CAACxE,GACpB,CACH;MAAAgC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAED/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEKmC,eAAe,CAACjE,OAAO,CAACgE,GAAG,CACxB,CACI2K,MAAM,EACN1I,KAAK,kBAELlI,KAAA,CAAAwD,aAAA,CAACvC,SAAS;MACN+C,SAAS,EAAC,SAAS;MACnBtC,GAAG,EACCwG,KACH;MACD2I,QAAQ,EAAE;QACNrJ,IAAI,EAAE,GAAGoJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEpP,KAAK,EAAE,CAAE;MAC9B,CAAE;MAAAkC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAGDmE,KAAK,GACF,CAAC,EAAC,GAEL,EAAC,GAAG,EAGD0I,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEpP,KAEL,CAEnB,CACD,CACA,CACP,CAER,CAEhB,CACA,CACD,CACJ,CACN,CAAC,eACNxB,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAQ,CACP,CACR,eACD/D,KAAA,CAAAwD,aAAA;MAAKQ,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpB/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iBAAmB,CAAC,eACxB/D,KAAA,CAAAwD,aAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACtB/D,KAAA,CAAAwD,aAAA;MAAKoM,KAAK,EAAE;QAAEkB,KAAK,EAAE;MAAO,CAAE;MAAApN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1B/D,KAAA,CAAAwD,aAAA,CAAC9C,QAAQ,EAAAqK,MAAA,CAAAgG,MAAA;MACLC,SAAS,EAAC;IAAO,GACbvQ,aAAa,CAAC,MAAM,CAAC;MAAAiD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,EAC5B,CACA,CAAC,eACN/D,KAAA,CAAAwD,aAAA,UAAAuH,MAAA,CAAAgG,MAAA;MACIzB,IAAI,EAAC;IAAM,GACPF,kBAAkB;MACtBmB,OAAO,EAAG1I,CAAC,IACP,IAAI,CAAClC,QAAQ,CAAC;QAAEN,kBAAkB,EAAE;MAAK,CAAC,CAC7C;MACD4L,QAAQ,EAAEnM,oBAAoB,IAAIG,cAAe;MACjDiM,QAAQ,EAAGrJ,CAAC,IAAK;QACb,IAAI,CAACa,gBAAgB,CAACb,CAAC,CAAC;MAC5B,CAAE;MACF+H,KAAK,EAAE;QAAES,OAAO,EAAE;MAAO,CAAE;MAC3B,eAAY,mBAAmB;MAAA3M,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,EAClC,CACA,CAAC,EAELgB,kBAAkB,GAAG,CAAC,IAAIA,kBAAkB,GAAG,GAAG,iBAC/C/E,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,qBACIjE,KAAA,CAAAwD,aAAA,CAAC/D,IAAI;MAAAiE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAO,CAAC,eACb/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAQ,CAAC,mBAET,eAAA/D,KAAA,CAAAwD,aAAA,CAACjE;IACG;IAAA;MACAyE,SAAS,EAAC,SAAS;MACnBmN,OAAO,EAAEpM,kBAAmB;MAAArB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAC/B,CACH,CACL,EACAoG,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC/H,KAAK,CAAC6J,MAAM,iBAC9BjM,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,QAAM,EAACoG,OAAO,EAAC,2BAA4B,CACjD,EACA,CAAC,IAAI,CAAC/H,KAAK,CAAC6J,MAAM,iBACfjM,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,yGAGS,EAAC,GACV,CAAC,eACJ/D,KAAA,CAAAwD,aAAA;MAAGQ,SAAS,EAAC,gBAAgB;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,oCACQ,EAAC,GACpC,CACF,CACR,EACA,IAAI,CAAC3B,KAAK,CAACgP,aAAa,IAAI,CAAC,IAAI,CAAChP,KAAK,CAAC6J,MAAM,iBAC3CjM,KAAA,CAAAwD,aAAA;MAAGQ,SAAS,EAAC,aAAa;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,6DACoC,EAAC,GAC7D,CACN,EACAsL,aAAa,iBACVrP,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,QACKkB,WAAW,CAAChC,MAAM,GAAG,CAAC,iBACnBnD,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,QACKkB,WAAW,CAACc,GAAG,CAAC,CAACoL,UAAU,EAAEnJ,KAAK,kBAC/BlI,KAAA,CAAAwD,aAAA;MAAGQ,SAAS,EAAC,aAAa;MAACtC,GAAG,EAAEwG,KAAM;MAAAxE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACjCsN,UACF,CACN,CACH,CACL,EACAtG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,oBAAoB,CAAC,CAACtB,MAAM,GAC1C,CAAC,iBACDnD,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,QACK8G,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,oBAAoB,CAAC,CAACwB,GAAG,CACtCqL,mBAAmB,iBAChBtR,KAAA,CAAAwD,aAAA;MAAGQ,SAAS,EAAC,aAAa;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,eACV,EAAC,GAAG,EAChB,IAAI,CAACkL,uBAAuB,CACzBqC,mBACJ,CAAC,EAAE,GAAG,EAAC,QACD,EAAC,GAAG,EACT,IAAI,CAACnC,yBAAyB,CAC3BmC,mBACJ,CACD,CAEX,CACF,CACL,EACApM,WAAW,IAAIX,WAAW,GAAG,CAAC,iBAC3BvE,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,QACK8G,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,eAAe,CAAC,CAACyB,GAAG,CACjCsL,oBAAoB,iBACjBvR,KAAA,CAAAwD,aAAA;MAAGQ,SAAS,EAAC,aAAa;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,eACV,EAAC,GAAG,EAChB,IAAI,CAACkL,uBAAuB,CACzBsC,oBACJ,CAAC,EAAE,GAAG,EAAC,QACD,EAAC,GAAG,EACT,IAAI,CAACpC,yBAAyB,CAC3BoC,oBACJ,CACD,CAEX,CACF,CACL,eACDvR,KAAA,CAAAwD,aAAA,CAACvE,MAAM;MACHqQ,IAAI,EAAC,SAAS;MACd2B,QAAQ,EACJ,CAAC5B,aAAa,IACdvK,oBAAoB,IACpBG,cAAc,IACdC,WAAW,IACXG,kBACH;MACDkL,OAAO,EAAG1I,CAAC,IAAK;QACZ,IAAI,CAACwF,mBAAmB,CAACxF,CAAC,CAAC;MAC/B,CAAE;MAAAnE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAED,IAAI,CAAC3B,KAAK,CAAC6J,MAAM,GACZ,qBAAqB,GACrB,qBACF,CAAC;IAAA;IAEL;IACAjM,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,qBACIjE,KAAA,CAAAwD,aAAA,CAACvE,MAAM;MACHsR,OAAO,EAAG1I,CAAC,IAAK,IAAI,CAAC2G,WAAW,CAAC,CAAE;MACnCc,IAAI,EAAC,MAAM;MAAA5L,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACd,aAEO,CAAC,eACT/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAQ,CAAC,EACRkB,cAAc,iBACXjF,KAAA,CAAAwD,aAAA,CAACxE,KAAK;MACFsQ,IAAI,EAAC,MAAM;MACXjQ,OAAO,eACHW,KAAA,CAAAwD,aAAA;QAAAE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI/D,KAAA,CAAAwD,aAAA;QAAAE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAI,qBAAuB,CAAC,eAC5B/D,KAAA,CAAAwD,aAAA;QAAAE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAQ,CAAC,eACT/D,KAAA,CAAAwD,aAAA;QACIoM,KAAK,EACD,IAAI,CAACxN,KAAK,CACLoP,kBAAkB,GACjB;UACIC,UAAU,EACN;QACR,CAAC,GACD,CAAC,CACV;QAAA/N,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEAqB,UACF,CACF,CACR;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACJ,CAEP,CAAC,EAENe,oBAAoB,iBACjB9E,KAAA,CAAAwD,aAAA,CAAAxD,KAAA,CAAAiE,QAAA,qBACIjE,KAAA,CAAAwD,aAAA,CAAC/D,IAAI;MAAAiE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAO,CAAC,eACb/D,KAAA,CAAAwD,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAQ,CAAC,wBAEX,CACL,EAEA4D,kBAAkB,gBACf3H,KAAA,CAAAwD,aAAA,CAACpE,IAAI;MACDsS,UAAU,EAAC,YAAY;MACvBlB,IAAI,EAAC,OAAO;MACZmB,QAAQ;MACRC,UAAU,EAAE;QACRV,QAAQ,EAAGW,IAAI,IAAK;UAChB/F,OAAO,CAACC,GAAG,CAAC8F,IAAI,CAAC;UACjB,IAAI,CAAClM,QAAQ,CAAC;YACVL,WAAW,EAAEuM;UACjB,CAAC,CAAC;QACN,CAAC;QACDxQ,QAAQ,EAAE,EAAE;QACZyQ,OAAO,EAAE,IAAI,CAACrM,KAAK,CAACH;MACxB,CAAE;MACFyM,UAAU,EAAE,IAAI,CAACzJ,OAAO,CAAC,CAAE;MAC3B0J,UAAU,EAAGC,IAAI,iBACbjS,KAAA,CAAAwD,aAAA,CAACpE,IAAI,CAAC8S,IAAI;QACNxQ,GAAG,EAAEuQ,IAAI,CAACvK,kBAAmB;QAAAhE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAE7B/D,KAAA,CAAAwD,aAAA,CAACrB,sBAAsB;QACnBE,IAAI,EAAE,IAAI,CAACwD,OAAO,CAAC,CAAE;QACrBpD,cAAc;QACdH,aAAa,EAAE,IAAI,CAACsF,iBAAiB,CACjCqK,IACJ,CAAE;QACF1P,iBAAiB,EAAGsF,CAAC,IACjB,IAAI,CAACC,eAAe,CAChBmK,IAAI,CAACvK,kBACT,CACH;QACDlF,eAAe,EAAGuF,QAAQ,IACtB,IAAI,CAACC,mBAAmB,CACpBD,QACJ,CACH;QAAArE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CACJ,CACM,CACb;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACL,CAAC,gBAEF/D,KAAA,CAAAwD,aAAA,CAAC9D,KAAK;MACFsE,SAAS,EAAC,qBAAqB;MAC/B2N,QAAQ;MACRnB,IAAI,EAAC,OAAO;MACZoB,UAAU,EAAE;QACRV,QAAQ,EAAGW,IAAI,IAAK;UAChB/F,OAAO,CAACC,GAAG,CAAC8F,IAAI,CAAC;UACjB,IAAI,CAAClM,QAAQ,CAAC;YACVL,WAAW,EAAEuM;UACjB,CAAC,CAAC;QACN,CAAC;QACDxQ,QAAQ,EAAE,EAAE;QACZyQ,OAAO,EAAE,IAAI,CAACrM,KAAK,CAACH;MACxB,CAAE;MACFgB,OAAO,EAAE,IAAI,CAACW,UAAU,CAAC,CAAE;MAC3B8K,UAAU,EAAE,IAAI,CAACzJ,OAAO,CAAC;MACzB;MAAA;MAAA5E,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACH,CAEP,CAEL,CACJ,CAAC;EAEd;AACJ;AAEA,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}