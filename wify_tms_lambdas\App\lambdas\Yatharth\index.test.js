const AWS = require("aws-sdk");
require("dotenv").config();

AWS.config.update({
  accessKeyId: "",
  secretAccessKey: "",
  region: "ap-south-1",
});

const lambda = new AWS.Lambda();

describe("Lambda Function Actual Call", () => {
  it("should invoke the actual Lambda function", async () => {
    const params = {
      FunctionName: "tms-commercials-B2C-pnl-data-aggregator",
      InvocationType: "RequestResponse",
      Payload: "{}",
    };
    const response = await lambda.invoke(params).promise();
    expect(response.StatusCode).toBe(200); // Check HTTP status code
    const payload = JSON.parse(response.Payload);
    expect(payload).toBeDefined(); // Verify response exists
  }, 600000); // Set timeout to 30 seconds
});
