const { v4: uuidv4 } = require("uuid"); // Make sure to install: npm install uuid

exports.handler = async (event) => {
  try {
    let message = "Error occured cannot convert json";
    const inputJson = event?.convertedData || {};
    console.log("inputJson: ", JSON.stringify(inputJson));

    // ===== Existing address extraction =====
    const deliveryAddress = inputJson?.ServiceOrder?.Addresses?.Address?.find(
      (addr) => addr.Code === "DELIVERY"
    );
    const invoiceAddress = inputJson?.ServiceOrder?.Addresses?.Address?.find(
      (addr) => addr.Code === "INVOICE"
    );

    if (!deliveryAddress)
      throw new Error("DELIVERY address not found in input JSON");
    if (!invoiceAddress)
      throw new Error("INVOICE address not found in input JSON");

    let cust_line_0 = "";
    let cust_line_1 = "";
    if (deliveryAddress.Address1) {
      const [line0, ...rest] = deliveryAddress.Address1.split(",");
      cust_line_0 = line0.trim();
      cust_line_1 = rest.join(",").trim();
    }

    const extraAddressParts = [
      deliveryAddress.Address2,
      deliveryAddress.Address3,
      deliveryAddress.Address4,
      deliveryAddress.Address5,
    ]
      .filter(Boolean)
      .join(", ");

    const timeWindowFrom =
      inputJson?.ServiceOrder?.Services?.Service?.TimeWindows?.BookedTimeWindow
        ?.TimeWindowFrom;
    const formattedDate = timeWindowFrom
      ? new Date(timeWindowFrom).toISOString()
      : null;

    const invoiceAddressParts = [
      invoiceAddress.Address1,
      invoiceAddress.Address2,
      invoiceAddress.Address3,
      invoiceAddress.Address4,
      invoiceAddress.Address5,
    ].filter(Boolean);

    const now = process.hrtime.bigint();
    const microseconds = Number(now / 1000n);

    // ===== 🆕 ServiceOrderLines mapping =====
    const serviceOrderLines =
      inputJson??.ServiceOrderLines?.ServiceOrderLine || [];
    console.log("serviceOrderLines -- ", serviceOrderLines);

    const flattenArticles = (lines) => {
      let result = [];
      for (const line of lines) {
        if (line.Type === "ARTICLE") {
          const qty = Number(line.QuantityOrdered) || 0;
          const rate = Number(line.GrossPrice) || 0;
          const key = uuidv4();
          result.push({
            key,
            qty,
            rate,
            total: qty * rate,
            input_table_id: key,
            "05df3fda-9c70-4af9-8707-cb6f5eafea84": Number(line.NetPrice) || 0,
            "0bc58b52-6a1c-4aad-b3c8-c2036adc2d3f": line.Description || "",
            "86323979-15d3-412e-afb4-41b60514043e": line.ItemNumber || "",
          });
        }
        if (
          line.ChildOrderLines &&
          Array.isArray(line.ChildOrderLines.ServiceOrderLine)
        ) {
          result = result.concat(
            flattenArticles(line.ChildOrderLines.ServiceOrderLine)
          );
        }
      }
      return result;
    };

    const articleItems = flattenArticles(serviceOrderLines);
    console.log("articleItems :: ", articleItems);
    const totalQty = articleItems.reduce((sum, i) => sum + i.qty, 0);
    const totalAmount = articleItems.reduce((sum, i) => sum + i.total, 0);

    // ===== Final output JSON =====
    const outputJson = {
      cust_full_name: deliveryAddress.Name || null,
      cust_mobile: deliveryAddress.MobilePhone
        ? deliveryAddress.MobilePhone.slice(-10)
        : null,
      cust_email: deliveryAddress.Email || null,
      cust_pincode: deliveryAddress.PostalCode || null,
      OrderNumber: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
      cust_city: deliveryAddress.City || null,
      cust_state: deliveryAddress.State || null,
      cust_line_0,
      cust_line_1: cust_line_1 || null,
      cust_line_2: extraAddressParts || null,
      "639edfe2-2eb0-42b2-86b6-83a3547d3063": formattedDate,
      "abfe7d96-b9f7-4a2c-994a-cb5f6f169642": invoiceAddressParts,
      request_description: `Dummy description for IKEA B2C ${microseconds}`,
      request_priority: "Normal",

      // 🆕 line_items block
      line_items: {
        total: totalAmount,
        form_data: {
          "7f2e9d03-babe-49ac-ac39-54f198d607cc": articleItems,
        },
        revisions: [],
        total_qty: totalQty,
        "7f2e9d03-babe-49ac-ac39-54f198d607cc_total_qty": totalQty,
      },
    };

    console.log("outputJson: ", JSON.stringify(outputJson));
    console.log("line_items", JSON.stringify(outputJson?.line_items));

    return {
      statusCode: 200,
      body: JSON.stringify(outputJson, null, 2),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message }),
    };
  }
};
