{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\services\\\\project\\\\daily-status\\\\DailyReqStatusEditor.js\";\nimport { But<PERSON>, Col, Collapse, Form, message, Modal, Row, Spin } from 'antd';\nimport FormBuilder from 'antd-form-builder';\nimport _, { values } from 'lodash';\nimport React, { useEffect, useState } from 'react';\nimport CircularProgress from '../../../../components/CircularProgress';\nimport { decodeFieldsMetaFrmJson, decodeFileSectionsFrmJson, decodeMicSectionsFrmJson } from '../../../../components/wify-utils/FieldCreator/helpers';\nimport S3Uploader from '../../../../components/wify-utils/S3Uploader/S3Uploader';\nimport SliderWidget from '../../../../components/wify-utils/SliderWidget';\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\nimport { convertDateFieldsToMoments, convertMomentToDateString, convertMomentToLocalDateString, convertMomentToLocalDateString1, convertMomentToMonthDateAndYearFormat, convertValuesOfObjToMoment, getCurrentDateAndTimeFrDisplay, getValueDataFrmFormMeta, getViewModeMetaFrFormMeta, isMobileView, parseFormulaToString, parseFormulaToValue, setRemarkFieldAsNoRemarkIfEmpty } from '../../../../util/helpers';\nimport http_utils from '../../../../util/http_utils';\nimport { getCustomPrefixNameFrUploadingFiles } from '../../../../util/helpers';\nimport AttachmentsPreview from '../deployment/AttachmentPreview';\nimport { getAllMergedAttachments } from '../../helpers';\nimport MicInputV2 from '../../../../components/wify-utils/MicInput_v2';\nimport moment from 'moment';\nconst sbtskDetailsUrl = '/services/sbtskdetails/attachment';\nconst formatter = value => `${value}%`;\nconst DailyReqStatusEditor = ({\n  editorDayDetails,\n  srvcConfigData,\n  reqData,\n  onChange,\n  urlToSubmitFrUpdates,\n  isSrvcPrvdrTab\n}) => {\n  var _prefillFormData$sele;\n  const day = editorDayDetails.assignee_date;\n  const srvcFormData = reqData.form_data;\n  let existingDailyStatusObj = (isSrvcPrvdrTab ? srvcFormData === null || srvcFormData === void 0 ? void 0 : srvcFormData.sp_daily_status_updates : srvcFormData === null || srvcFormData === void 0 ? void 0 : srvcFormData.daily_status_updates) || {};\n  //converting obj to moment at initially because antd date.clone issue raises.\n  existingDailyStatusObj[day] = convertValuesOfObjToMoment(existingDailyStatusObj[day]);\n  const [form] = Form.useForm();\n  const [error, setError] = useState(undefined);\n  const [errorFrloadingSbtskDetailViewData, setErrorFrLoadingSbtskDetailViewData] = useState(undefined);\n  const forceUpdate = FormBuilder.useForceUpdate();\n  const [isFormSubmitting, setIsFormSubmitting] = useState(false);\n  const [fileSections, setFileSections] = useState([]); //Use karna hai\n  const [filesBySection, setFilesBySection] = useState({});\n  const [sectionWiseUploaderReady, setSectionWiseUploaderReady] = useState({});\n  const [micRecordingsBySection, setMicRecordingsBySection] = useState({});\n  const [sectionWiseMicUploaderReady, setSectionWiseMicUploaderReady] = useState({});\n  const [allFileUploadersReady, setAllFileUploadersReady] = useState(true);\n  const [allMicRecordingsUploadersReady, setAllMicRecordingsUploadersReady] = useState(true);\n  const [showUploadedFilesModal, setShowUploadedFilesModal] = useState();\n  const [sbtskDetailViewData, setSbtskDetailViewData] = useState(undefined);\n  const [loadingSbtskDetailViewData, setLoadingSbtskDetailViewData] = useState(false);\n  const [isFormChanged, setIsFormChanged] = useState(false);\n  const [isErrorShown, setIsErrorShown] = useState(false);\n  const [selectedImage, setSelectedImage] = useState({});\n  let selectedFilesBySection = {};\n  // const prefillsrvcFormData =  26132\n  // console.log('DailyReqStatusEditor - srvcConfigData',srvcConfigData);\n  // console.log('DailyReqStatusEditor : reqData',reqData);\n  const rolesWhoHaveEditAccess = (srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_who_can_edit) || [];\n  const canCurrentUserEdit = ConfigHelpers === null || ConfigHelpers === void 0 ? void 0 : ConfigHelpers.doesUserHaveOneOfTheRole(rolesWhoHaveEditAccess);\n  let timeoutInstance;\n  // console.log('DailyReqStatusEditor - canCurrentUserEdit',canCurrentUserEdit);\n\n  useEffect(() => {\n    forceUpdate();\n    setSlidersInitialValue();\n  }, []); // Refresh form after first render for dynamic form elements\n\n  const dailyUpdateIssueFormMeta = () => {\n    let dailyUpdateIssueFormMeta = decodeFieldsMetaFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_issue_form_fields);\n    return dailyUpdateIssueFormMeta;\n  };\n  const dailyUpdateFormMeta = () => {\n    let dailyUpdateFormMeta = isSrvcPrvdrTab ? decodeFieldsMetaFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.sp_daily_update_form_fields) : decodeFieldsMetaFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_form_fields);\n    return dailyUpdateFormMeta;\n  };\n  const setSlidersInitialValue = () => {\n    let lineItemWiseProgressFields = getLineItemWiseProgressMeta();\n\n    //set slider value to lower limit when you open it so if you change only one slider and save. All sliders will be saved with current values instead of 0\n    lineItemWiseProgressFields.forEach(singleLineItemMeta => {\n      let lowerLimit = getLowerLimitFrSlider(day, singleLineItemMeta.key);\n      //ssetting slider if day has already been saved before\n      if (existingDailyStatusObj[day] != undefined && existingDailyStatusObj[day][singleLineItemMeta.key] == undefined) {\n        form.setFieldsValue({\n          [singleLineItemMeta.key]: lowerLimit\n        });\n        //setting for day which has never been saved before\n      } else if (existingDailyStatusObj[day] == undefined) {\n        form.setFieldsValue({\n          [singleLineItemMeta.key]: lowerLimit\n        });\n      }\n    });\n\n    //setting day progress sliders just like line items sliders\n    let lowerLimitDailyProgress = getLowerLimitFrSlider(day, 'day_progress');\n    if (existingDailyStatusObj[day] != undefined && existingDailyStatusObj[day]['day_progress'] == undefined) {\n      form.setFieldsValue({\n        day_progress: lowerLimitDailyProgress\n      });\n    } else if (existingDailyStatusObj[day] == undefined) {\n      form.setFieldsValue({\n        day_progress: lowerLimitDailyProgress\n      });\n    }\n  };\n\n  //find closest before date so lower limit can be found to set the initial value of the slider\n  const findclosestBeforeDate = date => {\n    let allDates = Object.keys(existingDailyStatusObj);\n    let closestBefore = null;\n    let indexIfDateExists = allDates.findIndex(function (key) {\n      return key === date;\n    });\n    if (date > allDates[allDates.length - 1]) {\n      closestBefore = allDates[allDates.length - 1];\n      return closestBefore;\n    } else if (date <= allDates[0]) {\n      closestBefore = 0;\n      return closestBefore;\n    }\n    closestBefore = allDates[allDates.findIndex(function (key) {\n      return key > date;\n    }) - 1];\n    if (indexIfDateExists >= 1) {\n      closestBefore = allDates[indexIfDateExists - 1];\n    }\n    return closestBefore;\n  };\n\n  //lower limit for setting sliders in useeffect\n  const getLowerLimitFrSlider = (day, fieldKey) => {\n    let closestBefore = findclosestBeforeDate(day);\n    let prevDateData;\n    let lowerLimit = 0;\n    if (closestBefore) {\n      var _prevDateData;\n      prevDateData = existingDailyStatusObj[closestBefore];\n      lowerLimit = (_prevDateData = prevDateData) === null || _prevDateData === void 0 ? void 0 : _prevDateData[fieldKey];\n    }\n    return lowerLimit;\n  };\n  const calculateDayProgress = (value, fieldKey, lowerLimit, upperLimit) => {\n    let countOfItems = 0;\n    let totalProgOfItems = 0;\n    const lineItemWiseProgressFields = getLineItemWiseProgressMeta();\n    lineItemWiseProgressFields.forEach(singleLineItemMeta => {\n      let lineItemProg = form.getFieldValue(singleLineItemMeta.key) || 0;\n      totalProgOfItems = totalProgOfItems + lineItemProg;\n      countOfItems++;\n    });\n    const prog = Math.round(totalProgOfItems / countOfItems);\n    form.setFieldsValue({\n      day_progress: prog\n    });\n  };\n  const checkFrProgressionAcrossRange = (newVal, day, fieldKey) => {\n    //creating a sorted copy of existingdailyobj to perform operations\n    const sortedExistingDailyStatusObj = {};\n    Object.keys(existingDailyStatusObj).sort((a, b) => existingDailyStatusObj[a] - existingDailyStatusObj[b]).forEach(key => {\n      sortedExistingDailyStatusObj[key] = existingDailyStatusObj[key];\n    });\n    let dates = Object.keys(sortedExistingDailyStatusObj) || [];\n    if (dates.length == 0) {\n      return;\n    }\n\n    // sort the existing dail status obj\n    let copyOfExisitingDailyStatusObj = _.cloneDeepWith(sortedExistingDailyStatusObj);\n    if (copyOfExisitingDailyStatusObj[day] == undefined) {\n      copyOfExisitingDailyStatusObj[day] = {};\n    }\n    copyOfExisitingDailyStatusObj[day][fieldKey] = newVal;\n\n    // Loop on the days serially and get the first day at which the progression fails\n\n    let days = Object.keys(copyOfExisitingDailyStatusObj);\n    days.sort();\n    let firstDay = days[0];\n    let previousValue = copyOfExisitingDailyStatusObj[firstDay][fieldKey];\n    let msgCount = 0;\n    days.map(date => {\n      if (copyOfExisitingDailyStatusObj[date][fieldKey] < previousValue) {\n        if (!isErrorShown) {\n          message.error(`${date} progress is lower than previous date, please fix this first before update`);\n          setIsErrorShown(true);\n        }\n        msgCount++;\n      }\n      previousValue = copyOfExisitingDailyStatusObj[date][fieldKey];\n    });\n    if (timeoutInstance) {\n      clearTimeout(timeoutInstance);\n    }\n    timeoutInstance = setTimeout(() => {\n      if (msgCount > 0) {\n        var _sortedExistingDailyS;\n        form.setFieldsValue({\n          [fieldKey]: (sortedExistingDailyStatusObj === null || sortedExistingDailyStatusObj === void 0 ? void 0 : (_sortedExistingDailyS = sortedExistingDailyStatusObj[day]) === null || _sortedExistingDailyS === void 0 ? void 0 : _sortedExistingDailyS[fieldKey]) || getLowerLimitFrSlider(day, fieldKey)\n        });\n        setIsErrorShown(false);\n      } else {\n        form.setFieldsValue({\n          [fieldKey]: newVal\n        });\n      }\n      calculateDayProgress(newVal, fieldKey);\n    }, 500);\n  };\n  const getLineItemWiseProgressMeta = () => {\n    var _reqData$form_data, _reqData$form_data$sp, _reqData$form_data2, _reqData$form_data2$l;\n    let lineItemsWiseMeta = [];\n    let reqLineItemsFormData = isSrvcPrvdrTab ? reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data = reqData.form_data) === null || _reqData$form_data === void 0 ? void 0 : (_reqData$form_data$sp = _reqData$form_data.sp_line_items) === null || _reqData$form_data$sp === void 0 ? void 0 : _reqData$form_data$sp.form_data : reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data2 = reqData.form_data) === null || _reqData$form_data2 === void 0 ? void 0 : (_reqData$form_data2$l = _reqData$form_data2.line_items) === null || _reqData$form_data2$l === void 0 ? void 0 : _reqData$form_data2$l.form_data;\n    if (reqLineItemsFormData) {\n      let lineItemConfig = JSON.parse(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.srvc_type_line_item_config);\n      Object.keys(lineItemConfig).forEach(lineItemGrpKey => {\n        let groupConfig = lineItemConfig[lineItemGrpKey];\n        let nameFieldFormula = groupConfig.name_field_formula;\n        const fields = decodeFieldsMetaFrmJson(groupConfig.fields) || [];\n        const idVsLabelMapping = {};\n        fields.forEach(singleFieldMeta => {\n          idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;\n        });\n        let requestLineItemsInGrp = reqLineItemsFormData[lineItemGrpKey];\n        if (requestLineItemsInGrp && Array.isArray(requestLineItemsInGrp)) {\n          requestLineItemsInGrp.forEach((singleLineItemInGrp, index) => {\n            let label = `Item ${index + 1}`;\n            let valueDataFrLineItem = getValueDataFrmFormMeta(fields, singleLineItemInGrp);\n            if (nameFieldFormula && nameFieldFormula.length > 0) {\n              label = parseFormulaToString(nameFieldFormula, idVsLabelMapping, valueDataFrLineItem);\n            }\n            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;\n            lineItemsWiseMeta.push({\n              key: itemProgressKey,\n              label: label,\n              // parsed from formula,\n              title: label,\n              widget: SliderWidget,\n              widgetProps: {\n                tooltip: {\n                  formatter: formatter\n                },\n                onChange: value => {\n                  // check for progression\n                  if (srvcConfigData.daily_progress_update_mode == 'strict_mode') {\n                    checkFrProgressionAcrossRange(value, day, itemProgressKey);\n                  }\n                  calculateDayProgress(value, itemProgressKey);\n                }\n              }\n            });\n          });\n        }\n      });\n    }\n    return lineItemsWiseMeta;\n  };\n  const getSelectedLineItemWiseProgressMeta = selectedLineItemIds => {\n    var _reqData$form_data3, _reqData$form_data3$s, _reqData$form_data4, _reqData$form_data4$l;\n    let lineItemsWiseMeta = [];\n    if (!selectedLineItemIds || selectedLineItemIds.length === 0) {\n      return lineItemsWiseMeta;\n    }\n    let reqLineItemsFormData = isSrvcPrvdrTab ? reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data3 = reqData.form_data) === null || _reqData$form_data3 === void 0 ? void 0 : (_reqData$form_data3$s = _reqData$form_data3.sp_line_items) === null || _reqData$form_data3$s === void 0 ? void 0 : _reqData$form_data3$s.form_data : reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data4 = reqData.form_data) === null || _reqData$form_data4 === void 0 ? void 0 : (_reqData$form_data4$l = _reqData$form_data4.line_items) === null || _reqData$form_data4$l === void 0 ? void 0 : _reqData$form_data4$l.form_data;\n    if (reqLineItemsFormData) {\n      let lineItemConfig = JSON.parse(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.srvc_type_line_item_config);\n      Object.keys(lineItemConfig).forEach(lineItemGrpKey => {\n        let groupConfig = lineItemConfig[lineItemGrpKey];\n        let nameFieldFormula = groupConfig.name_field_formula;\n        const fields = decodeFieldsMetaFrmJson(groupConfig.fields) || [];\n        const idVsLabelMapping = {};\n        fields.forEach(singleFieldMeta => {\n          idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;\n        });\n        let requestLineItemsInGrp = reqLineItemsFormData[lineItemGrpKey];\n        if (requestLineItemsInGrp && Array.isArray(requestLineItemsInGrp)) {\n          requestLineItemsInGrp.forEach((singleLineItemInGrp, index) => {\n            // Check if this line item is in the selected items\n            const lineItemId = `${lineItemGrpKey}_${singleLineItemInGrp.input_table_id}`;\n            if (!selectedLineItemIds.includes(lineItemId)) {\n              return; // Skip if not selected\n            }\n            let label = `Item ${index + 1}`;\n            let valueDataFrLineItem = getValueDataFrmFormMeta(fields, singleLineItemInGrp);\n            if (nameFieldFormula && nameFieldFormula.length > 0) {\n              label = parseFormulaToString(nameFieldFormula, idVsLabelMapping, valueDataFrLineItem);\n            }\n            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;\n            lineItemsWiseMeta.push({\n              key: itemProgressKey,\n              className: 'wy-daily-update-line-item-progress',\n              label: label,\n              // parsed from formula,\n              title: label,\n              widget: SliderWidget,\n              widgetProps: {\n                tooltip: {\n                  formatter: formatter\n                },\n                onChange: value => {\n                  // check for progression\n                  if (srvcConfigData.daily_progress_update_mode == 'strict_mode') {\n                    checkFrProgressionAcrossRange(value, day, itemProgressKey);\n                  }\n                  calculateDayProgress(value, itemProgressKey);\n                }\n              }\n            });\n          });\n        }\n      });\n    }\n    return lineItemsWiseMeta;\n  };\n  const handleOkFrUploadedFilesModal = (initialFiles, key) => {\n    setShowUploadedFilesModal('');\n    if (selectedFilesBySection[key] && filesBySection[key]) {\n      let removedCount = 0;\n      filesBySection[key] = filesBySection[key].filter(element => {\n        if (selectedFilesBySection[key].includes(element)) {\n          removedCount++;\n          return false; // Exclude element from the filtered array\n        }\n        return true; // Include element in the filtered array\n      });\n      let newSelectedFilesBySection = _.cloneDeep(filesBySection);\n      if (removedCount == 0) {\n        newSelectedFilesBySection[key] = [...new Set(newSelectedFilesBySection[key].concat(selectedFilesBySection[key]))];\n      } else {\n        newSelectedFilesBySection[key] = [...newSelectedFilesBySection[key]];\n      }\n      setFilesBySection(newSelectedFilesBySection);\n      setSelectedImage(newSelectedFilesBySection);\n      onFormValueChange();\n    } else if (selectedFilesBySection[key]) {\n      let newSelectedFilesBySection = _.cloneDeep(filesBySection);\n      newSelectedFilesBySection[key] = [...selectedFilesBySection[key], ...initialFiles];\n      setFilesBySection(newSelectedFilesBySection);\n      setSelectedImage(newSelectedFilesBySection);\n      onFormValueChange();\n    }\n  };\n  const selectFromUploadedFilesClick = sbtskDetailViewData => {\n    // console.log('check',reqData.form_data)\n    if (sbtskDetailViewData == undefined) {\n      setLoadingSbtskDetailViewData(true);\n      setErrorFrLoadingSbtskDetailViewData(undefined);\n      var params = {};\n      const onComplete = resp => {\n        setSbtskDetailViewData(resp.data);\n        setLoadingSbtskDetailViewData(false);\n      };\n      const onError = error => {\n        message.error('Unable to load');\n        setErrorFrLoadingSbtskDetailViewData(http_utils.decodeErrorToMessage(error));\n        setLoadingSbtskDetailViewData(false);\n      };\n      let url = sbtskDetailsUrl + '/' + reqData.id + '/' + editorDayDetails.assignee_date;\n      // console.log(\"url\",url);\n      http_utils.performGetCall(url, params, onComplete, onError);\n    }\n  };\n  const noteSelectedFilesBySection = (fileSection, files) => {\n    var _selectedFilesBySecti;\n    if (selectedFilesBySection[fileSection] == undefined) {\n      selectedFilesBySection[fileSection] = [files];\n    } else if (!((_selectedFilesBySecti = selectedFilesBySection[fileSection]) === null || _selectedFilesBySecti === void 0 ? void 0 : _selectedFilesBySecti.includes(files))) {\n      selectedFilesBySection[fileSection].push(files);\n    } else {\n      var _selectedFilesBySecti2;\n      const index = (_selectedFilesBySecti2 = selectedFilesBySection[fileSection]) === null || _selectedFilesBySecti2 === void 0 ? void 0 : _selectedFilesBySecti2.indexOf(files);\n      if (index > -1) {\n        // only splice array when item is found\n        selectedFilesBySection[fileSection].splice(index, 1); // 2nd parameter means remove one item only\n      }\n    }\n  };\n  const hasFacedIssueForDay = () => (form.getFieldValue('issue_faced') || 'No') == 'Yes';\n\n  // Function to check if files are selected and update required property\n  const handleFileSectionRequiredStatus = singleFileSection => {\n    var _selectedImage$single;\n    const filesSelected = (selectedImage === null || selectedImage === void 0 ? void 0 : (_selectedImage$single = selectedImage[singleFileSection.key]) === null || _selectedImage$single === void 0 ? void 0 : _selectedImage$single.length) > 0;\n    singleFileSection.required = filesSelected ? false : singleFileSection.required;\n  };\n  const getLineItemOptions = () => {\n    var _reqData$form_data5, _reqData$form_data5$s, _reqData$form_data6, _reqData$form_data6$l;\n    let options = [];\n    let lineItemsData = isSrvcPrvdrTab ? reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data5 = reqData.form_data) === null || _reqData$form_data5 === void 0 ? void 0 : (_reqData$form_data5$s = _reqData$form_data5.sp_line_items) === null || _reqData$form_data5$s === void 0 ? void 0 : _reqData$form_data5$s.form_data : reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data6 = reqData.form_data) === null || _reqData$form_data6 === void 0 ? void 0 : (_reqData$form_data6$l = _reqData$form_data6.line_items) === null || _reqData$form_data6$l === void 0 ? void 0 : _reqData$form_data6$l.form_data;\n    if (!lineItemsData) {\n      return options;\n    }\n    let lineItemConfig = {};\n    try {\n      lineItemConfig = JSON.parse((srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.srvc_type_line_item_config) || '{}');\n    } catch (e) {\n      console.log('DailyReqStatusEditor :: getLineItemOptions :: Error ::', e);\n      return options;\n    }\n\n    // Process each line item group\n    Object.keys(lineItemConfig).forEach(lineItemGrpKey => {\n      if (!lineItemsData[lineItemGrpKey] || !Array.isArray(lineItemsData[lineItemGrpKey])) {\n        return;\n      }\n      let groupConfig = lineItemConfig[lineItemGrpKey] || {};\n      let nameFieldFormula = groupConfig.name_field_formula;\n      const fields = decodeFieldsMetaFrmJson(groupConfig.fields) || [];\n\n      // Create a mapping of field labels to keys\n      const idVsLabelMapping = {};\n      fields.forEach(singleFieldMeta => {\n        idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;\n      });\n\n      // Process each line item in the group\n      lineItemsData[lineItemGrpKey].forEach((singleLineItem, index) => {\n        if (!singleLineItem || !singleLineItem.input_table_id) {\n          return;\n        }\n        let label = `${groupConfig.label || 'Item'} ${index + 1}`;\n        let valueData = getValueDataFrmFormMeta(fields, singleLineItem);\n\n        // Try to use the name formula if available\n        if (nameFieldFormula && nameFieldFormula.length > 0) {\n          try {\n            label = parseFormulaToString(nameFieldFormula, idVsLabelMapping, valueData);\n          } catch (e) {\n            console.log('DailyReqStatusEditor :: getLineItemOptions :: Error ::', e);\n          }\n        }\n\n        // Add the option\n        options.push({\n          label: label,\n          value: `${lineItemGrpKey}_${singleLineItem.input_table_id}`\n        });\n      });\n    });\n    return options;\n  };\n  const meta = () => {\n    let dailyUpdateWillHaveIssues = (srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_will_have_issues) == 'Yes';\n    let trackLineItemWiseProgress = (srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_track_line_item_progress) == 'Yes';\n    let showLineItemBySelection = srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.show_line_item_by_selection;\n    let trackLineItemWisePhotos = (srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_dynamic_line_item_wise_files) == 'Yes';\n    let selectedLineItemsFrProgress = form.getFieldValue('selected_line_items');\n    return {\n      formItemLayout: null,\n      fields: [{\n        key: 'day_remarks',\n        label: 'Work done today',\n        widget: 'textarea'\n      }, {\n        render: () => /*#__PURE__*/React.createElement(\"hr\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 35\n          }\n        })\n      }, ...(dailyUpdateWillHaveIssues ? [{\n        key: 'issue_faced',\n        label: 'Issue faced ?',\n        widget: 'radio-group',\n        options: ['Yes', 'No'],\n        onChange: () => {\n          forceUpdate();\n        },\n        required: true\n      }] : []), ...(hasFacedIssueForDay() ? [{\n        render: () => /*#__PURE__*/React.createElement(\"hr\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 45\n          }\n        })\n      }, ...dailyUpdateIssueFormMeta(), {\n        render: () => {\n          var _getIssueFileMeta, _getFormIssueMicMeta;\n          return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Row, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 39\n            }\n          }, (_getIssueFileMeta = getIssueFileMeta()) === null || _getIssueFileMeta === void 0 ? void 0 : _getIssueFileMeta.map((singleFileSection, index) => {\n            var _prefillFormData$atta2, _prefillFormData$atta3, _prefillFormData$atta4, _prefillFormData$atta5;\n            handleFileSectionRequiredStatus(singleFileSection);\n            return /*#__PURE__*/React.createElement(Col, {\n              xs: 24,\n              md: 24,\n              className: \"gx-pl-0\",\n              key: singleFileSection.key,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 55\n              }\n            }, singleFileSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n              className: \"gx-mt-3\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 63\n              }\n            }, singleFileSection.required && /*#__PURE__*/React.createElement(\"span\", {\n              style: {\n                color: 'red'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 71\n              }\n            }, ' ', \"*\", ' '), singleFileSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n              className: \"gx-bg-dark\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 67\n              }\n            })), /*#__PURE__*/React.createElement(Form.Item, {\n              name: 'file_uploads',\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 59\n              }\n            }, /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Button, {\n              type: \"link\",\n              onClick: () => {\n                setShowUploadedFilesModal(singleFileSection.key);\n                selectFromUploadedFilesClick(sbtskDetailViewData);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 71\n              }\n            }, \"Select from uploaded files\"), /*#__PURE__*/React.createElement(Modal, {\n              title: \"Attachments\",\n              centered: true,\n              visible: singleFileSection.key == showUploadedFilesModal,\n              onOk: () => {\n                var _prefillFormData$atta;\n                handleOkFrUploadedFilesModal(((_prefillFormData$atta = prefillFormData.attachments) === null || _prefillFormData$atta === void 0 ? void 0 : _prefillFormData$atta[singleFileSection.key]) || [], singleFileSection.key);\n              },\n              onCancel: () => setShowUploadedFilesModal(''),\n              width: 1000,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 71\n              }\n            }, loadingSbtskDetailViewData ? /*#__PURE__*/React.createElement(\"div\", {\n              className: \"gx-loader-view gx-loader-position\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 79\n              }\n            }, /*#__PURE__*/React.createElement(CircularProgress, {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 83\n              }\n            })) : sbtskDetailViewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n              className: \"gx-text-red\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 79\n              }\n            }, errorFrloadingSbtskDetailViewData) : /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: getAllMergedAttachments(sbtskDetailViewData, ((_prefillFormData$atta2 = prefillFormData.attachments) === null || _prefillFormData$atta2 === void 0 ? void 0 : _prefillFormData$atta2[singleFileSection.key]) || []),\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 79\n              }\n            })), selectedImage[singleFileSection.key] && /*#__PURE__*/React.createElement(\"div\", {\n              key: index,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 79\n              }\n            }, /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: selectedImage[singleFileSection.key],\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              selectOnly: false,\n              sectionKey: singleFileSection.key,\n              prevUploadedFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta3 = prefillFormData.attachments) === null || _prefillFormData$atta3 === void 0 ? void 0 : _prefillFormData$atta3[singleFileSection.key],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 83\n              }\n            }))\n            // ))\n            ), /*#__PURE__*/React.createElement(S3Uploader\n            // className=\"gx-w-50\"\n            // demoMode\n            , {\n              required: singleFileSection.required,\n              maxColSpan: 4,\n              authToken: http_utils.getAuthToken(),\n              prefixDomain: http_utils.getCDNDomain(),\n              customFilePrefixName: getCustomPrefixNameFrUploadingFiles(),\n              totalFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta4 = prefillFormData.attachments) === null || _prefillFormData$atta4 === void 0 ? void 0 : _prefillFormData$atta4[singleFileSection.key],\n              onFilesChanged: (files, deletedFileUrl) => {\n                onFilesChanged(singleFileSection.key, files, deletedFileUrl);\n              },\n              onReadyStatusChanged: isReady => {\n                onFileUploaderReadyChange(singleFileSection.key, isReady);\n              },\n              initialFiles: ((_prefillFormData$atta5 = prefillFormData.attachments) === null || _prefillFormData$atta5 === void 0 ? void 0 : _prefillFormData$atta5[singleFileSection.key]) || [],\n              customPreviewHeight: \"100%\",\n              customFileIconMaxWidth: \"40px\",\n              compConfig: {\n                name: 'daily-req-status-modal-preview'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 63\n              }\n            })));\n          }), (_getFormIssueMicMeta = getFormIssueMicMeta()) === null || _getFormIssueMicMeta === void 0 ? void 0 : _getFormIssueMicMeta.map((singleMicSection, index) => {\n            var _prefillFormData$mic_;\n            return /*#__PURE__*/React.createElement(Col, {\n              xs: 24,\n              md: 24,\n              className: \"gx-pl-0\",\n              key: singleMicSection.key,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 51\n              }\n            }, singleMicSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n              className: \"gx-mt-3\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 59\n              }\n            }, singleMicSection.required && /*#__PURE__*/React.createElement(\"span\", {\n              style: {\n                color: 'red'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 67\n              }\n            }, ' ', \"*\", ' '), singleMicSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n              className: \"gx-bg-dark\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 63\n              }\n            })), /*#__PURE__*/React.createElement(Form.Item, {\n              name: 'mic_recording_uploads',\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 55\n              }\n            }, /*#__PURE__*/React.createElement(MicInputV2\n            // className=\"gx-w-50\"\n            // demoMode\n            , {\n              required: singleMicSection.required,\n              maxColSpan: 6,\n              authToken: http_utils.getAuthToken(),\n              prefixDomain: http_utils.getCDNDomain(),\n              onFilesChanged: files => {\n                onMicFilesChanged(singleMicSection.key, files);\n              },\n              onReadyStatusChanged: isReady => {\n                onMicFileUploaderReadyChange(singleMicSection.key, isReady);\n              },\n              initialFiles: ((_prefillFormData$mic_ = prefillFormData.mic_files) === null || _prefillFormData$mic_ === void 0 ? void 0 : _prefillFormData$mic_[singleMicSection.key]) || [],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 59\n              }\n            })));\n          })), /*#__PURE__*/React.createElement(\"hr\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 39\n            }\n          }));\n        }\n      }] : []), ...(trackLineItemWiseProgress && showLineItemBySelection && canCurrentUserEdit ? [{\n        key: 'selected_line_items',\n        label: 'Line Item Progress',\n        widget: 'select',\n        widgetProps: {\n          mode: 'multiple',\n          maxTagCount: isMobileView() ? 1 : 3,\n          // maxTagTextLength: 20,\n          showSearch: true,\n          optionFilterProp: 'children',\n          placeholder: 'Select line items to track progress',\n          style: {\n            width: '100%'\n          },\n          allowClear: true\n        },\n        options: getLineItemOptions(),\n        onChange: value => {\n          forceUpdate();\n        }\n      }] : []), ...(showLineItemBySelection && trackLineItemWiseProgress && selectedLineItemsFrProgress && selectedLineItemsFrProgress.length > 0 ? [{\n        render: () => /*#__PURE__*/React.createElement(\"div\", {\n          className: \"gx-module-box-content gx-px-3 gx-py-3 wy-show-line-items-by-selection\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 35\n          }\n        }, /*#__PURE__*/React.createElement(FormBuilder, {\n          meta: {\n            fields: getSelectedLineItemWiseProgressMeta(form.getFieldValue('selected_line_items'))\n          },\n          form: form,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 39\n          }\n        }))\n      }] : []), ...(trackLineItemWiseProgress && (!showLineItemBySelection || !canCurrentUserEdit) ? getLineItemWiseProgressMeta() : []), ...(trackLineItemWisePhotos ? [{\n        render: () => {\n          var _getLineItemWiseProgr;\n          return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Row, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 39\n            }\n          }, (_getLineItemWiseProgr = getLineItemWiseProgressMeta()) === null || _getLineItemWiseProgr === void 0 ? void 0 : _getLineItemWiseProgr.map((singleFileSection, index) => {\n            var _prefillFormData$atta7, _prefillFormData$atta8, _prefillFormData$atta9, _prefillFormData$atta0;\n            handleFileSectionRequiredStatus(singleFileSection);\n            return /*#__PURE__*/React.createElement(Col, {\n              xs: 24,\n              md: 24,\n              className: \"gx-pl-0\",\n              key: singleFileSection.key,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 55\n              }\n            }, singleFileSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n              className: \"gx-mt-3\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 63\n              }\n            }, singleFileSection.required && /*#__PURE__*/React.createElement(\"span\", {\n              style: {\n                color: 'red'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 71\n              }\n            }, ' ', \"*\", ' '), singleFileSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n              className: \"gx-bg-dark\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 67\n              }\n            })), /*#__PURE__*/React.createElement(Form.Item, {\n              name: 'file_uploads',\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 59\n              }\n            }, /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Button, {\n              type: \"link\",\n              onClick: () => {\n                setShowUploadedFilesModal(singleFileSection.key);\n                selectFromUploadedFilesClick(sbtskDetailViewData);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 71\n              }\n            }, \"Select from uploaded files\"), /*#__PURE__*/React.createElement(Modal, {\n              title: \"Attachments\",\n              centered: true,\n              visible: singleFileSection.key == showUploadedFilesModal,\n              onOk: () => {\n                var _prefillFormData$atta6;\n                handleOkFrUploadedFilesModal(((_prefillFormData$atta6 = prefillFormData.attachments) === null || _prefillFormData$atta6 === void 0 ? void 0 : _prefillFormData$atta6[singleFileSection.key]) || [], singleFileSection.key);\n              },\n              onCancel: () => setShowUploadedFilesModal(''),\n              width: 1000,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 71\n              }\n            }, loadingSbtskDetailViewData ? /*#__PURE__*/React.createElement(\"div\", {\n              className: \"gx-loader-view gx-loader-position\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 79\n              }\n            }, /*#__PURE__*/React.createElement(CircularProgress, {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 83\n              }\n            })) : sbtskDetailViewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n              className: \"gx-text-red\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 79\n              }\n            }, errorFrloadingSbtskDetailViewData) : /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: getAllMergedAttachments(sbtskDetailViewData, ((_prefillFormData$atta7 = prefillFormData.attachments) === null || _prefillFormData$atta7 === void 0 ? void 0 : _prefillFormData$atta7[singleFileSection.key]) || []),\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 79\n              }\n            })), selectedImage[singleFileSection.key] && /*#__PURE__*/React.createElement(\"div\", {\n              key: index,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 79\n              }\n            }, /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: selectedImage[singleFileSection.key],\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              selectOnly: false,\n              sectionKey: singleFileSection.key,\n              prevUploadedFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta8 = prefillFormData.attachments) === null || _prefillFormData$atta8 === void 0 ? void 0 : _prefillFormData$atta8[singleFileSection.key],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 83\n              }\n            }))\n            // ))\n            ), /*#__PURE__*/React.createElement(S3Uploader\n            // className=\"gx-w-50\"\n            // demoMode\n            , {\n              required: singleFileSection.required,\n              maxColSpan: 4,\n              authToken: http_utils.getAuthToken(),\n              prefixDomain: http_utils.getCDNDomain(),\n              customFilePrefixName: getCustomPrefixNameFrUploadingFiles(),\n              totalFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta9 = prefillFormData.attachments) === null || _prefillFormData$atta9 === void 0 ? void 0 : _prefillFormData$atta9[singleFileSection.key],\n              onFilesChanged: (files, deletedFileUrl) => {\n                onFilesChanged(singleFileSection.key, files, deletedFileUrl);\n              },\n              onReadyStatusChanged: isReady => {\n                onFileUploaderReadyChange(singleFileSection.key, isReady);\n              },\n              initialFiles: ((_prefillFormData$atta0 = prefillFormData.attachments) === null || _prefillFormData$atta0 === void 0 ? void 0 : _prefillFormData$atta0[singleFileSection.key]) || [],\n              customPreviewHeight: \"100%\",\n              customFileIconMaxWidth: \"40px\",\n              compConfig: {\n                name: 'daily-request-status-editor-sp'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 63\n              }\n            })));\n          })), /*#__PURE__*/React.createElement(\"hr\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1247,\n              columnNumber: 39\n            }\n          }));\n        }\n      }] : []), ...dailyUpdateFormMeta(), {\n        render: () => {\n          var _getFormFileMeta, _getFormMicMeta;\n          return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Row, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 29\n            }\n          }, (_getFormFileMeta = getFormFileMeta()) === null || _getFormFileMeta === void 0 ? void 0 : _getFormFileMeta.map((singleFileSection, index) => {\n            var _prefillFormData$atta10, _prefillFormData$atta11, _prefillFormData$atta12, _prefillFormData$atta13;\n            handleFileSectionRequiredStatus(singleFileSection);\n            return /*#__PURE__*/React.createElement(Col, {\n              xs: 24,\n              md: 24,\n              className: \"gx-pl-0\",\n              key: singleFileSection.key,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 45\n              }\n            }, singleFileSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n              className: \"gx-mt-3\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1272,\n                columnNumber: 53\n              }\n            }, singleFileSection.required && /*#__PURE__*/React.createElement(\"span\", {\n              style: {\n                color: 'red'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 61\n              }\n            }, ' ', \"*\", ' '), singleFileSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n              className: \"gx-bg-dark\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1286,\n                columnNumber: 57\n              }\n            })), /*#__PURE__*/React.createElement(Form.Item, {\n              name: 'file_uploads',\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 49\n              }\n            }, /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Button, {\n              type: \"link\",\n              onClick: () => {\n                setShowUploadedFilesModal(singleFileSection.key);\n                selectFromUploadedFilesClick(sbtskDetailViewData);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1294,\n                columnNumber: 61\n              }\n            }, \"Select from uploaded files\"), /*#__PURE__*/React.createElement(Modal, {\n              title: \"Attachments\",\n              centered: true,\n              visible: singleFileSection.key == showUploadedFilesModal,\n              onOk: () => {\n                var _prefillFormData$atta1;\n                handleOkFrUploadedFilesModal(((_prefillFormData$atta1 = prefillFormData.attachments) === null || _prefillFormData$atta1 === void 0 ? void 0 : _prefillFormData$atta1[singleFileSection.key]) || [], singleFileSection.key);\n              },\n              onCancel: () => setShowUploadedFilesModal(''),\n              width: 1000,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1309,\n                columnNumber: 61\n              }\n            }, loadingSbtskDetailViewData ? /*#__PURE__*/React.createElement(\"div\", {\n              className: \"gx-loader-view gx-loader-position\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 69\n              }\n            }, /*#__PURE__*/React.createElement(CircularProgress, {\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 73\n              }\n            })) : sbtskDetailViewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n              className: \"gx-text-red\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 69\n              }\n            }, errorFrloadingSbtskDetailViewData) : /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: getAllMergedAttachments(sbtskDetailViewData, ((_prefillFormData$atta10 = prefillFormData.attachments) === null || _prefillFormData$atta10 === void 0 ? void 0 : _prefillFormData$atta10[singleFileSection.key]) || []),\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 69\n              }\n            })), selectedImage[singleFileSection.key] && /*#__PURE__*/React.createElement(\"div\", {\n              key: index,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 69\n              }\n            }, /*#__PURE__*/React.createElement(AttachmentsPreview, {\n              attachments: selectedImage[singleFileSection.key],\n              noteSelectedFilesBySection: files => {\n                noteSelectedFilesBySection(singleFileSection.key, files);\n              },\n              selectOnly: false,\n              sectionKey: singleFileSection.key,\n              prevUploadedFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta11 = prefillFormData.attachments) === null || _prefillFormData$atta11 === void 0 ? void 0 : _prefillFormData$atta11[singleFileSection.key],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 73\n              }\n            }))\n            // ))\n            ), /*#__PURE__*/React.createElement(S3Uploader\n            // className=\"gx-w-50\"\n            // demoMode\n            , {\n              required: singleFileSection.required,\n              maxColSpan: 4,\n              authToken: http_utils.getAuthToken(),\n              prefixDomain: http_utils.getCDNDomain(),\n              customFilePrefixName: getCustomPrefixNameFrUploadingFiles(),\n              totalFiles: prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$atta12 = prefillFormData.attachments) === null || _prefillFormData$atta12 === void 0 ? void 0 : _prefillFormData$atta12[singleFileSection.key],\n              onFilesChanged: (files, deletedFileUrl) => {\n                onFilesChanged(singleFileSection.key, files, deletedFileUrl);\n              },\n              onReadyStatusChanged: isReady => {\n                onFileUploaderReadyChange(singleFileSection.key, isReady);\n              },\n              initialFiles: ((_prefillFormData$atta13 = prefillFormData.attachments) === null || _prefillFormData$atta13 === void 0 ? void 0 : _prefillFormData$atta13[singleFileSection.key]) || [],\n              customPreviewHeight: \"100%\",\n              customFileIconMaxWidth: \"40px\",\n              compConfig: {\n                name: 'daily-request-status-editor'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1412,\n                columnNumber: 53\n              }\n            })));\n          }), (_getFormMicMeta = getFormMicMeta()) === null || _getFormMicMeta === void 0 ? void 0 : _getFormMicMeta.map((singleMicSection, index) => {\n            var _prefillFormData$mic_2;\n            return /*#__PURE__*/React.createElement(Col, {\n              xs: 24,\n              md: 24,\n              className: \"gx-pl-0\",\n              key: singleMicSection.key,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1468,\n                columnNumber: 45\n              }\n            }, singleMicSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n              className: \"gx-mt-3\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1476,\n                columnNumber: 53\n              }\n            }, singleMicSection.required && /*#__PURE__*/React.createElement(\"span\", {\n              style: {\n                color: 'red'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1478,\n                columnNumber: 61\n              }\n            }, ' ', \"*\", ' '), singleMicSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n              className: \"gx-bg-dark\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1488,\n                columnNumber: 57\n              }\n            })), /*#__PURE__*/React.createElement(Form.Item, {\n              name: 'mic_recording_uploads',\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1491,\n                columnNumber: 49\n              }\n            }, /*#__PURE__*/React.createElement(MicInputV2\n            // className=\"gx-w-50\"\n            // demoMode\n            , {\n              required: singleMicSection.required,\n              maxColSpan: 6,\n              authToken: http_utils.getAuthToken(),\n              prefixDomain: http_utils.getCDNDomain(),\n              onFilesChanged: files => {\n                onMicFilesChanged(singleMicSection.key, files);\n              },\n              onReadyStatusChanged: isReady => {\n                onMicFileUploaderReadyChange(singleMicSection.key, isReady);\n              },\n              initialFiles: ((_prefillFormData$mic_2 = prefillFormData.mic_files) === null || _prefillFormData$mic_2 === void 0 ? void 0 : _prefillFormData$mic_2[singleMicSection.key]) || [],\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 1496,\n                columnNumber: 53\n              }\n            })));\n          })), /*#__PURE__*/React.createElement(\"hr\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1535,\n              columnNumber: 29\n            }\n          }));\n        }\n      }, {\n        key: 'day_progress',\n        label: 'Day progress',\n        widget: SliderWidget,\n        widgetProps: {\n          tooltip: {\n            formatter: formatter\n          },\n          onChange: value => {\n            if (srvcConfigData.daily_progress_update_mode == 'strict_mode') {\n              checkFrProgressionAcrossRange(value, day, 'day_progress');\n            }\n          },\n          disabled: trackLineItemWiseProgress\n        }\n      }]\n    };\n  };\n  const onFormValueChange = () => {\n    setIsFormChanged(true);\n  };\n\n  /**\r\n   * Extracts and maps the progress values for all line items from the form data.\r\n   *\r\n   * This function determines whether the service provider tab is active and selects the appropriate\r\n   * line items (`sp_line_items` or `line_items`) from the request data. It uses the service configuration\r\n   * (`srvc_type_line_item_config`) to iterate through the defined line item groups, and fetches progress\r\n   * values for each item using their `input_table_id` as a unique key.\r\n   *\r\n   * @returns {Object} A map of progress field keys (`progress_<input_table_id>`) to their corresponding values\r\n   *                   from the form. If the form value is not set, the value will be `null`.\r\n   *\r\n   * @example\r\n   * {\r\n   *   progress_1234: 50,\r\n   *   progress_5678: 75\r\n   * }\r\n   */\n  const getAllLineItemProgressWithValues = () => {\n    var _reqData$form_data7, _reqData$form_data7$s, _reqData$form_data8, _reqData$form_data8$l;\n    let progressMap = {};\n    let reqLineItemsFormData = isSrvcPrvdrTab ? reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data7 = reqData.form_data) === null || _reqData$form_data7 === void 0 ? void 0 : (_reqData$form_data7$s = _reqData$form_data7.sp_line_items) === null || _reqData$form_data7$s === void 0 ? void 0 : _reqData$form_data7$s.form_data : reqData === null || reqData === void 0 ? void 0 : (_reqData$form_data8 = reqData.form_data) === null || _reqData$form_data8 === void 0 ? void 0 : (_reqData$form_data8$l = _reqData$form_data8.line_items) === null || _reqData$form_data8$l === void 0 ? void 0 : _reqData$form_data8$l.form_data;\n    if (!reqLineItemsFormData) return progressMap;\n    let lineItemConfig = JSON.parse(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.srvc_type_line_item_config);\n    Object.keys(lineItemConfig).forEach(lineItemGrpKey => {\n      let itemsInGroup = reqLineItemsFormData[lineItemGrpKey] || [];\n      itemsInGroup.forEach(item => {\n        var _form$getFieldValue;\n        const key = `progress_${item.input_table_id}`;\n        const value = (_form$getFieldValue = form.getFieldValue(key)) !== null && _form$getFieldValue !== void 0 ? _form$getFieldValue : null; // you can default to 0 if needed\n        progressMap[key] = value;\n      });\n    });\n    return progressMap;\n  };\n  const onSubmit = values => {\n    var _existingDayStatusUpd, _existingDayStatusUpd2;\n    setIsFormSubmitting(true);\n    setError(undefined);\n    let existingDayStatusUpdates = existingDailyStatusObj[day];\n    if (existingDayStatusUpdates == undefined) {\n      existingDayStatusUpdates = {};\n    }\n    existingDayStatusUpdates.update_day_and_time = getCurrentDateAndTimeFrDisplay();\n    let existingFileOrSelectedNewFile = {\n      ...((_existingDayStatusUpd = existingDayStatusUpdates) === null || _existingDayStatusUpd === void 0 ? void 0 : _existingDayStatusUpd.attachments),\n      ...filesBySection\n    };\n    let existingMicRecordingsOrSelectedNewRecordings = {\n      ...((_existingDayStatusUpd2 = existingDayStatusUpdates) === null || _existingDayStatusUpd2 === void 0 ? void 0 : _existingDayStatusUpd2.mic_files),\n      ...micRecordingsBySection\n    };\n    values['attachments'] = existingFileOrSelectedNewFile;\n    values['mic_files'] = existingMicRecordingsOrSelectedNewRecordings;\n\n    // save all the line items progress data if selected_line_items config enabled\n    if (form.getFieldValue('selected_line_items') && (srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_track_line_item_progress) == 'Yes') {\n      const allLineItemProgressMap = getAllLineItemProgressWithValues();\n      Object.entries(allLineItemProgressMap).forEach(([key, value]) => {\n        if (!(key in values)) {\n          values[key] = value !== null && value !== void 0 ? value : 0; // Use existing value, or 0 if null/undefined\n        }\n      });\n    }\n    let mergedStatusUpdateFrDay = {\n      ...existingDayStatusUpdates,\n      ...values // newDayStatusUpdateValues\n    };\n    existingDailyStatusObj[day] = mergedStatusUpdateFrDay;\n    let daily_status_updates_key_name = ConfigHelpers.isServiceProvider() ? 'sp_daily_status_updates' : 'daily_status_updates';\n    var params = {\n      [daily_status_updates_key_name]: {\n        ...existingDailyStatusObj\n      },\n      updatedDay: day\n    };\n    // console.log('params',params)\n    const onComplete = resp => {\n      setIsFormSubmitting(false);\n      setError(undefined);\n      if (onChange) {\n        onChange(day);\n      }\n      // message.info(JSON.stringify(resp.data));\n    };\n    const onError = error => {\n      setIsFormSubmitting(false);\n      setError(http_utils.decodeErrorToMessage(error));\n    };\n    var url = urlToSubmitFrUpdates;\n    http_utils.performPutCall(url, params, onComplete, onError);\n  };\n  const getIssueFileMeta = () => {\n    let dailyUpdateIssueFileMeta = [];\n    dailyUpdateIssueFileMeta = decodeFileSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_issue_form_fields);\n    return dailyUpdateIssueFileMeta;\n  };\n  const getFormIssueMicMeta = () => {\n    let dailyUpdateIssueMicMeta = [];\n    dailyUpdateIssueMicMeta = decodeMicSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_issue_form_fields);\n    return dailyUpdateIssueMicMeta;\n  };\n  const getFormFileMeta = () => {\n    let dailyUpdateFileMeta = [];\n    dailyUpdateFileMeta = isSrvcPrvdrTab ? decodeFileSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.sp_daily_update_form_fields) : decodeFileSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_form_fields);\n    return dailyUpdateFileMeta;\n  };\n  const getFormMicMeta = () => {\n    let dailyUpdateMicMeta = [];\n    dailyUpdateMicMeta = isSrvcPrvdrTab ? decodeMicSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.sp_daily_update_form_fields) : decodeMicSectionsFrmJson(srvcConfigData === null || srvcConfigData === void 0 ? void 0 : srvcConfigData.daily_update_form_fields);\n    return dailyUpdateMicMeta;\n  };\n  const onFilesChanged = (section, files, deletedFileUrl = undefined) => {\n    onFilesChangedFn(section, files, filesBySection, setFilesBySection, onFormValueChange, deletedFileUrl);\n  };\n  const onMicFilesChanged = (section, files) => {\n    var _micRecordingsBySecti;\n    let newFilesBySection = _.cloneDeep(micRecordingsBySection);\n    let newFiles = [];\n    if (micRecordingsBySection[section] && (files === null || files === void 0 ? void 0 : files.length) >= ((_micRecordingsBySecti = micRecordingsBySection[section]) === null || _micRecordingsBySecti === void 0 ? void 0 : _micRecordingsBySecti.length)) {\n      newFiles = files.filter(singleFile => !micRecordingsBySection[section].includes(singleFile));\n    } else {\n      newFiles = files;\n    }\n    newFilesBySection[section] = micRecordingsBySection[section] && files.length >= micRecordingsBySection[section].length ? [...micRecordingsBySection[section], ...newFiles] : files;\n    //    console.log('newfile',newFilesBySection)\n    setMicRecordingsBySection(newFilesBySection);\n    onFormValueChange();\n  };\n  const onFileUploaderReadyChange = (section, isReady) => {\n    let newSectionWiseReady = sectionWiseUploaderReady;\n    newSectionWiseReady[section] = isReady;\n    setSectionWiseUploaderReady(newSectionWiseReady);\n    setAllFileUploadersReady(getAllFileUploadersReady());\n  };\n  const onMicFileUploaderReadyChange = (section, isReady) => {\n    let newSectionWiseReady = sectionWiseMicUploaderReady;\n    newSectionWiseReady[section] = isReady;\n    setSectionWiseMicUploaderReady(newSectionWiseReady);\n    setAllMicRecordingsUploadersReady(getAllMicRecordingsUploadersReady());\n  };\n  const getAllFileUploadersReady = () => {\n    let notReady = false;\n    Object.keys(sectionWiseUploaderReady).map(section => {\n      if (!sectionWiseUploaderReady[section]) {\n        notReady = true;\n      }\n    });\n    return !notReady;\n  };\n  const getAllMicRecordingsUploadersReady = () => {\n    let notReady = false;\n    Object.keys(sectionWiseMicUploaderReady).map(section => {\n      if (!sectionWiseMicUploaderReady[section]) {\n        notReady = true;\n      }\n    });\n    return !notReady;\n  };\n  let prefillFormData = convertDateFieldsToMoments(existingDailyStatusObj[day], meta().fields) || {};\n  // TODO convertdatefields to moment\n  //set no remarks by default\n  const remark = form === null || form === void 0 ? void 0 : form.getFieldValue('day_remarks');\n  if ((prefillFormData === null || prefillFormData === void 0 ? void 0 : prefillFormData.day_remarks) == undefined && remark == undefined) {\n    form.setFieldsValue({\n      day_remarks: 'No remarks'\n    });\n  }\n  const getMeta = (fields, prefillFormData) => {\n    if (!canCurrentUserEdit) {\n      const viewMeta = getViewModeMetaFrFormMeta(fields, prefillFormData);\n      if (viewMeta.length > 0) {\n        return viewMeta;\n      } else {\n        return [{\n          render: () => /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1806,\n              columnNumber: 41\n            }\n          }, \"No updates added\")\n        }];\n      }\n    } else {\n      return meta();\n    }\n  };\n\n  // if line item deleted after updating daily update form then delete that line item id from daily update form (line item progress selection)\n  const updatedSelectedLineItems = prefillFormData === null || prefillFormData === void 0 ? void 0 : (_prefillFormData$sele = prefillFormData.selected_line_items) === null || _prefillFormData$sele === void 0 ? void 0 : _prefillFormData$sele.filter(singleLineItem => {\n    return getLineItemOptions().some(option => option.value === singleLineItem);\n  });\n  prefillFormData.selected_line_items = updatedSelectedLineItems;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-mt-3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1824,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Form, {\n    initialValues: prefillFormData,\n    form: form,\n    layout: \"vertical\",\n    onFinish: onSubmit,\n    onValuesChange: onFormValueChange,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1825,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    meta: getMeta(meta().fields, prefillFormData),\n    form: form,\n    viewMode: !canCurrentUserEdit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1832,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-mt-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1838,\n      columnNumber: 17\n    }\n  }, (!allFileUploadersReady || !allMicRecordingsUploadersReady) && /*#__PURE__*/React.createElement(Spin, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1840,\n      columnNumber: 61\n    }\n  }), canCurrentUserEdit && /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    htmlType: \"submit\",\n    disabled: isFormSubmitting || !allFileUploadersReady || !isFormChanged || !allMicRecordingsUploadersReady,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1842,\n      columnNumber: 25\n    }\n  }, \"Save\"), isFormSubmitting ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-loader-view gx-loader-position\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1856,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(CircularProgress, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1857,\n      columnNumber: 29\n    }\n  })) : null, error ? /*#__PURE__*/React.createElement(\"p\", {\n    className: \"gx-text-red\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1860,\n      columnNumber: 30\n    }\n  }, error) : null)));\n};\nconst onFilesChangedFn = (section, files, filesBySection, setFilesBySection, onFormValueChange, deletedFileUrl) => {\n  var _filesBySection$secti;\n  let newFilesBySection = _.cloneDeep(filesBySection);\n  let newFiles = [];\n  if (filesBySection[section] && (files === null || files === void 0 ? void 0 : files.length) >= ((_filesBySection$secti = filesBySection[section]) === null || _filesBySection$secti === void 0 ? void 0 : _filesBySection$secti.length)) {\n    newFiles = files.filter(singleFile => !filesBySection[section].includes(singleFile));\n  } else {\n    if (filesBySection[section]) {\n      newFiles = [...new Set(files.concat(filesBySection[section]))];\n    } else {\n      newFiles = files;\n    }\n  }\n\n  // Get the existing files for the specified section from the filesBySection object,\n  // or initialize an empty array if no files exist for the section.\n  const existingFiles = filesBySection[section] || [];\n\n  // Combine the current files (passed as an argument) with the existing files,\n  // and use a Set to ensure unique values in the resulting array.\n  // Create a new Set with deep-copied objects\n  const combinedFiles = [...new Set([...files, ...existingFiles])];\n\n  // Check if there are existing files for the section and if the number of files\n  // passed as an argument is greater than or equal to the number of existing files.\n  // If true, update the files for the section with the combined unique files;\n  // otherwise, use the files passed as an argument.\n  newFilesBySection[section] = filesBySection[section] && files.length >= existingFiles.length ? combinedFiles : files;\n\n  // If deletedFileUrl exits then remove from combinedFiles\n  if (deletedFileUrl) {\n    newFilesBySection[section] = combinedFiles === null || combinedFiles === void 0 ? void 0 : combinedFiles.filter(singleFile => singleFile !== deletedFileUrl);\n  }\n  setFilesBySection(newFilesBySection);\n  onFormValueChange();\n};\n\n// Export functions and components\nexport { onFilesChangedFn };\nexport default DailyReqStatusEditor;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Col", "Collapse", "Form", "message", "Modal", "Row", "Spin", "FormBuilder", "_", "values", "React", "useEffect", "useState", "CircularProgress", "decodeFieldsMetaFrmJson", "decodeFileSectionsFrmJson", "decodeMicSectionsFrmJson", "S3Uploader", "SliderW<PERSON>t", "ConfigHelpers", "convertDateFieldsToMoments", "convertMomentToDateString", "convertMomentToLocalDateString", "convertMomentToLocalDateString1", "convertMomentToMonthDateAndYearFormat", "convertValuesOfObjToMoment", "getCurrentDateAndTimeFrDisplay", "getValueDataFrmFormMeta", "getViewModeMetaFrFormMeta", "isMobile<PERSON>iew", "parseFormulaToString", "parseFormulaToValue", "setRemarkFieldAsNoRemarkIfEmpty", "http_utils", "getCustomPrefixNameFrUploadingFiles", "AttachmentsPreview", "getAllMergedAttachments", "MicInputV2", "moment", "sbtskDetailsUrl", "formatter", "value", "DailyReqStatusEditor", "editorDayDetails", "srvcConfigData", "reqData", "onChange", "urlToSubmitFrUpdates", "isSrvcPrvdrTab", "_prefillFormData$sele", "day", "assignee_date", "srvcFormData", "form_data", "existingDailyStatusObj", "sp_daily_status_updates", "daily_status_updates", "form", "useForm", "error", "setError", "undefined", "errorFrloadingSbtskDetailViewData", "setErrorFrLoadingSbtskDetailViewData", "forceUpdate", "useForceUpdate", "isFormSubmitting", "setIsFormSubmitting", "fileSections", "setFileSections", "filesBySection", "setFilesBySection", "sectionWiseUploaderReady", "setSectionWiseUploaderReady", "micRecordingsBySection", "setMicRecordingsBySection", "sectionWiseMicUploaderReady", "setSectionWiseMicUploaderReady", "allFileUploadersReady", "setAllFileUploadersReady", "allMicRecordingsUploadersReady", "setAllMicRecordingsUploadersReady", "showUploadedFilesModal", "setShowUploadedFilesModal", "sbtskDetailViewData", "setSbtskDetailViewData", "loadingSbtskDetailViewData", "setLoadingSbtskDetailViewData", "isFormChanged", "setIsFormChanged", "isErrorShown", "setIsErrorShown", "selectedImage", "setSelectedImage", "selectedFilesBySection", "rolesWhoHaveEditAccess", "daily_update_who_can_edit", "canCurrentUserEdit", "doesUserHaveOneOfTheRole", "timeoutInstance", "setSlidersInitialValue", "dailyUpdateIssueFormMeta", "daily_update_issue_form_fields", "dailyUpdateFormMeta", "sp_daily_update_form_fields", "daily_update_form_fields", "lineItemWiseProgressFields", "getLineItemWiseProgressMeta", "for<PERSON>ach", "singleLineItemMeta", "lowerLimit", "getLowerLimitFrSlider", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lowerLimitDailyProgress", "day_progress", "findclosestBeforeDate", "date", "allDates", "Object", "keys", "closestBefore", "indexIfDateExists", "findIndex", "length", "<PERSON><PERSON><PERSON>", "prevDateData", "_prevDateData", "calculateDayProgress", "upperLimit", "countOfItems", "totalProgOfItems", "lineItemProg", "getFieldValue", "prog", "Math", "round", "checkFrProgressionAcrossRange", "newVal", "sortedExistingDailyStatusObj", "sort", "a", "b", "dates", "copyOfExisitingDailyStatusObj", "cloneDeepWith", "days", "firstDay", "previousValue", "msgCount", "map", "clearTimeout", "setTimeout", "_sortedExistingDailyS", "_reqData$form_data", "_reqData$form_data$sp", "_reqData$form_data2", "_reqData$form_data2$l", "lineItemsWiseMeta", "reqLineItemsFormData", "sp_line_items", "line_items", "lineItemConfig", "JSON", "parse", "srvc_type_line_item_config", "lineItemGrpKey", "groupConfig", "nameFieldF<PERSON><PERSON>", "name_field_formula", "fields", "idVsLabelMapping", "singleFieldMeta", "label", "requestLineItemsInGrp", "Array", "isArray", "singleLineItemInGrp", "index", "valueDataFrLineItem", "itemProgressKey", "input_table_id", "push", "title", "widget", "widgetProps", "tooltip", "daily_progress_update_mode", "getSelectedLineItemWiseProgressMeta", "selectedLineItemIds", "_reqData$form_data3", "_reqData$form_data3$s", "_reqData$form_data4", "_reqData$form_data4$l", "lineItemId", "includes", "className", "handleOkFrUploadedFilesModal", "initialFiles", "removedCount", "filter", "element", "newSelectedFilesBySection", "cloneDeep", "Set", "concat", "onFormValueChange", "selectFromUploadedFilesClick", "params", "onComplete", "resp", "data", "onError", "decodeErrorToMessage", "url", "id", "performGetCall", "noteSelectedFilesBySection", "fileSection", "files", "_selectedFilesBySecti", "_selectedFilesBySecti2", "indexOf", "splice", "hasFacedIssueForDay", "handleFileSectionRequiredStatus", "singleFileSection", "_selectedImage$single", "filesSelected", "required", "getLineItemOptions", "_reqData$form_data5", "_reqData$form_data5$s", "_reqData$form_data6", "_reqData$form_data6$l", "options", "lineItemsData", "e", "console", "log", "singleLineItem", "valueData", "meta", "dailyUpdateWillHaveIssues", "daily_update_will_have_issues", "trackLineItemWiseProgress", "daily_update_track_line_item_progress", "showLineItemBySelection", "show_line_item_by_selection", "trackLineItemWisePhotos", "daily_update_dynamic_line_item_wise_files", "selectedLineItemsFrProgress", "formItemLayout", "render", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_getIssueFileMeta", "_getFormIssueMicMeta", "Fragment", "getIssueFileMeta", "_prefillFormData$atta2", "_prefillFormData$atta3", "_prefillFormData$atta4", "_prefillFormData$atta5", "xs", "md", "style", "color", "<PERSON><PERSON>", "name", "type", "onClick", "centered", "visible", "onOk", "_prefillFormData$atta", "prefillFormData", "attachments", "onCancel", "width", "selectOnly", "sectionKey", "prevUploadedFiles", "maxColSpan", "authToken", "getAuthToken", "prefixDomain", "getCDNDomain", "customFilePrefixName", "totalFiles", "onFilesChanged", "deletedFileUrl", "onReadyStatusChanged", "isReady", "onFileUploaderReadyChange", "customPreviewHeight", "customFileIconMaxWidth", "compConfig", "getFormIssueMicMeta", "singleMicSection", "_prefillFormData$mic_", "onMicFilesChanged", "onMicFileUploaderReadyChange", "mic_files", "mode", "maxTag<PERSON>ount", "showSearch", "optionFilterProp", "placeholder", "allowClear", "_getLineItemWiseProgr", "_prefillFormData$atta7", "_prefillFormData$atta8", "_prefillFormData$atta9", "_prefillFormData$atta0", "_prefillFormData$atta6", "_getFormFileMeta", "_getFormMicMeta", "getFormFileMeta", "_prefillFormData$atta10", "_prefillFormData$atta11", "_prefillFormData$atta12", "_prefillFormData$atta13", "_prefillFormData$atta1", "getFormMicMeta", "_prefillFormData$mic_2", "disabled", "getAllLineItemProgressWithValues", "_reqData$form_data7", "_reqData$form_data7$s", "_reqData$form_data8", "_reqData$form_data8$l", "progressMap", "itemsInGroup", "item", "_form$getFieldValue", "onSubmit", "_existingDayStatusUpd", "_existingDayStatusUpd2", "existingDayStatusUpdates", "update_day_and_time", "existingFileOrSelectedNewFile", "existingMicRecordingsOrSelectedNewRecordings", "allLineItemProgressMap", "entries", "mergedStatusUpdateFrDay", "daily_status_updates_key_name", "isServiceProvider", "updatedDay", "performPutCall", "dailyUpdateIssueFileMeta", "dailyUpdateIssueMicMeta", "dailyUpdateFileMeta", "dailyUpdateMicMeta", "section", "onFilesChangedFn", "_micRecordingsBySecti", "newFilesBySection", "newFiles", "singleFile", "newSectionWiseReady", "getAllFileUploadersReady", "getAllMicRecordingsUploadersReady", "notReady", "remark", "day_remarks", "getMeta", "viewMeta", "updatedSelectedLineItems", "selected_line_items", "some", "option", "initialValues", "layout", "onFinish", "onValuesChange", "viewMode", "htmlType", "_filesBySection$secti", "existingFiles", "combinedFiles"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/services/project/daily-status/DailyReqStatusEditor.js"], "sourcesContent": ["import { <PERSON><PERSON>, Col, Collapse, Form, message, Modal, Row, Spin } from 'antd';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport _, { values } from 'lodash';\r\nimport React, { useEffect, useState } from 'react';\r\nimport CircularProgress from '../../../../components/CircularProgress';\r\nimport {\r\n    decodeFieldsMetaFrmJson,\r\n    decodeFileSectionsFrmJson,\r\n    decodeMicSectionsFrmJson,\r\n} from '../../../../components/wify-utils/FieldCreator/helpers';\r\nimport S3Uploader from '../../../../components/wify-utils/S3Uploader/S3Uploader';\r\nimport SliderWidget from '../../../../components/wify-utils/SliderWidget';\r\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\r\nimport {\r\n    convertDateFieldsToMoments,\r\n    convertMomentToDateString,\r\n    convertMomentToLocalDateString,\r\n    convertMomentToLocalDateString1,\r\n    convertMomentToMonthDateAndYearFormat,\r\n    convertValuesOfObjToMoment,\r\n    getCurrentDateAndTimeFrDisplay,\r\n    getValueDataFrmFormMeta,\r\n    getViewModeMetaFrFormMeta,\r\n    isMobileView,\r\n    parseFormulaToString,\r\n    parseFormulaToValue,\r\n    setRemarkFieldAsNoRemarkIfEmpty,\r\n} from '../../../../util/helpers';\r\nimport http_utils from '../../../../util/http_utils';\r\nimport { getCustomPrefixNameFrUploadingFiles } from '../../../../util/helpers';\r\nimport AttachmentsPreview from '../deployment/AttachmentPreview';\r\nimport { getAllMergedAttachments } from '../../helpers';\r\nimport MicInputV2 from '../../../../components/wify-utils/MicInput_v2';\r\nimport moment from 'moment';\r\n\r\nconst sbtskDetailsUrl = '/services/sbtskdetails/attachment';\r\nconst formatter = (value) => `${value}%`;\r\n\r\nconst DailyReqStatusEditor = ({\r\n    editorDayDetails,\r\n    srvcConfigData,\r\n    reqData,\r\n    onChange,\r\n    urlToSubmitFrUpdates,\r\n    isSrvcPrvdrTab,\r\n}) => {\r\n    const day = editorDayDetails.assignee_date;\r\n    const srvcFormData = reqData.form_data;\r\n    let existingDailyStatusObj =\r\n        (isSrvcPrvdrTab\r\n            ? srvcFormData?.sp_daily_status_updates\r\n            : srvcFormData?.daily_status_updates) || {};\r\n    //converting obj to moment at initially because antd date.clone issue raises.\r\n    existingDailyStatusObj[day] = convertValuesOfObjToMoment(\r\n        existingDailyStatusObj[day]\r\n    );\r\n\r\n    const [form] = Form.useForm();\r\n    const [error, setError] = useState(undefined);\r\n    const [\r\n        errorFrloadingSbtskDetailViewData,\r\n        setErrorFrLoadingSbtskDetailViewData,\r\n    ] = useState(undefined);\r\n    const forceUpdate = FormBuilder.useForceUpdate();\r\n    const [isFormSubmitting, setIsFormSubmitting] = useState(false);\r\n    const [fileSections, setFileSections] = useState([]); //Use karna hai\r\n    const [filesBySection, setFilesBySection] = useState({});\r\n    const [sectionWiseUploaderReady, setSectionWiseUploaderReady] = useState(\r\n        {}\r\n    );\r\n    const [micRecordingsBySection, setMicRecordingsBySection] = useState({});\r\n    const [sectionWiseMicUploaderReady, setSectionWiseMicUploaderReady] =\r\n        useState({});\r\n    const [allFileUploadersReady, setAllFileUploadersReady] = useState(true);\r\n    const [allMicRecordingsUploadersReady, setAllMicRecordingsUploadersReady] =\r\n        useState(true);\r\n    const [showUploadedFilesModal, setShowUploadedFilesModal] = useState();\r\n    const [sbtskDetailViewData, setSbtskDetailViewData] = useState(undefined);\r\n    const [loadingSbtskDetailViewData, setLoadingSbtskDetailViewData] =\r\n        useState(false);\r\n    const [isFormChanged, setIsFormChanged] = useState(false);\r\n    const [isErrorShown, setIsErrorShown] = useState(false);\r\n    const [selectedImage, setSelectedImage] = useState({});\r\n    let selectedFilesBySection = {};\r\n    // const prefillsrvcFormData =  26132\r\n    // console.log('DailyReqStatusEditor - srvcConfigData',srvcConfigData);\r\n    // console.log('DailyReqStatusEditor : reqData',reqData);\r\n    const rolesWhoHaveEditAccess =\r\n        srvcConfigData?.daily_update_who_can_edit || [];\r\n    const canCurrentUserEdit = ConfigHelpers?.doesUserHaveOneOfTheRole(\r\n        rolesWhoHaveEditAccess\r\n    );\r\n    let timeoutInstance;\r\n    // console.log('DailyReqStatusEditor - canCurrentUserEdit',canCurrentUserEdit);\r\n\r\n    useEffect(() => {\r\n        forceUpdate();\r\n        setSlidersInitialValue();\r\n    }, []); // Refresh form after first render for dynamic form elements\r\n\r\n    const dailyUpdateIssueFormMeta = () => {\r\n        let dailyUpdateIssueFormMeta = decodeFieldsMetaFrmJson(\r\n            srvcConfigData?.daily_update_issue_form_fields\r\n        );\r\n        return dailyUpdateIssueFormMeta;\r\n    };\r\n\r\n    const dailyUpdateFormMeta = () => {\r\n        let dailyUpdateFormMeta = isSrvcPrvdrTab\r\n            ? decodeFieldsMetaFrmJson(\r\n                  srvcConfigData?.sp_daily_update_form_fields\r\n              )\r\n            : decodeFieldsMetaFrmJson(srvcConfigData?.daily_update_form_fields);\r\n        return dailyUpdateFormMeta;\r\n    };\r\n\r\n    const setSlidersInitialValue = () => {\r\n        let lineItemWiseProgressFields = getLineItemWiseProgressMeta();\r\n\r\n        //set slider value to lower limit when you open it so if you change only one slider and save. All sliders will be saved with current values instead of 0\r\n        lineItemWiseProgressFields.forEach((singleLineItemMeta) => {\r\n            let lowerLimit = getLowerLimitFrSlider(day, singleLineItemMeta.key);\r\n            //ssetting slider if day has already been saved before\r\n            if (\r\n                existingDailyStatusObj[day] != undefined &&\r\n                existingDailyStatusObj[day][singleLineItemMeta.key] == undefined\r\n            ) {\r\n                form.setFieldsValue({\r\n                    [singleLineItemMeta.key]: lowerLimit,\r\n                });\r\n                //setting for day which has never been saved before\r\n            } else if (existingDailyStatusObj[day] == undefined) {\r\n                form.setFieldsValue({\r\n                    [singleLineItemMeta.key]: lowerLimit,\r\n                });\r\n            }\r\n        });\r\n\r\n        //setting day progress sliders just like line items sliders\r\n        let lowerLimitDailyProgress = getLowerLimitFrSlider(\r\n            day,\r\n            'day_progress'\r\n        );\r\n        if (\r\n            existingDailyStatusObj[day] != undefined &&\r\n            existingDailyStatusObj[day]['day_progress'] == undefined\r\n        ) {\r\n            form.setFieldsValue({\r\n                day_progress: lowerLimitDailyProgress,\r\n            });\r\n        } else if (existingDailyStatusObj[day] == undefined) {\r\n            form.setFieldsValue({\r\n                day_progress: lowerLimitDailyProgress,\r\n            });\r\n        }\r\n    };\r\n\r\n    //find closest before date so lower limit can be found to set the initial value of the slider\r\n    const findclosestBeforeDate = (date) => {\r\n        let allDates = Object.keys(existingDailyStatusObj);\r\n\r\n        let closestBefore = null;\r\n        let indexIfDateExists = allDates.findIndex(function (key) {\r\n            return key === date;\r\n        });\r\n        if (date > allDates[allDates.length - 1]) {\r\n            closestBefore = allDates[allDates.length - 1];\r\n            return closestBefore;\r\n        } else if (date <= allDates[0]) {\r\n            closestBefore = 0;\r\n            return closestBefore;\r\n        }\r\n\r\n        closestBefore =\r\n            allDates[\r\n                allDates.findIndex(function (key) {\r\n                    return key > date;\r\n                }) - 1\r\n            ];\r\n\r\n        if (indexIfDateExists >= 1) {\r\n            closestBefore = allDates[indexIfDateExists - 1];\r\n        }\r\n\r\n        return closestBefore;\r\n    };\r\n\r\n    //lower limit for setting sliders in useeffect\r\n    const getLowerLimitFrSlider = (day, fieldKey) => {\r\n        let closestBefore = findclosestBeforeDate(day);\r\n        let prevDateData;\r\n\r\n        let lowerLimit = 0;\r\n\r\n        if (closestBefore) {\r\n            prevDateData = existingDailyStatusObj[closestBefore];\r\n            lowerLimit = prevDateData?.[fieldKey];\r\n        }\r\n        return lowerLimit;\r\n    };\r\n\r\n    const calculateDayProgress = (value, fieldKey, lowerLimit, upperLimit) => {\r\n        let countOfItems = 0;\r\n        let totalProgOfItems = 0;\r\n        const lineItemWiseProgressFields = getLineItemWiseProgressMeta();\r\n        lineItemWiseProgressFields.forEach((singleLineItemMeta) => {\r\n            let lineItemProg = form.getFieldValue(singleLineItemMeta.key) || 0;\r\n            totalProgOfItems = totalProgOfItems + lineItemProg;\r\n            countOfItems++;\r\n        });\r\n        const prog = Math.round(totalProgOfItems / countOfItems);\r\n        form.setFieldsValue({\r\n            day_progress: prog,\r\n        });\r\n    };\r\n\r\n    const checkFrProgressionAcrossRange = (newVal, day, fieldKey) => {\r\n        //creating a sorted copy of existingdailyobj to perform operations\r\n        const sortedExistingDailyStatusObj = {};\r\n\r\n        Object.keys(existingDailyStatusObj)\r\n            .sort(\r\n                (a, b) => existingDailyStatusObj[a] - existingDailyStatusObj[b]\r\n            )\r\n            .forEach((key) => {\r\n                sortedExistingDailyStatusObj[key] = existingDailyStatusObj[key];\r\n            });\r\n\r\n        let dates = Object.keys(sortedExistingDailyStatusObj) || [];\r\n        if (dates.length == 0) {\r\n            return;\r\n        }\r\n\r\n        // sort the existing dail status obj\r\n        let copyOfExisitingDailyStatusObj = _.cloneDeepWith(\r\n            sortedExistingDailyStatusObj\r\n        );\r\n        if (copyOfExisitingDailyStatusObj[day] == undefined) {\r\n            copyOfExisitingDailyStatusObj[day] = {};\r\n        }\r\n        copyOfExisitingDailyStatusObj[day][fieldKey] = newVal;\r\n\r\n        // Loop on the days serially and get the first day at which the progression fails\r\n\r\n        let days = Object.keys(copyOfExisitingDailyStatusObj);\r\n        days.sort();\r\n\r\n        let firstDay = days[0];\r\n        let previousValue = copyOfExisitingDailyStatusObj[firstDay][fieldKey];\r\n\r\n        let msgCount = 0;\r\n\r\n        days.map((date) => {\r\n            if (copyOfExisitingDailyStatusObj[date][fieldKey] < previousValue) {\r\n                if (!isErrorShown) {\r\n                    message.error(\r\n                        `${date} progress is lower than previous date, please fix this first before update`\r\n                    );\r\n                    setIsErrorShown(true);\r\n                }\r\n                msgCount++;\r\n            }\r\n            previousValue = copyOfExisitingDailyStatusObj[date][fieldKey];\r\n        });\r\n        if (timeoutInstance) {\r\n            clearTimeout(timeoutInstance);\r\n        }\r\n        timeoutInstance = setTimeout(() => {\r\n            if (msgCount > 0) {\r\n                form.setFieldsValue({\r\n                    [fieldKey]:\r\n                        sortedExistingDailyStatusObj?.[day]?.[fieldKey] ||\r\n                        getLowerLimitFrSlider(day, fieldKey),\r\n                });\r\n                setIsErrorShown(false);\r\n            } else {\r\n                form.setFieldsValue({\r\n                    [fieldKey]: newVal,\r\n                });\r\n            }\r\n            calculateDayProgress(newVal, fieldKey);\r\n        }, 500);\r\n    };\r\n\r\n    const getLineItemWiseProgressMeta = () => {\r\n        let lineItemsWiseMeta = [];\r\n        let reqLineItemsFormData = isSrvcPrvdrTab\r\n            ? reqData?.form_data?.sp_line_items?.form_data\r\n            : reqData?.form_data?.line_items?.form_data;\r\n        if (reqLineItemsFormData) {\r\n            let lineItemConfig = JSON.parse(\r\n                srvcConfigData?.srvc_type_line_item_config\r\n            );\r\n            Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {\r\n                let groupConfig = lineItemConfig[lineItemGrpKey];\r\n                let nameFieldFormula = groupConfig.name_field_formula;\r\n                const fields =\r\n                    decodeFieldsMetaFrmJson(groupConfig.fields) || [];\r\n                const idVsLabelMapping = {};\r\n                fields.forEach((singleFieldMeta) => {\r\n                    idVsLabelMapping[singleFieldMeta.label] =\r\n                        singleFieldMeta.key;\r\n                });\r\n\r\n                let requestLineItemsInGrp =\r\n                    reqLineItemsFormData[lineItemGrpKey];\r\n                if (\r\n                    requestLineItemsInGrp &&\r\n                    Array.isArray(requestLineItemsInGrp)\r\n                ) {\r\n                    requestLineItemsInGrp.forEach(\r\n                        (singleLineItemInGrp, index) => {\r\n                            let label = `Item ${index + 1}`;\r\n                            let valueDataFrLineItem = getValueDataFrmFormMeta(\r\n                                fields,\r\n                                singleLineItemInGrp\r\n                            );\r\n                            if (\r\n                                nameFieldFormula &&\r\n                                nameFieldFormula.length > 0\r\n                            ) {\r\n                                label = parseFormulaToString(\r\n                                    nameFieldFormula,\r\n                                    idVsLabelMapping,\r\n                                    valueDataFrLineItem\r\n                                );\r\n                            }\r\n                            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;\r\n                            lineItemsWiseMeta.push({\r\n                                key: itemProgressKey,\r\n                                label: label, // parsed from formula,\r\n                                title: label,\r\n                                widget: SliderWidget,\r\n                                widgetProps: {\r\n                                    tooltip: { formatter: formatter },\r\n                                    onChange: (value) => {\r\n                                        // check for progression\r\n                                        if (\r\n                                            srvcConfigData.daily_progress_update_mode ==\r\n                                            'strict_mode'\r\n                                        ) {\r\n                                            checkFrProgressionAcrossRange(\r\n                                                value,\r\n                                                day,\r\n                                                itemProgressKey\r\n                                            );\r\n                                        }\r\n                                        calculateDayProgress(\r\n                                            value,\r\n                                            itemProgressKey\r\n                                        );\r\n                                    },\r\n                                },\r\n                            });\r\n                        }\r\n                    );\r\n                }\r\n            });\r\n        }\r\n        return lineItemsWiseMeta;\r\n    };\r\n    const getSelectedLineItemWiseProgressMeta = (selectedLineItemIds) => {\r\n        let lineItemsWiseMeta = [];\r\n\r\n        if (!selectedLineItemIds || selectedLineItemIds.length === 0) {\r\n            return lineItemsWiseMeta;\r\n        }\r\n\r\n        let reqLineItemsFormData = isSrvcPrvdrTab\r\n            ? reqData?.form_data?.sp_line_items?.form_data\r\n            : reqData?.form_data?.line_items?.form_data;\r\n\r\n        if (reqLineItemsFormData) {\r\n            let lineItemConfig = JSON.parse(\r\n                srvcConfigData?.srvc_type_line_item_config\r\n            );\r\n\r\n            Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {\r\n                let groupConfig = lineItemConfig[lineItemGrpKey];\r\n                let nameFieldFormula = groupConfig.name_field_formula;\r\n                const fields =\r\n                    decodeFieldsMetaFrmJson(groupConfig.fields) || [];\r\n                const idVsLabelMapping = {};\r\n                fields.forEach((singleFieldMeta) => {\r\n                    idVsLabelMapping[singleFieldMeta.label] =\r\n                        singleFieldMeta.key;\r\n                });\r\n\r\n                let requestLineItemsInGrp =\r\n                    reqLineItemsFormData[lineItemGrpKey];\r\n                if (\r\n                    requestLineItemsInGrp &&\r\n                    Array.isArray(requestLineItemsInGrp)\r\n                ) {\r\n                    requestLineItemsInGrp.forEach(\r\n                        (singleLineItemInGrp, index) => {\r\n                            // Check if this line item is in the selected items\r\n                            const lineItemId = `${lineItemGrpKey}_${singleLineItemInGrp.input_table_id}`;\r\n                            if (!selectedLineItemIds.includes(lineItemId)) {\r\n                                return; // Skip if not selected\r\n                            }\r\n\r\n                            let label = `Item ${index + 1}`;\r\n                            let valueDataFrLineItem = getValueDataFrmFormMeta(\r\n                                fields,\r\n                                singleLineItemInGrp\r\n                            );\r\n                            if (\r\n                                nameFieldFormula &&\r\n                                nameFieldFormula.length > 0\r\n                            ) {\r\n                                label = parseFormulaToString(\r\n                                    nameFieldFormula,\r\n                                    idVsLabelMapping,\r\n                                    valueDataFrLineItem\r\n                                );\r\n                            }\r\n                            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;\r\n                            lineItemsWiseMeta.push({\r\n                                key: itemProgressKey,\r\n                                className: 'wy-daily-update-line-item-progress',\r\n                                label: label, // parsed from formula,\r\n                                title: label,\r\n                                widget: SliderWidget,\r\n                                widgetProps: {\r\n                                    tooltip: { formatter: formatter },\r\n\r\n                                    onChange: (value) => {\r\n                                        // check for progression\r\n                                        if (\r\n                                            srvcConfigData.daily_progress_update_mode ==\r\n                                            'strict_mode'\r\n                                        ) {\r\n                                            checkFrProgressionAcrossRange(\r\n                                                value,\r\n                                                day,\r\n                                                itemProgressKey\r\n                                            );\r\n                                        }\r\n                                        calculateDayProgress(\r\n                                            value,\r\n                                            itemProgressKey\r\n                                        );\r\n                                    },\r\n                                },\r\n                            });\r\n                        }\r\n                    );\r\n                }\r\n            });\r\n        }\r\n        return lineItemsWiseMeta;\r\n    };\r\n\r\n    const handleOkFrUploadedFilesModal = (initialFiles, key) => {\r\n        setShowUploadedFilesModal('');\r\n\r\n        if (selectedFilesBySection[key] && filesBySection[key]) {\r\n            let removedCount = 0;\r\n            filesBySection[key] = filesBySection[key].filter((element) => {\r\n                if (selectedFilesBySection[key].includes(element)) {\r\n                    removedCount++;\r\n                    return false; // Exclude element from the filtered array\r\n                }\r\n                return true; // Include element in the filtered array\r\n            });\r\n            let newSelectedFilesBySection = _.cloneDeep(filesBySection);\r\n            if (removedCount == 0) {\r\n                newSelectedFilesBySection[key] = [\r\n                    ...new Set(\r\n                        newSelectedFilesBySection[key].concat(\r\n                            selectedFilesBySection[key]\r\n                        )\r\n                    ),\r\n                ];\r\n            } else {\r\n                newSelectedFilesBySection[key] = [\r\n                    ...newSelectedFilesBySection[key],\r\n                ];\r\n            }\r\n            setFilesBySection(newSelectedFilesBySection);\r\n            setSelectedImage(newSelectedFilesBySection);\r\n            onFormValueChange();\r\n        } else if (selectedFilesBySection[key]) {\r\n            let newSelectedFilesBySection = _.cloneDeep(filesBySection);\r\n            newSelectedFilesBySection[key] = [\r\n                ...selectedFilesBySection[key],\r\n                ...initialFiles,\r\n            ];\r\n            setFilesBySection(newSelectedFilesBySection);\r\n            setSelectedImage(newSelectedFilesBySection);\r\n            onFormValueChange();\r\n        }\r\n    };\r\n\r\n    const selectFromUploadedFilesClick = (sbtskDetailViewData) => {\r\n        // console.log('check',reqData.form_data)\r\n        if (sbtskDetailViewData == undefined) {\r\n            setLoadingSbtskDetailViewData(true);\r\n            setErrorFrLoadingSbtskDetailViewData(undefined);\r\n            var params = {};\r\n\r\n            const onComplete = (resp) => {\r\n                setSbtskDetailViewData(resp.data);\r\n                setLoadingSbtskDetailViewData(false);\r\n            };\r\n\r\n            const onError = (error) => {\r\n                message.error('Unable to load');\r\n                setErrorFrLoadingSbtskDetailViewData(\r\n                    http_utils.decodeErrorToMessage(error)\r\n                );\r\n                setLoadingSbtskDetailViewData(false);\r\n            };\r\n            let url =\r\n                sbtskDetailsUrl +\r\n                '/' +\r\n                reqData.id +\r\n                '/' +\r\n                editorDayDetails.assignee_date;\r\n            // console.log(\"url\",url);\r\n            http_utils.performGetCall(url, params, onComplete, onError);\r\n        }\r\n    };\r\n\r\n    const noteSelectedFilesBySection = (fileSection, files) => {\r\n        if (selectedFilesBySection[fileSection] == undefined) {\r\n            selectedFilesBySection[fileSection] = [files];\r\n        } else if (!selectedFilesBySection[fileSection]?.includes(files)) {\r\n            selectedFilesBySection[fileSection].push(files);\r\n        } else {\r\n            const index = selectedFilesBySection[fileSection]?.indexOf(files);\r\n            if (index > -1) {\r\n                // only splice array when item is found\r\n                selectedFilesBySection[fileSection].splice(index, 1); // 2nd parameter means remove one item only\r\n            }\r\n        }\r\n    };\r\n\r\n    const hasFacedIssueForDay = () =>\r\n        (form.getFieldValue('issue_faced') || 'No') == 'Yes';\r\n\r\n    // Function to check if files are selected and update required property\r\n    const handleFileSectionRequiredStatus = (singleFileSection) => {\r\n        const filesSelected =\r\n            selectedImage?.[singleFileSection.key]?.length > 0;\r\n        singleFileSection.required = filesSelected\r\n            ? false\r\n            : singleFileSection.required;\r\n    };\r\n\r\n    const getLineItemOptions = () => {\r\n        let options = [];\r\n        let lineItemsData = isSrvcPrvdrTab\r\n            ? reqData?.form_data?.sp_line_items?.form_data\r\n            : reqData?.form_data?.line_items?.form_data;\r\n\r\n        if (!lineItemsData) {\r\n            return options;\r\n        }\r\n\r\n        let lineItemConfig = {};\r\n        try {\r\n            lineItemConfig = JSON.parse(\r\n                srvcConfigData?.srvc_type_line_item_config || '{}'\r\n            );\r\n        } catch (e) {\r\n            console.log(\r\n                'DailyReqStatusEditor :: getLineItemOptions :: Error ::',\r\n                e\r\n            );\r\n            return options;\r\n        }\r\n\r\n        // Process each line item group\r\n        Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {\r\n            if (\r\n                !lineItemsData[lineItemGrpKey] ||\r\n                !Array.isArray(lineItemsData[lineItemGrpKey])\r\n            ) {\r\n                return;\r\n            }\r\n\r\n            let groupConfig = lineItemConfig[lineItemGrpKey] || {};\r\n            let nameFieldFormula = groupConfig.name_field_formula;\r\n            const fields = decodeFieldsMetaFrmJson(groupConfig.fields) || [];\r\n\r\n            // Create a mapping of field labels to keys\r\n            const idVsLabelMapping = {};\r\n            fields.forEach((singleFieldMeta) => {\r\n                idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;\r\n            });\r\n\r\n            // Process each line item in the group\r\n            lineItemsData[lineItemGrpKey].forEach((singleLineItem, index) => {\r\n                if (!singleLineItem || !singleLineItem.input_table_id) {\r\n                    return;\r\n                }\r\n\r\n                let label = `${groupConfig.label || 'Item'} ${index + 1}`;\r\n                let valueData = getValueDataFrmFormMeta(fields, singleLineItem);\r\n\r\n                // Try to use the name formula if available\r\n                if (nameFieldFormula && nameFieldFormula.length > 0) {\r\n                    try {\r\n                        label = parseFormulaToString(\r\n                            nameFieldFormula,\r\n                            idVsLabelMapping,\r\n                            valueData\r\n                        );\r\n                    } catch (e) {\r\n                        console.log(\r\n                            'DailyReqStatusEditor :: getLineItemOptions :: Error ::',\r\n                            e\r\n                        );\r\n                    }\r\n                }\r\n\r\n                // Add the option\r\n                options.push({\r\n                    label: label,\r\n                    value: `${lineItemGrpKey}_${singleLineItem.input_table_id}`,\r\n                });\r\n            });\r\n        });\r\n\r\n        return options;\r\n    };\r\n\r\n    const meta = () => {\r\n        let dailyUpdateWillHaveIssues =\r\n            srvcConfigData?.daily_update_will_have_issues == 'Yes';\r\n        let trackLineItemWiseProgress =\r\n            srvcConfigData?.daily_update_track_line_item_progress == 'Yes';\r\n        let showLineItemBySelection =\r\n            srvcConfigData?.show_line_item_by_selection;\r\n        let trackLineItemWisePhotos =\r\n            srvcConfigData?.daily_update_dynamic_line_item_wise_files == 'Yes';\r\n        let selectedLineItemsFrProgress = form.getFieldValue(\r\n            'selected_line_items'\r\n        );\r\n\r\n        return {\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'day_remarks',\r\n                    label: 'Work done today',\r\n                    widget: 'textarea',\r\n                },\r\n                {\r\n                    render: () => <hr />,\r\n                },\r\n                ...(dailyUpdateWillHaveIssues\r\n                    ? [\r\n                          {\r\n                              key: 'issue_faced',\r\n                              label: 'Issue faced ?',\r\n                              widget: 'radio-group',\r\n                              options: ['Yes', 'No'],\r\n                              onChange: () => {\r\n                                  forceUpdate();\r\n                              },\r\n                              required: true,\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(hasFacedIssueForDay()\r\n                    ? [\r\n                          {\r\n                              render: () => <hr />,\r\n                          },\r\n                          ...dailyUpdateIssueFormMeta(),\r\n                          {\r\n                              render: () => (\r\n                                  <>\r\n                                      <Row>\r\n                                          {getIssueFileMeta()?.map(\r\n                                              (singleFileSection, index) => {\r\n                                                  handleFileSectionRequiredStatus(\r\n                                                      singleFileSection\r\n                                                  );\r\n                                                  return (\r\n                                                      <Col\r\n                                                          xs={24}\r\n                                                          md={24}\r\n                                                          className=\"gx-pl-0\"\r\n                                                          key={\r\n                                                              singleFileSection.key\r\n                                                          }\r\n                                                      >\r\n                                                          {singleFileSection.title !=\r\n                                                              '' && (\r\n                                                              <h3 className=\"gx-mt-3\">\r\n                                                                  {singleFileSection.required && (\r\n                                                                      <span\r\n                                                                          style={{\r\n                                                                              color: 'red',\r\n                                                                          }}\r\n                                                                      >\r\n                                                                          {' '}\r\n                                                                          *{' '}\r\n                                                                      </span>\r\n                                                                  )}\r\n                                                                  {\r\n                                                                      singleFileSection.title\r\n                                                                  }\r\n                                                                  <hr className=\"gx-bg-dark\"></hr>\r\n                                                              </h3>\r\n                                                          )}\r\n                                                          <Form.Item\r\n                                                              name={\r\n                                                                  'file_uploads'\r\n                                                              }\r\n                                                          >\r\n                                                              {\r\n                                                                  <>\r\n                                                                      <Button\r\n                                                                          type=\"link\"\r\n                                                                          onClick={() => {\r\n                                                                              setShowUploadedFilesModal(\r\n                                                                                  singleFileSection.key\r\n                                                                              );\r\n                                                                              selectFromUploadedFilesClick(\r\n                                                                                  sbtskDetailViewData\r\n                                                                              );\r\n                                                                          }}\r\n                                                                      >\r\n                                                                          Select\r\n                                                                          from\r\n                                                                          uploaded\r\n                                                                          files\r\n                                                                      </Button>\r\n\r\n                                                                      <Modal\r\n                                                                          title=\"Attachments\"\r\n                                                                          centered\r\n                                                                          visible={\r\n                                                                              singleFileSection.key ==\r\n                                                                              showUploadedFilesModal\r\n                                                                          }\r\n                                                                          onOk={() => {\r\n                                                                              handleOkFrUploadedFilesModal(\r\n                                                                                  prefillFormData\r\n                                                                                      .attachments?.[\r\n                                                                                      singleFileSection\r\n                                                                                          .key\r\n                                                                                  ] ||\r\n                                                                                      [],\r\n                                                                                  singleFileSection.key\r\n                                                                              );\r\n                                                                          }}\r\n                                                                          onCancel={() =>\r\n                                                                              setShowUploadedFilesModal(\r\n                                                                                  ''\r\n                                                                              )\r\n                                                                          }\r\n                                                                          width={\r\n                                                                              1000\r\n                                                                          }\r\n                                                                      >\r\n                                                                          {loadingSbtskDetailViewData ? (\r\n                                                                              <div className=\"gx-loader-view gx-loader-position\">\r\n                                                                                  <CircularProgress />\r\n                                                                              </div>\r\n                                                                          ) : sbtskDetailViewData ==\r\n                                                                            undefined ? (\r\n                                                                              <p className=\"gx-text-red\">\r\n                                                                                  {\r\n                                                                                      errorFrloadingSbtskDetailViewData\r\n                                                                                  }\r\n                                                                              </p>\r\n                                                                          ) : (\r\n                                                                              <AttachmentsPreview\r\n                                                                                  attachments={getAllMergedAttachments(\r\n                                                                                      sbtskDetailViewData,\r\n                                                                                      prefillFormData\r\n                                                                                          .attachments?.[\r\n                                                                                          singleFileSection\r\n                                                                                              .key\r\n                                                                                      ] ||\r\n                                                                                          []\r\n                                                                                  )}\r\n                                                                                  noteSelectedFilesBySection={(\r\n                                                                                      files\r\n                                                                                  ) => {\r\n                                                                                      noteSelectedFilesBySection(\r\n                                                                                          singleFileSection.key,\r\n                                                                                          files\r\n                                                                                      );\r\n                                                                                  }}\r\n                                                                              />\r\n                                                                          )}\r\n                                                                      </Modal>\r\n                                                                      {\r\n                                                                          selectedImage[\r\n                                                                              singleFileSection\r\n                                                                                  .key\r\n                                                                          ] && (\r\n                                                                              <div\r\n                                                                                  key={\r\n                                                                                      index\r\n                                                                                  }\r\n                                                                              >\r\n                                                                                  <AttachmentsPreview\r\n                                                                                      attachments={\r\n                                                                                          selectedImage[\r\n                                                                                              singleFileSection\r\n                                                                                                  .key\r\n                                                                                          ]\r\n                                                                                      }\r\n                                                                                      noteSelectedFilesBySection={(\r\n                                                                                          files\r\n                                                                                      ) => {\r\n                                                                                          noteSelectedFilesBySection(\r\n                                                                                              singleFileSection.key,\r\n                                                                                              files\r\n                                                                                          );\r\n                                                                                      }}\r\n                                                                                      selectOnly={\r\n                                                                                          false\r\n                                                                                      }\r\n                                                                                      sectionKey={\r\n                                                                                          singleFileSection.key\r\n                                                                                      }\r\n                                                                                      prevUploadedFiles={\r\n                                                                                          prefillFormData\r\n                                                                                              ?.attachments?.[\r\n                                                                                              singleFileSection\r\n                                                                                                  .key\r\n                                                                                          ]\r\n                                                                                      }\r\n                                                                                  />\r\n                                                                              </div>\r\n                                                                          )\r\n                                                                          // ))\r\n                                                                      }\r\n                                                                  </>\r\n                                                              }\r\n                                                              <S3Uploader\r\n                                                                  // className=\"gx-w-50\"\r\n                                                                  // demoMode\r\n                                                                  required={\r\n                                                                      singleFileSection.required\r\n                                                                  }\r\n                                                                  maxColSpan={4}\r\n                                                                  authToken={http_utils.getAuthToken()}\r\n                                                                  prefixDomain={http_utils.getCDNDomain()}\r\n                                                                  customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}\r\n                                                                  totalFiles={\r\n                                                                      prefillFormData\r\n                                                                          ?.attachments?.[\r\n                                                                          singleFileSection\r\n                                                                              .key\r\n                                                                      ]\r\n                                                                  }\r\n                                                                  onFilesChanged={(\r\n                                                                      files,\r\n                                                                      deletedFileUrl\r\n                                                                  ) => {\r\n                                                                      onFilesChanged(\r\n                                                                          singleFileSection.key,\r\n                                                                          files,\r\n                                                                          deletedFileUrl\r\n                                                                      );\r\n                                                                  }}\r\n                                                                  onReadyStatusChanged={(\r\n                                                                      isReady\r\n                                                                  ) => {\r\n                                                                      onFileUploaderReadyChange(\r\n                                                                          singleFileSection.key,\r\n                                                                          isReady\r\n                                                                      );\r\n                                                                  }}\r\n                                                                  initialFiles={\r\n                                                                      prefillFormData\r\n                                                                          .attachments?.[\r\n                                                                          singleFileSection\r\n                                                                              .key\r\n                                                                      ] || []\r\n                                                                  }\r\n                                                                  customPreviewHeight=\"100%\"\r\n                                                                  customFileIconMaxWidth=\"40px\"\r\n                                                                  compConfig={{\r\n                                                                      name: 'daily-req-status-modal-preview',\r\n                                                                  }}\r\n                                                              />\r\n                                                          </Form.Item>\r\n                                                      </Col>\r\n                                                  );\r\n                                              }\r\n                                          )}\r\n                                          {getFormIssueMicMeta()?.map(\r\n                                              (singleMicSection, index) => (\r\n                                                  <Col\r\n                                                      xs={24}\r\n                                                      md={24}\r\n                                                      className=\"gx-pl-0\"\r\n                                                      key={singleMicSection.key}\r\n                                                  >\r\n                                                      {singleMicSection.title !=\r\n                                                          '' && (\r\n                                                          <h3 className=\"gx-mt-3\">\r\n                                                              {singleMicSection.required && (\r\n                                                                  <span\r\n                                                                      style={{\r\n                                                                          color: 'red',\r\n                                                                      }}\r\n                                                                  >\r\n                                                                      {' '}\r\n                                                                      *{' '}\r\n                                                                  </span>\r\n                                                              )}\r\n                                                              {\r\n                                                                  singleMicSection.title\r\n                                                              }\r\n                                                              <hr className=\"gx-bg-dark\"></hr>\r\n                                                          </h3>\r\n                                                      )}\r\n                                                      <Form.Item\r\n                                                          name={\r\n                                                              'mic_recording_uploads'\r\n                                                          }\r\n                                                      >\r\n                                                          <MicInputV2\r\n                                                              // className=\"gx-w-50\"\r\n                                                              // demoMode\r\n                                                              required={\r\n                                                                  singleMicSection.required\r\n                                                              }\r\n                                                              maxColSpan={6}\r\n                                                              authToken={http_utils.getAuthToken()}\r\n                                                              prefixDomain={http_utils.getCDNDomain()}\r\n                                                              onFilesChanged={(\r\n                                                                  files\r\n                                                              ) => {\r\n                                                                  onMicFilesChanged(\r\n                                                                      singleMicSection.key,\r\n                                                                      files\r\n                                                                  );\r\n                                                              }}\r\n                                                              onReadyStatusChanged={(\r\n                                                                  isReady\r\n                                                              ) => {\r\n                                                                  onMicFileUploaderReadyChange(\r\n                                                                      singleMicSection.key,\r\n                                                                      isReady\r\n                                                                  );\r\n                                                              }}\r\n                                                              initialFiles={\r\n                                                                  prefillFormData\r\n                                                                      .mic_files?.[\r\n                                                                      singleMicSection\r\n                                                                          .key\r\n                                                                  ] || []\r\n                                                              }\r\n                                                          />\r\n                                                      </Form.Item>\r\n                                                  </Col>\r\n                                              )\r\n                                          )}\r\n                                      </Row>\r\n                                      <hr />\r\n                                  </>\r\n                              ),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(trackLineItemWiseProgress &&\r\n                showLineItemBySelection &&\r\n                canCurrentUserEdit\r\n                    ? [\r\n                          {\r\n                              key: 'selected_line_items',\r\n                              label: 'Line Item Progress',\r\n                              widget: 'select',\r\n                              widgetProps: {\r\n                                  mode: 'multiple',\r\n                                  maxTagCount: isMobileView() ? 1 : 3,\r\n                                  // maxTagTextLength: 20,\r\n                                  showSearch: true,\r\n                                  optionFilterProp: 'children',\r\n                                  placeholder:\r\n                                      'Select line items to track progress',\r\n                                  style: { width: '100%' },\r\n                                  allowClear: true,\r\n                              },\r\n                              options: getLineItemOptions(),\r\n                              onChange: (value) => {\r\n                                  forceUpdate();\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(showLineItemBySelection &&\r\n                trackLineItemWiseProgress &&\r\n                selectedLineItemsFrProgress &&\r\n                selectedLineItemsFrProgress.length > 0\r\n                    ? [\r\n                          {\r\n                              render: () => (\r\n                                  <div className=\"gx-module-box-content gx-px-3 gx-py-3 wy-show-line-items-by-selection\">\r\n                                      <FormBuilder\r\n                                          meta={{\r\n                                              fields: getSelectedLineItemWiseProgressMeta(\r\n                                                  form.getFieldValue(\r\n                                                      'selected_line_items'\r\n                                                  )\r\n                                              ),\r\n                                          }}\r\n                                          form={form}\r\n                                      />\r\n                                  </div>\r\n                              ),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(trackLineItemWiseProgress &&\r\n                (!showLineItemBySelection || !canCurrentUserEdit)\r\n                    ? getLineItemWiseProgressMeta()\r\n                    : []),\r\n\r\n                ...(trackLineItemWisePhotos\r\n                    ? [\r\n                          {\r\n                              render: () => (\r\n                                  <>\r\n                                      <Row>\r\n                                          {getLineItemWiseProgressMeta()?.map(\r\n                                              (singleFileSection, index) => {\r\n                                                  handleFileSectionRequiredStatus(\r\n                                                      singleFileSection\r\n                                                  );\r\n                                                  return (\r\n                                                      <Col\r\n                                                          xs={24}\r\n                                                          md={24}\r\n                                                          className=\"gx-pl-0\"\r\n                                                          key={\r\n                                                              singleFileSection.key\r\n                                                          }\r\n                                                      >\r\n                                                          {singleFileSection.title !=\r\n                                                              '' && (\r\n                                                              <h3 className=\"gx-mt-3\">\r\n                                                                  {singleFileSection.required && (\r\n                                                                      <span\r\n                                                                          style={{\r\n                                                                              color: 'red',\r\n                                                                          }}\r\n                                                                      >\r\n                                                                          {' '}\r\n                                                                          *{' '}\r\n                                                                      </span>\r\n                                                                  )}\r\n                                                                  {\r\n                                                                      singleFileSection.title\r\n                                                                  }\r\n                                                                  <hr className=\"gx-bg-dark\"></hr>\r\n                                                              </h3>\r\n                                                          )}\r\n                                                          <Form.Item\r\n                                                              name={\r\n                                                                  'file_uploads'\r\n                                                              }\r\n                                                          >\r\n                                                              {\r\n                                                                  <>\r\n                                                                      <Button\r\n                                                                          type=\"link\"\r\n                                                                          onClick={() => {\r\n                                                                              setShowUploadedFilesModal(\r\n                                                                                  singleFileSection.key\r\n                                                                              );\r\n                                                                              selectFromUploadedFilesClick(\r\n                                                                                  sbtskDetailViewData\r\n                                                                              );\r\n                                                                          }}\r\n                                                                      >\r\n                                                                          Select\r\n                                                                          from\r\n                                                                          uploaded\r\n                                                                          files\r\n                                                                      </Button>\r\n\r\n                                                                      <Modal\r\n                                                                          title=\"Attachments\"\r\n                                                                          centered\r\n                                                                          visible={\r\n                                                                              singleFileSection.key ==\r\n                                                                              showUploadedFilesModal\r\n                                                                          }\r\n                                                                          onOk={() => {\r\n                                                                              handleOkFrUploadedFilesModal(\r\n                                                                                  prefillFormData\r\n                                                                                      .attachments?.[\r\n                                                                                      singleFileSection\r\n                                                                                          .key\r\n                                                                                  ] ||\r\n                                                                                      [],\r\n                                                                                  singleFileSection.key\r\n                                                                              );\r\n                                                                          }}\r\n                                                                          onCancel={() =>\r\n                                                                              setShowUploadedFilesModal(\r\n                                                                                  ''\r\n                                                                              )\r\n                                                                          }\r\n                                                                          width={\r\n                                                                              1000\r\n                                                                          }\r\n                                                                      >\r\n                                                                          {loadingSbtskDetailViewData ? (\r\n                                                                              <div className=\"gx-loader-view gx-loader-position\">\r\n                                                                                  <CircularProgress />\r\n                                                                              </div>\r\n                                                                          ) : sbtskDetailViewData ==\r\n                                                                            undefined ? (\r\n                                                                              <p className=\"gx-text-red\">\r\n                                                                                  {\r\n                                                                                      errorFrloadingSbtskDetailViewData\r\n                                                                                  }\r\n                                                                              </p>\r\n                                                                          ) : (\r\n                                                                              <AttachmentsPreview\r\n                                                                                  attachments={getAllMergedAttachments(\r\n                                                                                      sbtskDetailViewData,\r\n                                                                                      prefillFormData\r\n                                                                                          .attachments?.[\r\n                                                                                          singleFileSection\r\n                                                                                              .key\r\n                                                                                      ] ||\r\n                                                                                          []\r\n                                                                                  )}\r\n                                                                                  noteSelectedFilesBySection={(\r\n                                                                                      files\r\n                                                                                  ) => {\r\n                                                                                      noteSelectedFilesBySection(\r\n                                                                                          singleFileSection.key,\r\n                                                                                          files\r\n                                                                                      );\r\n                                                                                  }}\r\n                                                                              />\r\n                                                                          )}\r\n\r\n                                                                          {/* todo */}\r\n                                                                      </Modal>\r\n                                                                      {\r\n                                                                          selectedImage[\r\n                                                                              singleFileSection\r\n                                                                                  .key\r\n                                                                          ] && (\r\n                                                                              <div\r\n                                                                                  key={\r\n                                                                                      index\r\n                                                                                  }\r\n                                                                              >\r\n                                                                                  <AttachmentsPreview\r\n                                                                                      attachments={\r\n                                                                                          selectedImage[\r\n                                                                                              singleFileSection\r\n                                                                                                  .key\r\n                                                                                          ]\r\n                                                                                      }\r\n                                                                                      noteSelectedFilesBySection={(\r\n                                                                                          files\r\n                                                                                      ) => {\r\n                                                                                          noteSelectedFilesBySection(\r\n                                                                                              singleFileSection.key,\r\n                                                                                              files\r\n                                                                                          );\r\n                                                                                      }}\r\n                                                                                      selectOnly={\r\n                                                                                          false\r\n                                                                                      }\r\n                                                                                      sectionKey={\r\n                                                                                          singleFileSection.key\r\n                                                                                      }\r\n                                                                                      prevUploadedFiles={\r\n                                                                                          prefillFormData\r\n                                                                                              ?.attachments?.[\r\n                                                                                              singleFileSection\r\n                                                                                                  .key\r\n                                                                                          ]\r\n                                                                                      }\r\n                                                                                  />\r\n                                                                              </div>\r\n                                                                          )\r\n                                                                          // ))\r\n                                                                      }\r\n                                                                  </>\r\n                                                              }\r\n                                                              <S3Uploader\r\n                                                                  // className=\"gx-w-50\"\r\n                                                                  // demoMode\r\n                                                                  required={\r\n                                                                      singleFileSection.required\r\n                                                                  }\r\n                                                                  maxColSpan={4}\r\n                                                                  authToken={http_utils.getAuthToken()}\r\n                                                                  prefixDomain={http_utils.getCDNDomain()}\r\n                                                                  customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}\r\n                                                                  totalFiles={\r\n                                                                      prefillFormData\r\n                                                                          ?.attachments?.[\r\n                                                                          singleFileSection\r\n                                                                              .key\r\n                                                                      ]\r\n                                                                  }\r\n                                                                  onFilesChanged={(\r\n                                                                      files,\r\n                                                                      deletedFileUrl\r\n                                                                  ) => {\r\n                                                                      onFilesChanged(\r\n                                                                          singleFileSection.key,\r\n                                                                          files,\r\n                                                                          deletedFileUrl\r\n                                                                      );\r\n                                                                  }}\r\n                                                                  onReadyStatusChanged={(\r\n                                                                      isReady\r\n                                                                  ) => {\r\n                                                                      onFileUploaderReadyChange(\r\n                                                                          singleFileSection.key,\r\n                                                                          isReady\r\n                                                                      );\r\n                                                                  }}\r\n                                                                  initialFiles={\r\n                                                                      prefillFormData\r\n                                                                          .attachments?.[\r\n                                                                          singleFileSection\r\n                                                                              .key\r\n                                                                      ] || []\r\n                                                                  }\r\n                                                                  customPreviewHeight=\"100%\"\r\n                                                                  customFileIconMaxWidth=\"40px\"\r\n                                                                  compConfig={{\r\n                                                                      name: 'daily-request-status-editor-sp',\r\n                                                                  }}\r\n                                                              />\r\n                                                          </Form.Item>\r\n                                                      </Col>\r\n                                                  );\r\n                                              }\r\n                                          )}\r\n                                      </Row>\r\n                                      <hr />\r\n                                  </>\r\n                              ),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...dailyUpdateFormMeta(),\r\n                {\r\n                    render: () => (\r\n                        <>\r\n                            <Row>\r\n                                {getFormFileMeta()?.map(\r\n                                    (singleFileSection, index) => {\r\n                                        handleFileSectionRequiredStatus(\r\n                                            singleFileSection\r\n                                        );\r\n                                        return (\r\n                                            <Col\r\n                                                xs={24}\r\n                                                md={24}\r\n                                                className=\"gx-pl-0\"\r\n                                                key={singleFileSection.key}\r\n                                            >\r\n                                                {singleFileSection.title !=\r\n                                                    '' && (\r\n                                                    <h3 className=\"gx-mt-3\">\r\n                                                        {singleFileSection.required && (\r\n                                                            <span\r\n                                                                style={{\r\n                                                                    color: 'red',\r\n                                                                }}\r\n                                                            >\r\n                                                                {' '}\r\n                                                                *{' '}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {\r\n                                                            singleFileSection.title\r\n                                                        }\r\n                                                        <hr className=\"gx-bg-dark\"></hr>\r\n                                                    </h3>\r\n                                                )}\r\n                                                <Form.Item\r\n                                                    name={'file_uploads'}\r\n                                                >\r\n                                                    {\r\n                                                        <>\r\n                                                            <Button\r\n                                                                type=\"link\"\r\n                                                                onClick={() => {\r\n                                                                    setShowUploadedFilesModal(\r\n                                                                        singleFileSection.key\r\n                                                                    );\r\n                                                                    selectFromUploadedFilesClick(\r\n                                                                        sbtskDetailViewData\r\n                                                                    );\r\n                                                                }}\r\n                                                            >\r\n                                                                Select from\r\n                                                                uploaded files\r\n                                                            </Button>\r\n\r\n                                                            <Modal\r\n                                                                title=\"Attachments\"\r\n                                                                centered\r\n                                                                visible={\r\n                                                                    singleFileSection.key ==\r\n                                                                    showUploadedFilesModal\r\n                                                                }\r\n                                                                onOk={() => {\r\n                                                                    handleOkFrUploadedFilesModal(\r\n                                                                        prefillFormData\r\n                                                                            .attachments?.[\r\n                                                                            singleFileSection\r\n                                                                                .key\r\n                                                                        ] || [],\r\n                                                                        singleFileSection.key\r\n                                                                    );\r\n                                                                }}\r\n                                                                onCancel={() =>\r\n                                                                    setShowUploadedFilesModal(\r\n                                                                        ''\r\n                                                                    )\r\n                                                                }\r\n                                                                width={1000}\r\n                                                            >\r\n                                                                {loadingSbtskDetailViewData ? (\r\n                                                                    <div className=\"gx-loader-view gx-loader-position\">\r\n                                                                        <CircularProgress />\r\n                                                                    </div>\r\n                                                                ) : sbtskDetailViewData ==\r\n                                                                  undefined ? (\r\n                                                                    <p className=\"gx-text-red\">\r\n                                                                        {\r\n                                                                            errorFrloadingSbtskDetailViewData\r\n                                                                        }\r\n                                                                    </p>\r\n                                                                ) : (\r\n                                                                    <AttachmentsPreview\r\n                                                                        attachments={getAllMergedAttachments(\r\n                                                                            sbtskDetailViewData,\r\n                                                                            prefillFormData\r\n                                                                                .attachments?.[\r\n                                                                                singleFileSection\r\n                                                                                    .key\r\n                                                                            ] ||\r\n                                                                                []\r\n                                                                        )}\r\n                                                                        noteSelectedFilesBySection={(\r\n                                                                            files\r\n                                                                        ) => {\r\n                                                                            noteSelectedFilesBySection(\r\n                                                                                singleFileSection.key,\r\n                                                                                files\r\n                                                                            );\r\n                                                                        }}\r\n                                                                    />\r\n                                                                )}\r\n                                                            </Modal>\r\n                                                            {\r\n                                                                selectedImage[\r\n                                                                    singleFileSection\r\n                                                                        .key\r\n                                                                ] && (\r\n                                                                    <div\r\n                                                                        key={\r\n                                                                            index\r\n                                                                        }\r\n                                                                    >\r\n                                                                        <AttachmentsPreview\r\n                                                                            attachments={\r\n                                                                                selectedImage[\r\n                                                                                    singleFileSection\r\n                                                                                        .key\r\n                                                                                ]\r\n                                                                            }\r\n                                                                            noteSelectedFilesBySection={(\r\n                                                                                files\r\n                                                                            ) => {\r\n                                                                                noteSelectedFilesBySection(\r\n                                                                                    singleFileSection.key,\r\n                                                                                    files\r\n                                                                                );\r\n                                                                            }}\r\n                                                                            selectOnly={\r\n                                                                                false\r\n                                                                            }\r\n                                                                            sectionKey={\r\n                                                                                singleFileSection.key\r\n                                                                            }\r\n                                                                            prevUploadedFiles={\r\n                                                                                prefillFormData\r\n                                                                                    ?.attachments?.[\r\n                                                                                    singleFileSection\r\n                                                                                        .key\r\n                                                                                ]\r\n                                                                            }\r\n                                                                        />\r\n                                                                    </div>\r\n                                                                )\r\n                                                                // ))\r\n                                                            }\r\n                                                        </>\r\n                                                    }\r\n\r\n                                                    <S3Uploader\r\n                                                        // className=\"gx-w-50\"\r\n                                                        // demoMode\r\n                                                        required={\r\n                                                            singleFileSection.required\r\n                                                        }\r\n                                                        maxColSpan={4}\r\n                                                        authToken={http_utils.getAuthToken()}\r\n                                                        prefixDomain={http_utils.getCDNDomain()}\r\n                                                        customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}\r\n                                                        totalFiles={\r\n                                                            prefillFormData\r\n                                                                ?.attachments?.[\r\n                                                                singleFileSection\r\n                                                                    .key\r\n                                                            ]\r\n                                                        }\r\n                                                        onFilesChanged={(\r\n                                                            files,\r\n                                                            deletedFileUrl\r\n                                                        ) => {\r\n                                                            onFilesChanged(\r\n                                                                singleFileSection.key,\r\n                                                                files,\r\n                                                                deletedFileUrl\r\n                                                            );\r\n                                                        }}\r\n                                                        onReadyStatusChanged={(\r\n                                                            isReady\r\n                                                        ) => {\r\n                                                            onFileUploaderReadyChange(\r\n                                                                singleFileSection.key,\r\n                                                                isReady\r\n                                                            );\r\n                                                        }}\r\n                                                        initialFiles={\r\n                                                            prefillFormData\r\n                                                                .attachments?.[\r\n                                                                singleFileSection\r\n                                                                    .key\r\n                                                            ] || []\r\n                                                        }\r\n                                                        customPreviewHeight=\"100%\"\r\n                                                        customFileIconMaxWidth=\"40px\"\r\n                                                        compConfig={{\r\n                                                            name: 'daily-request-status-editor',\r\n                                                        }}\r\n                                                    />\r\n                                                </Form.Item>\r\n                                            </Col>\r\n                                        );\r\n                                    }\r\n                                )}\r\n                                {getFormMicMeta()?.map(\r\n                                    (singleMicSection, index) => {\r\n                                        return (\r\n                                            <Col\r\n                                                xs={24}\r\n                                                md={24}\r\n                                                className=\"gx-pl-0\"\r\n                                                key={singleMicSection.key}\r\n                                            >\r\n                                                {singleMicSection.title !=\r\n                                                    '' && (\r\n                                                    <h3 className=\"gx-mt-3\">\r\n                                                        {singleMicSection.required && (\r\n                                                            <span\r\n                                                                style={{\r\n                                                                    color: 'red',\r\n                                                                }}\r\n                                                            >\r\n                                                                {' '}\r\n                                                                *{' '}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {singleMicSection.title}\r\n                                                        <hr className=\"gx-bg-dark\"></hr>\r\n                                                    </h3>\r\n                                                )}\r\n                                                <Form.Item\r\n                                                    name={\r\n                                                        'mic_recording_uploads'\r\n                                                    }\r\n                                                >\r\n                                                    <MicInputV2\r\n                                                        // className=\"gx-w-50\"\r\n                                                        // demoMode\r\n                                                        required={\r\n                                                            singleMicSection.required\r\n                                                        }\r\n                                                        maxColSpan={6}\r\n                                                        authToken={http_utils.getAuthToken()}\r\n                                                        prefixDomain={http_utils.getCDNDomain()}\r\n                                                        onFilesChanged={(\r\n                                                            files\r\n                                                        ) => {\r\n                                                            onMicFilesChanged(\r\n                                                                singleMicSection.key,\r\n                                                                files\r\n                                                            );\r\n                                                        }}\r\n                                                        onReadyStatusChanged={(\r\n                                                            isReady\r\n                                                        ) => {\r\n                                                            onMicFileUploaderReadyChange(\r\n                                                                singleMicSection.key,\r\n                                                                isReady\r\n                                                            );\r\n                                                        }}\r\n                                                        initialFiles={\r\n                                                            prefillFormData\r\n                                                                .mic_files?.[\r\n                                                                singleMicSection\r\n                                                                    .key\r\n                                                            ] || []\r\n                                                        }\r\n                                                    />\r\n                                                </Form.Item>\r\n                                            </Col>\r\n                                        );\r\n                                    }\r\n                                )}\r\n                            </Row>\r\n                            <hr />\r\n                        </>\r\n                    ),\r\n                },\r\n                {\r\n                    key: 'day_progress',\r\n                    label: 'Day progress',\r\n                    widget: SliderWidget,\r\n                    widgetProps: {\r\n                        tooltip: { formatter: formatter },\r\n                        onChange: (value) => {\r\n                            if (\r\n                                srvcConfigData.daily_progress_update_mode ==\r\n                                'strict_mode'\r\n                            ) {\r\n                                checkFrProgressionAcrossRange(\r\n                                    value,\r\n                                    day,\r\n                                    'day_progress'\r\n                                );\r\n                            }\r\n                        },\r\n                        disabled: trackLineItemWiseProgress,\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    };\r\n\r\n    const onFormValueChange = () => {\r\n        setIsFormChanged(true);\r\n    };\r\n\r\n    /**\r\n     * Extracts and maps the progress values for all line items from the form data.\r\n     *\r\n     * This function determines whether the service provider tab is active and selects the appropriate\r\n     * line items (`sp_line_items` or `line_items`) from the request data. It uses the service configuration\r\n     * (`srvc_type_line_item_config`) to iterate through the defined line item groups, and fetches progress\r\n     * values for each item using their `input_table_id` as a unique key.\r\n     *\r\n     * @returns {Object} A map of progress field keys (`progress_<input_table_id>`) to their corresponding values\r\n     *                   from the form. If the form value is not set, the value will be `null`.\r\n     *\r\n     * @example\r\n     * {\r\n     *   progress_1234: 50,\r\n     *   progress_5678: 75\r\n     * }\r\n     */\r\n    const getAllLineItemProgressWithValues = () => {\r\n        let progressMap = {};\r\n        let reqLineItemsFormData = isSrvcPrvdrTab\r\n            ? reqData?.form_data?.sp_line_items?.form_data\r\n            : reqData?.form_data?.line_items?.form_data;\r\n\r\n        if (!reqLineItemsFormData) return progressMap;\r\n\r\n        let lineItemConfig = JSON.parse(\r\n            srvcConfigData?.srvc_type_line_item_config\r\n        );\r\n\r\n        Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {\r\n            let itemsInGroup = reqLineItemsFormData[lineItemGrpKey] || [];\r\n            itemsInGroup.forEach((item) => {\r\n                const key = `progress_${item.input_table_id}`;\r\n                const value = form.getFieldValue(key) ?? null; // you can default to 0 if needed\r\n                progressMap[key] = value;\r\n            });\r\n        });\r\n\r\n        return progressMap;\r\n    };\r\n\r\n\r\n    const onSubmit = (values) => {\r\n        setIsFormSubmitting(true);\r\n        setError(undefined);\r\n        let existingDayStatusUpdates = existingDailyStatusObj[day];\r\n        if (existingDayStatusUpdates == undefined) {\r\n            existingDayStatusUpdates = {};\r\n        }\r\n        existingDayStatusUpdates.update_day_and_time =\r\n            getCurrentDateAndTimeFrDisplay();\r\n        let existingFileOrSelectedNewFile = {\r\n            ...existingDayStatusUpdates?.attachments,\r\n            ...filesBySection,\r\n        };\r\n        let existingMicRecordingsOrSelectedNewRecordings = {\r\n            ...existingDayStatusUpdates?.mic_files,\r\n            ...micRecordingsBySection,\r\n        };\r\n        values['attachments'] = existingFileOrSelectedNewFile;\r\n        values['mic_files'] = existingMicRecordingsOrSelectedNewRecordings;\r\n\r\n        // save all the line items progress data if selected_line_items config enabled\r\n        if (\r\n            form.getFieldValue('selected_line_items') &&\r\n            srvcConfigData?.daily_update_track_line_item_progress == 'Yes'\r\n        ) {\r\n            const allLineItemProgressMap = getAllLineItemProgressWithValues();\r\n\r\n            Object.entries(allLineItemProgressMap).forEach(([key, value]) => {\r\n                if (!(key in values)) {\r\n                    values[key] = value ?? 0; // Use existing value, or 0 if null/undefined\r\n                }\r\n            });\r\n        }\r\n\r\n        let mergedStatusUpdateFrDay = {\r\n            ...existingDayStatusUpdates,\r\n            ...values, // newDayStatusUpdateValues\r\n        };\r\n        existingDailyStatusObj[day] = mergedStatusUpdateFrDay;\r\n\r\n        let daily_status_updates_key_name = ConfigHelpers.isServiceProvider()\r\n            ? 'sp_daily_status_updates'\r\n            : 'daily_status_updates';\r\n        var params = {\r\n            [daily_status_updates_key_name]: {\r\n                ...existingDailyStatusObj,\r\n            },\r\n            updatedDay: day,\r\n        };\r\n        // console.log('params',params)\r\n        const onComplete = (resp) => {\r\n            setIsFormSubmitting(false);\r\n            setError(undefined);\r\n            if (onChange) {\r\n                onChange(day);\r\n            }\r\n            // message.info(JSON.stringify(resp.data));\r\n        };\r\n        const onError = (error) => {\r\n            setIsFormSubmitting(false);\r\n            setError(http_utils.decodeErrorToMessage(error));\r\n        };\r\n        var url = urlToSubmitFrUpdates;\r\n        http_utils.performPutCall(url, params, onComplete, onError);\r\n    };\r\n\r\n    const getIssueFileMeta = () => {\r\n        let dailyUpdateIssueFileMeta = [];\r\n        dailyUpdateIssueFileMeta = decodeFileSectionsFrmJson(\r\n            srvcConfigData?.daily_update_issue_form_fields\r\n        );\r\n        return dailyUpdateIssueFileMeta;\r\n    };\r\n\r\n    const getFormIssueMicMeta = () => {\r\n        let dailyUpdateIssueMicMeta = [];\r\n        dailyUpdateIssueMicMeta = decodeMicSectionsFrmJson(\r\n            srvcConfigData?.daily_update_issue_form_fields\r\n        );\r\n        return dailyUpdateIssueMicMeta;\r\n    };\r\n\r\n    const getFormFileMeta = () => {\r\n        let dailyUpdateFileMeta = [];\r\n        dailyUpdateFileMeta = isSrvcPrvdrTab\r\n            ? decodeFileSectionsFrmJson(\r\n                  srvcConfigData?.sp_daily_update_form_fields\r\n              )\r\n            : decodeFileSectionsFrmJson(\r\n                  srvcConfigData?.daily_update_form_fields\r\n              );\r\n        return dailyUpdateFileMeta;\r\n    };\r\n\r\n    const getFormMicMeta = () => {\r\n        let dailyUpdateMicMeta = [];\r\n        dailyUpdateMicMeta = isSrvcPrvdrTab\r\n            ? decodeMicSectionsFrmJson(\r\n                  srvcConfigData?.sp_daily_update_form_fields\r\n              )\r\n            : decodeMicSectionsFrmJson(\r\n                  srvcConfigData?.daily_update_form_fields\r\n              );\r\n        return dailyUpdateMicMeta;\r\n    };\r\n\r\n    const onFilesChanged = (section, files, deletedFileUrl = undefined) => {\r\n        onFilesChangedFn(\r\n            section,\r\n            files,\r\n            filesBySection,\r\n            setFilesBySection,\r\n            onFormValueChange,\r\n            deletedFileUrl\r\n        );\r\n    };\r\n\r\n    const onMicFilesChanged = (section, files) => {\r\n        let newFilesBySection = _.cloneDeep(micRecordingsBySection);\r\n        let newFiles = [];\r\n\r\n        if (\r\n            micRecordingsBySection[section] &&\r\n            files?.length >= micRecordingsBySection[section]?.length\r\n        ) {\r\n            newFiles = files.filter(\r\n                (singleFile) =>\r\n                    !micRecordingsBySection[section].includes(singleFile)\r\n            );\r\n        } else {\r\n            newFiles = files;\r\n        }\r\n        newFilesBySection[section] =\r\n            micRecordingsBySection[section] &&\r\n            files.length >= micRecordingsBySection[section].length\r\n                ? [...micRecordingsBySection[section], ...newFiles]\r\n                : files;\r\n        //    console.log('newfile',newFilesBySection)\r\n        setMicRecordingsBySection(newFilesBySection);\r\n        onFormValueChange();\r\n    };\r\n\r\n    const onFileUploaderReadyChange = (section, isReady) => {\r\n        let newSectionWiseReady = sectionWiseUploaderReady;\r\n        newSectionWiseReady[section] = isReady;\r\n        setSectionWiseUploaderReady(newSectionWiseReady);\r\n        setAllFileUploadersReady(getAllFileUploadersReady());\r\n    };\r\n\r\n    const onMicFileUploaderReadyChange = (section, isReady) => {\r\n        let newSectionWiseReady = sectionWiseMicUploaderReady;\r\n        newSectionWiseReady[section] = isReady;\r\n        setSectionWiseMicUploaderReady(newSectionWiseReady);\r\n        setAllMicRecordingsUploadersReady(getAllMicRecordingsUploadersReady());\r\n    };\r\n\r\n    const getAllFileUploadersReady = () => {\r\n        let notReady = false;\r\n        Object.keys(sectionWiseUploaderReady).map((section) => {\r\n            if (!sectionWiseUploaderReady[section]) {\r\n                notReady = true;\r\n            }\r\n        });\r\n        return !notReady;\r\n    };\r\n\r\n    const getAllMicRecordingsUploadersReady = () => {\r\n        let notReady = false;\r\n        Object.keys(sectionWiseMicUploaderReady).map((section) => {\r\n            if (!sectionWiseMicUploaderReady[section]) {\r\n                notReady = true;\r\n            }\r\n        });\r\n        return !notReady;\r\n    };\r\n\r\n    let prefillFormData =\r\n        convertDateFieldsToMoments(\r\n            existingDailyStatusObj[day],\r\n            meta().fields\r\n        ) || {};\r\n    // TODO convertdatefields to moment\r\n    //set no remarks by default\r\n    const remark = form?.getFieldValue('day_remarks');\r\n    if (prefillFormData?.day_remarks == undefined && remark == undefined) {\r\n        form.setFieldsValue({\r\n            day_remarks: 'No remarks',\r\n        });\r\n    }\r\n\r\n    const getMeta = (fields, prefillFormData) => {\r\n        if (!canCurrentUserEdit) {\r\n            const viewMeta = getViewModeMetaFrFormMeta(fields, prefillFormData);\r\n            if (viewMeta.length > 0) {\r\n                return viewMeta;\r\n            } else {\r\n                return [{ render: () => <div>No updates added</div> }];\r\n            }\r\n        } else {\r\n            return meta();\r\n        }\r\n    };\r\n\r\n    // if line item deleted after updating daily update form then delete that line item id from daily update form (line item progress selection)\r\n    const updatedSelectedLineItems =\r\n        prefillFormData?.selected_line_items?.filter((singleLineItem) => {\r\n            return getLineItemOptions().some(\r\n                (option) => option.value === singleLineItem\r\n            );\r\n        });\r\n\r\n    prefillFormData.selected_line_items = updatedSelectedLineItems;\r\n\r\n    return (\r\n        <div className=\"gx-mt-3\">\r\n            <Form\r\n                initialValues={prefillFormData}\r\n                form={form}\r\n                layout=\"vertical\"\r\n                onFinish={onSubmit}\r\n                onValuesChange={onFormValueChange}\r\n            >\r\n                <FormBuilder\r\n                    meta={getMeta(meta().fields, prefillFormData)}\r\n                    form={form}\r\n                    viewMode={!canCurrentUserEdit}\r\n                />\r\n\r\n                <div className=\"gx-mt-0\">\r\n                    {(!allFileUploadersReady ||\r\n                        !allMicRecordingsUploadersReady) && <Spin></Spin>}\r\n                    {canCurrentUserEdit && (\r\n                        <Button\r\n                            type=\"primary\"\r\n                            htmlType=\"submit\"\r\n                            disabled={\r\n                                isFormSubmitting ||\r\n                                !allFileUploadersReady ||\r\n                                !isFormChanged ||\r\n                                !allMicRecordingsUploadersReady\r\n                            }\r\n                        >\r\n                            Save\r\n                        </Button>\r\n                    )}\r\n                    {isFormSubmitting ? (\r\n                        <div className=\"gx-loader-view gx-loader-position\">\r\n                            <CircularProgress />\r\n                        </div>\r\n                    ) : null}\r\n                    {error ? <p className=\"gx-text-red\">{error}</p> : null}\r\n                </div>\r\n            </Form>\r\n            {/* {JSON.stringify(editorDayDetails)} */}\r\n        </div>\r\n    );\r\n};\r\n\r\nconst onFilesChangedFn = (\r\n    section,\r\n    files,\r\n    filesBySection,\r\n    setFilesBySection,\r\n    onFormValueChange,\r\n    deletedFileUrl\r\n) => {\r\n    let newFilesBySection = _.cloneDeep(filesBySection);\r\n    let newFiles = [];\r\n\r\n    if (\r\n        filesBySection[section] &&\r\n        files?.length >= filesBySection[section]?.length\r\n    ) {\r\n        newFiles = files.filter(\r\n            (singleFile) => !filesBySection[section].includes(singleFile)\r\n        );\r\n    } else {\r\n        if (filesBySection[section]) {\r\n            newFiles = [...new Set(files.concat(filesBySection[section]))];\r\n        } else {\r\n            newFiles = files;\r\n        }\r\n    }\r\n\r\n    // Get the existing files for the specified section from the filesBySection object,\r\n    // or initialize an empty array if no files exist for the section.\r\n    const existingFiles = filesBySection[section] || [];\r\n\r\n    // Combine the current files (passed as an argument) with the existing files,\r\n    // and use a Set to ensure unique values in the resulting array.\r\n    // Create a new Set with deep-copied objects\r\n    const combinedFiles = [...new Set([...files, ...existingFiles])];\r\n\r\n    // Check if there are existing files for the section and if the number of files\r\n    // passed as an argument is greater than or equal to the number of existing files.\r\n    // If true, update the files for the section with the combined unique files;\r\n    // otherwise, use the files passed as an argument.\r\n    newFilesBySection[section] =\r\n        filesBySection[section] && files.length >= existingFiles.length\r\n            ? combinedFiles\r\n            : files;\r\n\r\n    // If deletedFileUrl exits then remove from combinedFiles\r\n    if (deletedFileUrl) {\r\n        newFilesBySection[section] = combinedFiles?.filter(\r\n            (singleFile) => singleFile !== deletedFileUrl\r\n        );\r\n    }\r\n    setFilesBySection(newFilesBySection);\r\n    onFormValueChange();\r\n};\r\n\r\n// Export functions and components\r\nexport { onFilesChangedFn };\r\nexport default DailyReqStatusEditor;\r\n"], "mappings": ";AAAA,SAASA,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,QAAQ,MAAM;AAC7E,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,CAAC,IAAIC,MAAM,QAAQ,QAAQ;AAClC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,SACIC,uBAAuB,EACvBC,yBAAyB,EACzBC,wBAAwB,QACrB,wDAAwD;AAC/D,OAAOC,UAAU,MAAM,yDAAyD;AAChF,OAAOC,YAAY,MAAM,gDAAgD;AACzE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SACIC,0BAA0B,EAC1BC,yBAAyB,EACzBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,qCAAqC,EACrCC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,uBAAuB,EACvBC,yBAAyB,EACzBC,YAAY,EACZC,oBAAoB,EACpBC,mBAAmB,EACnBC,+BAA+B,QAC5B,0BAA0B;AACjC,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,mCAAmC,QAAQ,0BAA0B;AAC9E,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,SAASC,uBAAuB,QAAQ,eAAe;AACvD,OAAOC,UAAU,MAAM,+CAA+C;AACtE,OAAOC,MAAM,MAAM,QAAQ;AAE3B,MAAMC,eAAe,GAAG,mCAAmC;AAC3D,MAAMC,SAAS,GAAIC,KAAK,IAAK,GAAGA,KAAK,GAAG;AAExC,MAAMC,oBAAoB,GAAGA,CAAC;EAC1BC,gBAAgB;EAChBC,cAAc;EACdC,OAAO;EACPC,QAAQ;EACRC,oBAAoB;EACpBC;AACJ,CAAC,KAAK;EAAA,IAAAC,qBAAA;EACF,MAAMC,GAAG,GAAGP,gBAAgB,CAACQ,aAAa;EAC1C,MAAMC,YAAY,GAAGP,OAAO,CAACQ,SAAS;EACtC,IAAIC,sBAAsB,GACtB,CAACN,cAAc,GACTI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,uBAAuB,GACrCH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEI,oBAAoB,KAAK,CAAC,CAAC;EACnD;EACAF,sBAAsB,CAACJ,GAAG,CAAC,GAAGzB,0BAA0B,CACpD6B,sBAAsB,CAACJ,GAAG,CAC9B,CAAC;EAED,MAAM,CAACO,IAAI,CAAC,GAAGvD,IAAI,CAACwD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAACiD,SAAS,CAAC;EAC7C,MAAM,CACFC,iCAAiC,EACjCC,oCAAoC,CACvC,GAAGnD,QAAQ,CAACiD,SAAS,CAAC;EACvB,MAAMG,WAAW,GAAGzD,WAAW,CAAC0D,cAAc,CAAC,CAAC;EAChD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC4D,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG7D,QAAQ,CACpE,CAAC,CACL,CAAC;EACD,MAAM,CAAC8D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACgE,2BAA2B,EAAEC,8BAA8B,CAAC,GAC/DjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,MAAM,CAACkE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACoE,8BAA8B,EAAEC,iCAAiC,CAAC,GACrErE,QAAQ,CAAC,IAAI,CAAC;EAClB,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC;EACtE,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAACiD,SAAS,CAAC;EACzE,MAAM,CAACyB,0BAA0B,EAAEC,6BAA6B,CAAC,GAC7D3E,QAAQ,CAAC,KAAK,CAAC;EACnB,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,IAAIkF,sBAAsB,GAAG,CAAC,CAAC;EAC/B;EACA;EACA;EACA,MAAMC,sBAAsB,GACxB,CAAAnD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoD,yBAAyB,KAAI,EAAE;EACnD,MAAMC,kBAAkB,GAAG9E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+E,wBAAwB,CAC9DH,sBACJ,CAAC;EACD,IAAII,eAAe;EACnB;;EAEAxF,SAAS,CAAC,MAAM;IACZqD,WAAW,CAAC,CAAC;IACboC,sBAAsB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACnC,IAAIA,wBAAwB,GAAGvF,uBAAuB,CAClD8B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0D,8BACpB,CAAC;IACD,OAAOD,wBAAwB;EACnC,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIA,mBAAmB,GAAGvD,cAAc,GAClClC,uBAAuB,CACnB8B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4D,2BACpB,CAAC,GACD1F,uBAAuB,CAAC8B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6D,wBAAwB,CAAC;IACvE,OAAOF,mBAAmB;EAC9B,CAAC;EAED,MAAMH,sBAAsB,GAAGA,CAAA,KAAM;IACjC,IAAIM,0BAA0B,GAAGC,2BAA2B,CAAC,CAAC;;IAE9D;IACAD,0BAA0B,CAACE,OAAO,CAAEC,kBAAkB,IAAK;MACvD,IAAIC,UAAU,GAAGC,qBAAqB,CAAC7D,GAAG,EAAE2D,kBAAkB,CAACG,GAAG,CAAC;MACnE;MACA,IACI1D,sBAAsB,CAACJ,GAAG,CAAC,IAAIW,SAAS,IACxCP,sBAAsB,CAACJ,GAAG,CAAC,CAAC2D,kBAAkB,CAACG,GAAG,CAAC,IAAInD,SAAS,EAClE;QACEJ,IAAI,CAACwD,cAAc,CAAC;UAChB,CAACJ,kBAAkB,CAACG,GAAG,GAAGF;QAC9B,CAAC,CAAC;QACF;MACJ,CAAC,MAAM,IAAIxD,sBAAsB,CAACJ,GAAG,CAAC,IAAIW,SAAS,EAAE;QACjDJ,IAAI,CAACwD,cAAc,CAAC;UAChB,CAACJ,kBAAkB,CAACG,GAAG,GAAGF;QAC9B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;;IAEF;IACA,IAAII,uBAAuB,GAAGH,qBAAqB,CAC/C7D,GAAG,EACH,cACJ,CAAC;IACD,IACII,sBAAsB,CAACJ,GAAG,CAAC,IAAIW,SAAS,IACxCP,sBAAsB,CAACJ,GAAG,CAAC,CAAC,cAAc,CAAC,IAAIW,SAAS,EAC1D;MACEJ,IAAI,CAACwD,cAAc,CAAC;QAChBE,YAAY,EAAED;MAClB,CAAC,CAAC;IACN,CAAC,MAAM,IAAI5D,sBAAsB,CAACJ,GAAG,CAAC,IAAIW,SAAS,EAAE;MACjDJ,IAAI,CAACwD,cAAc,CAAC;QAChBE,YAAY,EAAED;MAClB,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAIC,IAAI,IAAK;IACpC,IAAIC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAClE,sBAAsB,CAAC;IAElD,IAAImE,aAAa,GAAG,IAAI;IACxB,IAAIC,iBAAiB,GAAGJ,QAAQ,CAACK,SAAS,CAAC,UAAUX,GAAG,EAAE;MACtD,OAAOA,GAAG,KAAKK,IAAI;IACvB,CAAC,CAAC;IACF,IAAIA,IAAI,GAAGC,QAAQ,CAACA,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC,EAAE;MACtCH,aAAa,GAAGH,QAAQ,CAACA,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC;MAC7C,OAAOH,aAAa;IACxB,CAAC,MAAM,IAAIJ,IAAI,IAAIC,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC5BG,aAAa,GAAG,CAAC;MACjB,OAAOA,aAAa;IACxB;IAEAA,aAAa,GACTH,QAAQ,CACJA,QAAQ,CAACK,SAAS,CAAC,UAAUX,GAAG,EAAE;MAC9B,OAAOA,GAAG,GAAGK,IAAI;IACrB,CAAC,CAAC,GAAG,CAAC,CACT;IAEL,IAAIK,iBAAiB,IAAI,CAAC,EAAE;MACxBD,aAAa,GAAGH,QAAQ,CAACI,iBAAiB,GAAG,CAAC,CAAC;IACnD;IAEA,OAAOD,aAAa;EACxB,CAAC;;EAED;EACA,MAAMV,qBAAqB,GAAGA,CAAC7D,GAAG,EAAE2E,QAAQ,KAAK;IAC7C,IAAIJ,aAAa,GAAGL,qBAAqB,CAAClE,GAAG,CAAC;IAC9C,IAAI4E,YAAY;IAEhB,IAAIhB,UAAU,GAAG,CAAC;IAElB,IAAIW,aAAa,EAAE;MAAA,IAAAM,aAAA;MACfD,YAAY,GAAGxE,sBAAsB,CAACmE,aAAa,CAAC;MACpDX,UAAU,IAAAiB,aAAA,GAAGD,YAAY,cAAAC,aAAA,uBAAZA,aAAA,CAAeF,QAAQ,CAAC;IACzC;IACA,OAAOf,UAAU;EACrB,CAAC;EAED,MAAMkB,oBAAoB,GAAGA,CAACvF,KAAK,EAAEoF,QAAQ,EAAEf,UAAU,EAAEmB,UAAU,KAAK;IACtE,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,MAAMzB,0BAA0B,GAAGC,2BAA2B,CAAC,CAAC;IAChED,0BAA0B,CAACE,OAAO,CAAEC,kBAAkB,IAAK;MACvD,IAAIuB,YAAY,GAAG3E,IAAI,CAAC4E,aAAa,CAACxB,kBAAkB,CAACG,GAAG,CAAC,IAAI,CAAC;MAClEmB,gBAAgB,GAAGA,gBAAgB,GAAGC,YAAY;MAClDF,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,MAAMI,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,gBAAgB,GAAGD,YAAY,CAAC;IACxDzE,IAAI,CAACwD,cAAc,CAAC;MAChBE,YAAY,EAAEmB;IAClB,CAAC,CAAC;EACN,CAAC;EAED,MAAMG,6BAA6B,GAAGA,CAACC,MAAM,EAAExF,GAAG,EAAE2E,QAAQ,KAAK;IAC7D;IACA,MAAMc,4BAA4B,GAAG,CAAC,CAAC;IAEvCpB,MAAM,CAACC,IAAI,CAAClE,sBAAsB,CAAC,CAC9BsF,IAAI,CACD,CAACC,CAAC,EAAEC,CAAC,KAAKxF,sBAAsB,CAACuF,CAAC,CAAC,GAAGvF,sBAAsB,CAACwF,CAAC,CAClE,CAAC,CACAlC,OAAO,CAAEI,GAAG,IAAK;MACd2B,4BAA4B,CAAC3B,GAAG,CAAC,GAAG1D,sBAAsB,CAAC0D,GAAG,CAAC;IACnE,CAAC,CAAC;IAEN,IAAI+B,KAAK,GAAGxB,MAAM,CAACC,IAAI,CAACmB,4BAA4B,CAAC,IAAI,EAAE;IAC3D,IAAII,KAAK,CAACnB,MAAM,IAAI,CAAC,EAAE;MACnB;IACJ;;IAEA;IACA,IAAIoB,6BAA6B,GAAGxI,CAAC,CAACyI,aAAa,CAC/CN,4BACJ,CAAC;IACD,IAAIK,6BAA6B,CAAC9F,GAAG,CAAC,IAAIW,SAAS,EAAE;MACjDmF,6BAA6B,CAAC9F,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C;IACA8F,6BAA6B,CAAC9F,GAAG,CAAC,CAAC2E,QAAQ,CAAC,GAAGa,MAAM;;IAErD;;IAEA,IAAIQ,IAAI,GAAG3B,MAAM,CAACC,IAAI,CAACwB,6BAA6B,CAAC;IACrDE,IAAI,CAACN,IAAI,CAAC,CAAC;IAEX,IAAIO,QAAQ,GAAGD,IAAI,CAAC,CAAC,CAAC;IACtB,IAAIE,aAAa,GAAGJ,6BAA6B,CAACG,QAAQ,CAAC,CAACtB,QAAQ,CAAC;IAErE,IAAIwB,QAAQ,GAAG,CAAC;IAEhBH,IAAI,CAACI,GAAG,CAAEjC,IAAI,IAAK;MACf,IAAI2B,6BAA6B,CAAC3B,IAAI,CAAC,CAACQ,QAAQ,CAAC,GAAGuB,aAAa,EAAE;QAC/D,IAAI,CAAC1D,YAAY,EAAE;UACfvF,OAAO,CAACwD,KAAK,CACT,GAAG0D,IAAI,4EACX,CAAC;UACD1B,eAAe,CAAC,IAAI,CAAC;QACzB;QACA0D,QAAQ,EAAE;MACd;MACAD,aAAa,GAAGJ,6BAA6B,CAAC3B,IAAI,CAAC,CAACQ,QAAQ,CAAC;IACjE,CAAC,CAAC;IACF,IAAI1B,eAAe,EAAE;MACjBoD,YAAY,CAACpD,eAAe,CAAC;IACjC;IACAA,eAAe,GAAGqD,UAAU,CAAC,MAAM;MAC/B,IAAIH,QAAQ,GAAG,CAAC,EAAE;QAAA,IAAAI,qBAAA;QACdhG,IAAI,CAACwD,cAAc,CAAC;UAChB,CAACY,QAAQ,GACL,CAAAc,4BAA4B,aAA5BA,4BAA4B,wBAAAc,qBAAA,GAA5Bd,4BAA4B,CAAGzF,GAAG,CAAC,cAAAuG,qBAAA,uBAAnCA,qBAAA,CAAsC5B,QAAQ,CAAC,KAC/Cd,qBAAqB,CAAC7D,GAAG,EAAE2E,QAAQ;QAC3C,CAAC,CAAC;QACFlC,eAAe,CAAC,KAAK,CAAC;MAC1B,CAAC,MAAM;QACHlC,IAAI,CAACwD,cAAc,CAAC;UAChB,CAACY,QAAQ,GAAGa;QAChB,CAAC,CAAC;MACN;MACAV,oBAAoB,CAACU,MAAM,EAAEb,QAAQ,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;EACX,CAAC;EAED,MAAMlB,2BAA2B,GAAGA,CAAA,KAAM;IAAA,IAAA+C,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IACtC,IAAIC,iBAAiB,GAAG,EAAE;IAC1B,IAAIC,oBAAoB,GAAG/G,cAAc,GACnCH,OAAO,aAAPA,OAAO,wBAAA6G,kBAAA,GAAP7G,OAAO,CAAEQ,SAAS,cAAAqG,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBM,aAAa,cAAAL,qBAAA,uBAAjCA,qBAAA,CAAmCtG,SAAS,GAC5CR,OAAO,aAAPA,OAAO,wBAAA+G,mBAAA,GAAP/G,OAAO,CAAEQ,SAAS,cAAAuG,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBK,UAAU,cAAAJ,qBAAA,uBAA9BA,qBAAA,CAAgCxG,SAAS;IAC/C,IAAI0G,oBAAoB,EAAE;MACtB,IAAIG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAC3BxH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyH,0BACpB,CAAC;MACD9C,MAAM,CAACC,IAAI,CAAC0C,cAAc,CAAC,CAACtD,OAAO,CAAE0D,cAAc,IAAK;QACpD,IAAIC,WAAW,GAAGL,cAAc,CAACI,cAAc,CAAC;QAChD,IAAIE,gBAAgB,GAAGD,WAAW,CAACE,kBAAkB;QACrD,MAAMC,MAAM,GACR5J,uBAAuB,CAACyJ,WAAW,CAACG,MAAM,CAAC,IAAI,EAAE;QACrD,MAAMC,gBAAgB,GAAG,CAAC,CAAC;QAC3BD,MAAM,CAAC9D,OAAO,CAAEgE,eAAe,IAAK;UAChCD,gBAAgB,CAACC,eAAe,CAACC,KAAK,CAAC,GACnCD,eAAe,CAAC5D,GAAG;QAC3B,CAAC,CAAC;QAEF,IAAI8D,qBAAqB,GACrBf,oBAAoB,CAACO,cAAc,CAAC;QACxC,IACIQ,qBAAqB,IACrBC,KAAK,CAACC,OAAO,CAACF,qBAAqB,CAAC,EACtC;UACEA,qBAAqB,CAAClE,OAAO,CACzB,CAACqE,mBAAmB,EAAEC,KAAK,KAAK;YAC5B,IAAIL,KAAK,GAAG,QAAQK,KAAK,GAAG,CAAC,EAAE;YAC/B,IAAIC,mBAAmB,GAAGxJ,uBAAuB,CAC7C+I,MAAM,EACNO,mBACJ,CAAC;YACD,IACIT,gBAAgB,IAChBA,gBAAgB,CAAC5C,MAAM,GAAG,CAAC,EAC7B;cACEiD,KAAK,GAAG/I,oBAAoB,CACxB0I,gBAAgB,EAChBG,gBAAgB,EAChBQ,mBACJ,CAAC;YACL;YACA,IAAIC,eAAe,GAAG,YAAYH,mBAAmB,CAACI,cAAc,EAAE;YACtEvB,iBAAiB,CAACwB,IAAI,CAAC;cACnBtE,GAAG,EAAEoE,eAAe;cACpBP,KAAK,EAAEA,KAAK;cAAE;cACdU,KAAK,EAAEV,KAAK;cACZW,MAAM,EAAEtK,YAAY;cACpBuK,WAAW,EAAE;gBACTC,OAAO,EAAE;kBAAElJ,SAAS,EAAEA;gBAAU,CAAC;gBACjCM,QAAQ,EAAGL,KAAK,IAAK;kBACjB;kBACA,IACIG,cAAc,CAAC+I,0BAA0B,IACzC,aAAa,EACf;oBACElD,6BAA6B,CACzBhG,KAAK,EACLS,GAAG,EACHkI,eACJ,CAAC;kBACL;kBACApD,oBAAoB,CAChBvF,KAAK,EACL2I,eACJ,CAAC;gBACL;cACJ;YACJ,CAAC,CAAC;UACN,CACJ,CAAC;QACL;MACJ,CAAC,CAAC;IACN;IACA,OAAOtB,iBAAiB;EAC5B,CAAC;EACD,MAAM8B,mCAAmC,GAAIC,mBAAmB,IAAK;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IACjE,IAAInC,iBAAiB,GAAG,EAAE;IAE1B,IAAI,CAAC+B,mBAAmB,IAAIA,mBAAmB,CAACjE,MAAM,KAAK,CAAC,EAAE;MAC1D,OAAOkC,iBAAiB;IAC5B;IAEA,IAAIC,oBAAoB,GAAG/G,cAAc,GACnCH,OAAO,aAAPA,OAAO,wBAAAiJ,mBAAA,GAAPjJ,OAAO,CAAEQ,SAAS,cAAAyI,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB9B,aAAa,cAAA+B,qBAAA,uBAAjCA,qBAAA,CAAmC1I,SAAS,GAC5CR,OAAO,aAAPA,OAAO,wBAAAmJ,mBAAA,GAAPnJ,OAAO,CAAEQ,SAAS,cAAA2I,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB/B,UAAU,cAAAgC,qBAAA,uBAA9BA,qBAAA,CAAgC5I,SAAS;IAE/C,IAAI0G,oBAAoB,EAAE;MACtB,IAAIG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAC3BxH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyH,0BACpB,CAAC;MAED9C,MAAM,CAACC,IAAI,CAAC0C,cAAc,CAAC,CAACtD,OAAO,CAAE0D,cAAc,IAAK;QACpD,IAAIC,WAAW,GAAGL,cAAc,CAACI,cAAc,CAAC;QAChD,IAAIE,gBAAgB,GAAGD,WAAW,CAACE,kBAAkB;QACrD,MAAMC,MAAM,GACR5J,uBAAuB,CAACyJ,WAAW,CAACG,MAAM,CAAC,IAAI,EAAE;QACrD,MAAMC,gBAAgB,GAAG,CAAC,CAAC;QAC3BD,MAAM,CAAC9D,OAAO,CAAEgE,eAAe,IAAK;UAChCD,gBAAgB,CAACC,eAAe,CAACC,KAAK,CAAC,GACnCD,eAAe,CAAC5D,GAAG;QAC3B,CAAC,CAAC;QAEF,IAAI8D,qBAAqB,GACrBf,oBAAoB,CAACO,cAAc,CAAC;QACxC,IACIQ,qBAAqB,IACrBC,KAAK,CAACC,OAAO,CAACF,qBAAqB,CAAC,EACtC;UACEA,qBAAqB,CAAClE,OAAO,CACzB,CAACqE,mBAAmB,EAAEC,KAAK,KAAK;YAC5B;YACA,MAAMgB,UAAU,GAAG,GAAG5B,cAAc,IAAIW,mBAAmB,CAACI,cAAc,EAAE;YAC5E,IAAI,CAACQ,mBAAmB,CAACM,QAAQ,CAACD,UAAU,CAAC,EAAE;cAC3C,OAAO,CAAC;YACZ;YAEA,IAAIrB,KAAK,GAAG,QAAQK,KAAK,GAAG,CAAC,EAAE;YAC/B,IAAIC,mBAAmB,GAAGxJ,uBAAuB,CAC7C+I,MAAM,EACNO,mBACJ,CAAC;YACD,IACIT,gBAAgB,IAChBA,gBAAgB,CAAC5C,MAAM,GAAG,CAAC,EAC7B;cACEiD,KAAK,GAAG/I,oBAAoB,CACxB0I,gBAAgB,EAChBG,gBAAgB,EAChBQ,mBACJ,CAAC;YACL;YACA,IAAIC,eAAe,GAAG,YAAYH,mBAAmB,CAACI,cAAc,EAAE;YACtEvB,iBAAiB,CAACwB,IAAI,CAAC;cACnBtE,GAAG,EAAEoE,eAAe;cACpBgB,SAAS,EAAE,oCAAoC;cAC/CvB,KAAK,EAAEA,KAAK;cAAE;cACdU,KAAK,EAAEV,KAAK;cACZW,MAAM,EAAEtK,YAAY;cACpBuK,WAAW,EAAE;gBACTC,OAAO,EAAE;kBAAElJ,SAAS,EAAEA;gBAAU,CAAC;gBAEjCM,QAAQ,EAAGL,KAAK,IAAK;kBACjB;kBACA,IACIG,cAAc,CAAC+I,0BAA0B,IACzC,aAAa,EACf;oBACElD,6BAA6B,CACzBhG,KAAK,EACLS,GAAG,EACHkI,eACJ,CAAC;kBACL;kBACApD,oBAAoB,CAChBvF,KAAK,EACL2I,eACJ,CAAC;gBACL;cACJ;YACJ,CAAC,CAAC;UACN,CACJ,CAAC;QACL;MACJ,CAAC,CAAC;IACN;IACA,OAAOtB,iBAAiB;EAC5B,CAAC;EAED,MAAMuC,4BAA4B,GAAGA,CAACC,YAAY,EAAEtF,GAAG,KAAK;IACxD7B,yBAAyB,CAAC,EAAE,CAAC;IAE7B,IAAIW,sBAAsB,CAACkB,GAAG,CAAC,IAAI1C,cAAc,CAAC0C,GAAG,CAAC,EAAE;MACpD,IAAIuF,YAAY,GAAG,CAAC;MACpBjI,cAAc,CAAC0C,GAAG,CAAC,GAAG1C,cAAc,CAAC0C,GAAG,CAAC,CAACwF,MAAM,CAAEC,OAAO,IAAK;QAC1D,IAAI3G,sBAAsB,CAACkB,GAAG,CAAC,CAACmF,QAAQ,CAACM,OAAO,CAAC,EAAE;UAC/CF,YAAY,EAAE;UACd,OAAO,KAAK,CAAC,CAAC;QAClB;QACA,OAAO,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MACF,IAAIG,yBAAyB,GAAGlM,CAAC,CAACmM,SAAS,CAACrI,cAAc,CAAC;MAC3D,IAAIiI,YAAY,IAAI,CAAC,EAAE;QACnBG,yBAAyB,CAAC1F,GAAG,CAAC,GAAG,CAC7B,GAAG,IAAI4F,GAAG,CACNF,yBAAyB,CAAC1F,GAAG,CAAC,CAAC6F,MAAM,CACjC/G,sBAAsB,CAACkB,GAAG,CAC9B,CACJ,CAAC,CACJ;MACL,CAAC,MAAM;QACH0F,yBAAyB,CAAC1F,GAAG,CAAC,GAAG,CAC7B,GAAG0F,yBAAyB,CAAC1F,GAAG,CAAC,CACpC;MACL;MACAzC,iBAAiB,CAACmI,yBAAyB,CAAC;MAC5C7G,gBAAgB,CAAC6G,yBAAyB,CAAC;MAC3CI,iBAAiB,CAAC,CAAC;IACvB,CAAC,MAAM,IAAIhH,sBAAsB,CAACkB,GAAG,CAAC,EAAE;MACpC,IAAI0F,yBAAyB,GAAGlM,CAAC,CAACmM,SAAS,CAACrI,cAAc,CAAC;MAC3DoI,yBAAyB,CAAC1F,GAAG,CAAC,GAAG,CAC7B,GAAGlB,sBAAsB,CAACkB,GAAG,CAAC,EAC9B,GAAGsF,YAAY,CAClB;MACD/H,iBAAiB,CAACmI,yBAAyB,CAAC;MAC5C7G,gBAAgB,CAAC6G,yBAAyB,CAAC;MAC3CI,iBAAiB,CAAC,CAAC;IACvB;EACJ,CAAC;EAED,MAAMC,4BAA4B,GAAI3H,mBAAmB,IAAK;IAC1D;IACA,IAAIA,mBAAmB,IAAIvB,SAAS,EAAE;MAClC0B,6BAA6B,CAAC,IAAI,CAAC;MACnCxB,oCAAoC,CAACF,SAAS,CAAC;MAC/C,IAAImJ,MAAM,GAAG,CAAC,CAAC;MAEf,MAAMC,UAAU,GAAIC,IAAI,IAAK;QACzB7H,sBAAsB,CAAC6H,IAAI,CAACC,IAAI,CAAC;QACjC5H,6BAA6B,CAAC,KAAK,CAAC;MACxC,CAAC;MAED,MAAM6H,OAAO,GAAIzJ,KAAK,IAAK;QACvBxD,OAAO,CAACwD,KAAK,CAAC,gBAAgB,CAAC;QAC/BI,oCAAoC,CAChC9B,UAAU,CAACoL,oBAAoB,CAAC1J,KAAK,CACzC,CAAC;QACD4B,6BAA6B,CAAC,KAAK,CAAC;MACxC,CAAC;MACD,IAAI+H,GAAG,GACH/K,eAAe,GACf,GAAG,GACHM,OAAO,CAAC0K,EAAE,GACV,GAAG,GACH5K,gBAAgB,CAACQ,aAAa;MAClC;MACAlB,UAAU,CAACuL,cAAc,CAACF,GAAG,EAAEN,MAAM,EAAEC,UAAU,EAAEG,OAAO,CAAC;IAC/D;EACJ,CAAC;EAED,MAAMK,0BAA0B,GAAGA,CAACC,WAAW,EAAEC,KAAK,KAAK;IAAA,IAAAC,qBAAA;IACvD,IAAI9H,sBAAsB,CAAC4H,WAAW,CAAC,IAAI7J,SAAS,EAAE;MAClDiC,sBAAsB,CAAC4H,WAAW,CAAC,GAAG,CAACC,KAAK,CAAC;IACjD,CAAC,MAAM,IAAI,GAAAC,qBAAA,GAAC9H,sBAAsB,CAAC4H,WAAW,CAAC,cAAAE,qBAAA,uBAAnCA,qBAAA,CAAqCzB,QAAQ,CAACwB,KAAK,CAAC,GAAE;MAC9D7H,sBAAsB,CAAC4H,WAAW,CAAC,CAACpC,IAAI,CAACqC,KAAK,CAAC;IACnD,CAAC,MAAM;MAAA,IAAAE,sBAAA;MACH,MAAM3C,KAAK,IAAA2C,sBAAA,GAAG/H,sBAAsB,CAAC4H,WAAW,CAAC,cAAAG,sBAAA,uBAAnCA,sBAAA,CAAqCC,OAAO,CAACH,KAAK,CAAC;MACjE,IAAIzC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ;QACApF,sBAAsB,CAAC4H,WAAW,CAAC,CAACK,MAAM,CAAC7C,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D;IACJ;EACJ,CAAC;EAED,MAAM8C,mBAAmB,GAAGA,CAAA,KACxB,CAACvK,IAAI,CAAC4E,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,KAAK,KAAK;;EAExD;EACA,MAAM4F,+BAA+B,GAAIC,iBAAiB,IAAK;IAAA,IAAAC,qBAAA;IAC3D,MAAMC,aAAa,GACf,CAAAxI,aAAa,aAAbA,aAAa,wBAAAuI,qBAAA,GAAbvI,aAAa,CAAGsI,iBAAiB,CAAClH,GAAG,CAAC,cAAAmH,qBAAA,uBAAtCA,qBAAA,CAAwCvG,MAAM,IAAG,CAAC;IACtDsG,iBAAiB,CAACG,QAAQ,GAAGD,aAAa,GACpC,KAAK,GACLF,iBAAiB,CAACG,QAAQ;EACpC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAC7B,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,aAAa,GAAG5L,cAAc,GAC5BH,OAAO,aAAPA,OAAO,wBAAA0L,mBAAA,GAAP1L,OAAO,CAAEQ,SAAS,cAAAkL,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBvE,aAAa,cAAAwE,qBAAA,uBAAjCA,qBAAA,CAAmCnL,SAAS,GAC5CR,OAAO,aAAPA,OAAO,wBAAA4L,mBAAA,GAAP5L,OAAO,CAAEQ,SAAS,cAAAoL,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBxE,UAAU,cAAAyE,qBAAA,uBAA9BA,qBAAA,CAAgCrL,SAAS;IAE/C,IAAI,CAACuL,aAAa,EAAE;MAChB,OAAOD,OAAO;IAClB;IAEA,IAAIzE,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI;MACAA,cAAc,GAAGC,IAAI,CAACC,KAAK,CACvB,CAAAxH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyH,0BAA0B,KAAI,IAClD,CAAC;IACL,CAAC,CAAC,OAAOwE,CAAC,EAAE;MACRC,OAAO,CAACC,GAAG,CACP,wDAAwD,EACxDF,CACJ,CAAC;MACD,OAAOF,OAAO;IAClB;;IAEA;IACApH,MAAM,CAACC,IAAI,CAAC0C,cAAc,CAAC,CAACtD,OAAO,CAAE0D,cAAc,IAAK;MACpD,IACI,CAACsE,aAAa,CAACtE,cAAc,CAAC,IAC9B,CAACS,KAAK,CAACC,OAAO,CAAC4D,aAAa,CAACtE,cAAc,CAAC,CAAC,EAC/C;QACE;MACJ;MAEA,IAAIC,WAAW,GAAGL,cAAc,CAACI,cAAc,CAAC,IAAI,CAAC,CAAC;MACtD,IAAIE,gBAAgB,GAAGD,WAAW,CAACE,kBAAkB;MACrD,MAAMC,MAAM,GAAG5J,uBAAuB,CAACyJ,WAAW,CAACG,MAAM,CAAC,IAAI,EAAE;;MAEhE;MACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC;MAC3BD,MAAM,CAAC9D,OAAO,CAAEgE,eAAe,IAAK;QAChCD,gBAAgB,CAACC,eAAe,CAACC,KAAK,CAAC,GAAGD,eAAe,CAAC5D,GAAG;MACjE,CAAC,CAAC;;MAEF;MACA4H,aAAa,CAACtE,cAAc,CAAC,CAAC1D,OAAO,CAAC,CAACoI,cAAc,EAAE9D,KAAK,KAAK;QAC7D,IAAI,CAAC8D,cAAc,IAAI,CAACA,cAAc,CAAC3D,cAAc,EAAE;UACnD;QACJ;QAEA,IAAIR,KAAK,GAAG,GAAGN,WAAW,CAACM,KAAK,IAAI,MAAM,IAAIK,KAAK,GAAG,CAAC,EAAE;QACzD,IAAI+D,SAAS,GAAGtN,uBAAuB,CAAC+I,MAAM,EAAEsE,cAAc,CAAC;;QAE/D;QACA,IAAIxE,gBAAgB,IAAIA,gBAAgB,CAAC5C,MAAM,GAAG,CAAC,EAAE;UACjD,IAAI;YACAiD,KAAK,GAAG/I,oBAAoB,CACxB0I,gBAAgB,EAChBG,gBAAgB,EAChBsE,SACJ,CAAC;UACL,CAAC,CAAC,OAAOJ,CAAC,EAAE;YACRC,OAAO,CAACC,GAAG,CACP,wDAAwD,EACxDF,CACJ,CAAC;UACL;QACJ;;QAEA;QACAF,OAAO,CAACrD,IAAI,CAAC;UACTT,KAAK,EAAEA,KAAK;UACZpI,KAAK,EAAE,GAAG6H,cAAc,IAAI0E,cAAc,CAAC3D,cAAc;QAC7D,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,OAAOsD,OAAO;EAClB,CAAC;EAED,MAAMO,IAAI,GAAGA,CAAA,KAAM;IACf,IAAIC,yBAAyB,GACzB,CAAAvM,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwM,6BAA6B,KAAI,KAAK;IAC1D,IAAIC,yBAAyB,GACzB,CAAAzM,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0M,qCAAqC,KAAI,KAAK;IAClE,IAAIC,uBAAuB,GACvB3M,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4M,2BAA2B;IAC/C,IAAIC,uBAAuB,GACvB,CAAA7M,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8M,yCAAyC,KAAI,KAAK;IACtE,IAAIC,2BAA2B,GAAGlM,IAAI,CAAC4E,aAAa,CAChD,qBACJ,CAAC;IAED,OAAO;MACHuH,cAAc,EAAE,IAAI;MACpBlF,MAAM,EAAE,CACJ;QACI1D,GAAG,EAAE,aAAa;QAClB6D,KAAK,EAAE,iBAAiB;QACxBW,MAAM,EAAE;MACZ,CAAC,EACD;QACIqE,MAAM,EAAEA,CAAA,kBAAMnP,KAAA,CAAAoP,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAK;MACvB,CAAC,EACD,IAAIjB,yBAAyB,GACvB,CACI;QACInI,GAAG,EAAE,aAAa;QAClB6D,KAAK,EAAE,eAAe;QACtBW,MAAM,EAAE,aAAa;QACrBmD,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;QACtB7L,QAAQ,EAAEA,CAAA,KAAM;UACZkB,WAAW,CAAC,CAAC;QACjB,CAAC;QACDqK,QAAQ,EAAE;MACd,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIL,mBAAmB,CAAC,CAAC,GACnB,CACI;QACI6B,MAAM,EAAEA,CAAA,kBAAMnP,KAAA,CAAAoP,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAK;MACvB,CAAC,EACD,GAAG/J,wBAAwB,CAAC,CAAC,EAC7B;QACIwJ,MAAM,EAAEA,CAAA;UAAA,IAAAQ,iBAAA,EAAAC,oBAAA;UAAA,oBACJ5P,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAACzP,GAAG;YAAA0P,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,IAAAC,iBAAA,GACCG,gBAAgB,CAAC,CAAC,cAAAH,iBAAA,uBAAlBA,iBAAA,CAAoB/G,GAAG,CACpB,CAAC4E,iBAAiB,EAAEhD,KAAK,KAAK;YAAA,IAAAuF,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAC1B3C,+BAA+B,CAC3BC,iBACJ,CAAC;YACD,oBACIxN,KAAA,CAAAoP,aAAA,CAAC9P,GAAG;cACA6Q,EAAE,EAAE,EAAG;cACPC,EAAE,EAAE,EAAG;cACP1E,SAAS,EAAC,SAAS;cACnBpF,GAAG,EACCkH,iBAAiB,CAAClH,GACrB;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEAlC,iBAAiB,CAAC3C,KAAK,IACpB,EAAE,iBACF7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,SAAS;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClBlC,iBAAiB,CAACG,QAAQ,iBACvB3N,KAAA,CAAAoP,aAAA;cACIiB,KAAK,EAAE;gBACHC,KAAK,EAAE;cACX,CAAE;cAAAjB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEGlC,iBAAiB,CAAC3C,KAAK,eAE3B7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,YAAY;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAK,CAC/B,CACP,eACD1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI,CAAC+Q,IAAI;cACNC,IAAI,EACA,cACH;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAGG1P,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAAC/P,MAAM;cACHoR,IAAI,EAAC,MAAM;cACXC,OAAO,EAAEA,CAAA,KAAM;gBACXjM,yBAAyB,CACrB+I,iBAAiB,CAAClH,GACtB,CAAC;gBACD+F,4BAA4B,CACxB3H,mBACJ,CAAC;cACL,CAAE;cAAA2K,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACL,4BAKO,CAAC,eAET1P,KAAA,CAAAoP,aAAA,CAAC1P,KAAK;cACFmL,KAAK,EAAC,aAAa;cACnB8F,QAAQ;cACRC,OAAO,EACHpD,iBAAiB,CAAClH,GAAG,IACrB9B,sBACH;cACDqM,IAAI,EAAEA,CAAA,KAAM;gBAAA,IAAAC,qBAAA;gBACRnF,4BAA4B,CACxB,EAAAmF,qBAAA,GAAAC,eAAe,CACVC,WAAW,cAAAF,qBAAA,uBADhBA,qBAAA,CAEItD,iBAAiB,CACZlH,GAAG,CACX,KACG,EAAE,EACNkH,iBAAiB,CAAClH,GACtB,CAAC;cACL,CAAE;cACF2K,QAAQ,EAAEA,CAAA,KACNxM,yBAAyB,CACrB,EACJ,CACH;cACDyM,KAAK,EACD,IACH;cAAA7B,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEA9K,0BAA0B,gBACvB5E,KAAA,CAAAoP,aAAA;cAAK1D,SAAS,EAAC,mCAAmC;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAC9C1P,KAAA,CAAAoP,aAAA,CAACjP,gBAAgB;cAAAkP,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAE,CAClB,CAAC,GACNhL,mBAAmB,IACrBvB,SAAS,gBACPnD,KAAA,CAAAoP,aAAA;cAAG1D,SAAS,EAAC,aAAa;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAElBtM,iCAEL,CAAC,gBAEJpD,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EAAEtP,uBAAuB,CAChCgD,mBAAmB,EACnB,EAAAqL,sBAAA,GAAAgB,eAAe,CACVC,WAAW,cAAAjB,sBAAA,uBADhBA,sBAAA,CAEIvC,iBAAiB,CACZlH,GAAG,CACX,KACG,EACR,CAAE;cACFyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cAAAoC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CAEF,CAAC,EAEJxK,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CACX,iBACGtG,KAAA,CAAAoP,aAAA;cACI9I,GAAG,EACCkE,KACH;cAAA6E,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAED1P,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EACP9L,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CAEf;cACDyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cACFkE,UAAU,EACN,KACH;cACDC,UAAU,EACN5D,iBAAiB,CAAClH,GACrB;cACD+K,iBAAiB,EACbN,eAAe,aAAfA,eAAe,wBAAAf,sBAAA,GAAfe,eAAe,CACTC,WAAW,cAAAhB,sBAAA,uBADjBA,sBAAA,CAEIxC,iBAAiB,CACZlH,GAAG,CAEf;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CACA;YAET;YAEN,CAAC,eAEP1P,KAAA,CAAAoP,aAAA,CAAC7O;YACG;YACA;YAAA;cACAoN,QAAQ,EACJH,iBAAiB,CAACG,QACrB;cACD2D,UAAU,EAAE,CAAE;cACdC,SAAS,EAAEhQ,UAAU,CAACiQ,YAAY,CAAC,CAAE;cACrCC,YAAY,EAAElQ,UAAU,CAACmQ,YAAY,CAAC,CAAE;cACxCC,oBAAoB,EAAEnQ,mCAAmC,CAAC,CAAE;cAC5DoQ,UAAU,EACNb,eAAe,aAAfA,eAAe,wBAAAd,sBAAA,GAAfc,eAAe,CACTC,WAAW,cAAAf,sBAAA,uBADjBA,sBAAA,CAEIzC,iBAAiB,CACZlH,GAAG,CAEf;cACDuL,cAAc,EAAEA,CACZ5E,KAAK,EACL6E,cAAc,KACb;gBACDD,cAAc,CACVrE,iBAAiB,CAAClH,GAAG,EACrB2G,KAAK,EACL6E,cACJ,CAAC;cACL,CAAE;cACFC,oBAAoB,EAChBC,OAAO,IACN;gBACDC,yBAAyB,CACrBzE,iBAAiB,CAAClH,GAAG,EACrB0L,OACJ,CAAC;cACL,CAAE;cACFpG,YAAY,EACR,EAAAsE,sBAAA,GAAAa,eAAe,CACVC,WAAW,cAAAd,sBAAA,uBADhBA,sBAAA,CAEI1C,iBAAiB,CACZlH,GAAG,CACX,KAAI,EACR;cACD4L,mBAAmB,EAAC,MAAM;cAC1BC,sBAAsB,EAAC,MAAM;cAC7BC,UAAU,EAAE;gBACR5B,IAAI,EAAE;cACV,CAAE;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CACM,CACV,CAAC;UAEd,CACJ,CAAC,GAAAE,oBAAA,GACAyC,mBAAmB,CAAC,CAAC,cAAAzC,oBAAA,uBAArBA,oBAAA,CAAuBhH,GAAG,CACvB,CAAC0J,gBAAgB,EAAE9H,KAAK;YAAA,IAAA+H,qBAAA;YAAA,oBACpBvS,KAAA,CAAAoP,aAAA,CAAC9P,GAAG;cACA6Q,EAAE,EAAE,EAAG;cACPC,EAAE,EAAE,EAAG;cACP1E,SAAS,EAAC,SAAS;cACnBpF,GAAG,EAAEgM,gBAAgB,CAAChM,GAAI;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEzB4C,gBAAgB,CAACzH,KAAK,IACnB,EAAE,iBACF7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,SAAS;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClB4C,gBAAgB,CAAC3E,QAAQ,iBACtB3N,KAAA,CAAAoP,aAAA;cACIiB,KAAK,EAAE;gBACHC,KAAK,EAAE;cACX,CAAE;cAAAjB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEG4C,gBAAgB,CAACzH,KAAK,eAE1B7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,YAAY;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAK,CAC/B,CACP,eACD1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI,CAAC+Q,IAAI;cACNC,IAAI,EACA,uBACH;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAED1P,KAAA,CAAAoP,aAAA,CAACzN;YACG;YACA;YAAA;cACAgM,QAAQ,EACJ2E,gBAAgB,CAAC3E,QACpB;cACD2D,UAAU,EAAE,CAAE;cACdC,SAAS,EAAEhQ,UAAU,CAACiQ,YAAY,CAAC,CAAE;cACrCC,YAAY,EAAElQ,UAAU,CAACmQ,YAAY,CAAC,CAAE;cACxCG,cAAc,EACV5E,KAAK,IACJ;gBACDuF,iBAAiB,CACbF,gBAAgB,CAAChM,GAAG,EACpB2G,KACJ,CAAC;cACL,CAAE;cACF8E,oBAAoB,EAChBC,OAAO,IACN;gBACDS,4BAA4B,CACxBH,gBAAgB,CAAChM,GAAG,EACpB0L,OACJ,CAAC;cACL,CAAE;cACFpG,YAAY,EACR,EAAA2G,qBAAA,GAAAxB,eAAe,CACV2B,SAAS,cAAAH,qBAAA,uBADdA,qBAAA,CAEID,gBAAgB,CACXhM,GAAG,CACX,KAAI,EACR;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CACM,CACV,CAAC;UAAA,CAEd,CACC,CAAC,eACN1P,KAAA,CAAAoP,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAK,CACP,CAAC;QAAA;MAEX,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIf,yBAAyB,IAC7BE,uBAAuB,IACvBtJ,kBAAkB,GACZ,CACI;QACIe,GAAG,EAAE,qBAAqB;QAC1B6D,KAAK,EAAE,oBAAoB;QAC3BW,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE;UACT4H,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAEzR,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UACnC;UACA0R,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE,UAAU;UAC5BC,WAAW,EACP,qCAAqC;UACzC1C,KAAK,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAC;UACxB8B,UAAU,EAAE;QAChB,CAAC;QACD/E,OAAO,EAAEL,kBAAkB,CAAC,CAAC;QAC7BxL,QAAQ,EAAGL,KAAK,IAAK;UACjBuB,WAAW,CAAC,CAAC;QACjB;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIuL,uBAAuB,IAC3BF,yBAAyB,IACzBM,2BAA2B,IAC3BA,2BAA2B,CAAC/H,MAAM,GAAG,CAAC,GAChC,CACI;QACIiI,MAAM,EAAEA,CAAA,kBACJnP,KAAA,CAAAoP,aAAA;UAAK1D,SAAS,EAAC,uEAAuE;UAAA2D,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAClF1P,KAAA,CAAAoP,aAAA,CAACvP,WAAW;UACR2O,IAAI,EAAE;YACFxE,MAAM,EAAEkB,mCAAmC,CACvCnI,IAAI,CAAC4E,aAAa,CACd,qBACJ,CACJ;UACJ,CAAE;UACF5E,IAAI,EAAEA,IAAK;UAAAsM,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACd,CACA;MAEb,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIf,yBAAyB,KAC5B,CAACE,uBAAuB,IAAI,CAACtJ,kBAAkB,CAAC,GAC3CU,2BAA2B,CAAC,CAAC,GAC7B,EAAE,CAAC,EAET,IAAI8I,uBAAuB,GACrB,CACI;QACII,MAAM,EAAEA,CAAA;UAAA,IAAA8D,qBAAA;UAAA,oBACJjT,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAACzP,GAAG;YAAA0P,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,IAAAuD,qBAAA,GACChN,2BAA2B,CAAC,CAAC,cAAAgN,qBAAA,uBAA7BA,qBAAA,CAA+BrK,GAAG,CAC/B,CAAC4E,iBAAiB,EAAEhD,KAAK,KAAK;YAAA,IAAA0I,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAC1B9F,+BAA+B,CAC3BC,iBACJ,CAAC;YACD,oBACIxN,KAAA,CAAAoP,aAAA,CAAC9P,GAAG;cACA6Q,EAAE,EAAE,EAAG;cACPC,EAAE,EAAE,EAAG;cACP1E,SAAS,EAAC,SAAS;cACnBpF,GAAG,EACCkH,iBAAiB,CAAClH,GACrB;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEAlC,iBAAiB,CAAC3C,KAAK,IACpB,EAAE,iBACF7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,SAAS;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClBlC,iBAAiB,CAACG,QAAQ,iBACvB3N,KAAA,CAAAoP,aAAA;cACIiB,KAAK,EAAE;gBACHC,KAAK,EAAE;cACX,CAAE;cAAAjB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEGlC,iBAAiB,CAAC3C,KAAK,eAE3B7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,YAAY;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAK,CAC/B,CACP,eACD1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI,CAAC+Q,IAAI;cACNC,IAAI,EACA,cACH;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAGG1P,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAAC/P,MAAM;cACHoR,IAAI,EAAC,MAAM;cACXC,OAAO,EAAEA,CAAA,KAAM;gBACXjM,yBAAyB,CACrB+I,iBAAiB,CAAClH,GACtB,CAAC;gBACD+F,4BAA4B,CACxB3H,mBACJ,CAAC;cACL,CAAE;cAAA2K,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACL,4BAKO,CAAC,eAET1P,KAAA,CAAAoP,aAAA,CAAC1P,KAAK;cACFmL,KAAK,EAAC,aAAa;cACnB8F,QAAQ;cACRC,OAAO,EACHpD,iBAAiB,CAAClH,GAAG,IACrB9B,sBACH;cACDqM,IAAI,EAAEA,CAAA,KAAM;gBAAA,IAAAyC,sBAAA;gBACR3H,4BAA4B,CACxB,EAAA2H,sBAAA,GAAAvC,eAAe,CACVC,WAAW,cAAAsC,sBAAA,uBADhBA,sBAAA,CAEI9F,iBAAiB,CACZlH,GAAG,CACX,KACG,EAAE,EACNkH,iBAAiB,CAAClH,GACtB,CAAC;cACL,CAAE;cACF2K,QAAQ,EAAEA,CAAA,KACNxM,yBAAyB,CACrB,EACJ,CACH;cACDyM,KAAK,EACD,IACH;cAAA7B,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEA9K,0BAA0B,gBACvB5E,KAAA,CAAAoP,aAAA;cAAK1D,SAAS,EAAC,mCAAmC;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAC9C1P,KAAA,CAAAoP,aAAA,CAACjP,gBAAgB;cAAAkP,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAE,CAClB,CAAC,GACNhL,mBAAmB,IACrBvB,SAAS,gBACPnD,KAAA,CAAAoP,aAAA;cAAG1D,SAAS,EAAC,aAAa;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAElBtM,iCAEL,CAAC,gBAEJpD,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EAAEtP,uBAAuB,CAChCgD,mBAAmB,EACnB,EAAAwO,sBAAA,GAAAnC,eAAe,CACVC,WAAW,cAAAkC,sBAAA,uBADhBA,sBAAA,CAEI1F,iBAAiB,CACZlH,GAAG,CACX,KACG,EACR,CAAE;cACFyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cAAAoC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CAIF,CAAC,EAEJxK,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CACX,iBACGtG,KAAA,CAAAoP,aAAA;cACI9I,GAAG,EACCkE,KACH;cAAA6E,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAED1P,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EACP9L,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CAEf;cACDyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cACFkE,UAAU,EACN,KACH;cACDC,UAAU,EACN5D,iBAAiB,CAAClH,GACrB;cACD+K,iBAAiB,EACbN,eAAe,aAAfA,eAAe,wBAAAoC,sBAAA,GAAfpC,eAAe,CACTC,WAAW,cAAAmC,sBAAA,uBADjBA,sBAAA,CAEI3F,iBAAiB,CACZlH,GAAG,CAEf;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CACA;YAET;YAEN,CAAC,eAEP1P,KAAA,CAAAoP,aAAA,CAAC7O;YACG;YACA;YAAA;cACAoN,QAAQ,EACJH,iBAAiB,CAACG,QACrB;cACD2D,UAAU,EAAE,CAAE;cACdC,SAAS,EAAEhQ,UAAU,CAACiQ,YAAY,CAAC,CAAE;cACrCC,YAAY,EAAElQ,UAAU,CAACmQ,YAAY,CAAC,CAAE;cACxCC,oBAAoB,EAAEnQ,mCAAmC,CAAC,CAAE;cAC5DoQ,UAAU,EACNb,eAAe,aAAfA,eAAe,wBAAAqC,sBAAA,GAAfrC,eAAe,CACTC,WAAW,cAAAoC,sBAAA,uBADjBA,sBAAA,CAEI5F,iBAAiB,CACZlH,GAAG,CAEf;cACDuL,cAAc,EAAEA,CACZ5E,KAAK,EACL6E,cAAc,KACb;gBACDD,cAAc,CACVrE,iBAAiB,CAAClH,GAAG,EACrB2G,KAAK,EACL6E,cACJ,CAAC;cACL,CAAE;cACFC,oBAAoB,EAChBC,OAAO,IACN;gBACDC,yBAAyB,CACrBzE,iBAAiB,CAAClH,GAAG,EACrB0L,OACJ,CAAC;cACL,CAAE;cACFpG,YAAY,EACR,EAAAyH,sBAAA,GAAAtC,eAAe,CACVC,WAAW,cAAAqC,sBAAA,uBADhBA,sBAAA,CAEI7F,iBAAiB,CACZlH,GAAG,CACX,KAAI,EACR;cACD4L,mBAAmB,EAAC,MAAM;cAC1BC,sBAAsB,EAAC,MAAM;cAC7BC,UAAU,EAAE;gBACR5B,IAAI,EAAE;cACV,CAAE;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CACM,CACV,CAAC;UAEd,CACJ,CACC,CAAC,eACN1P,KAAA,CAAAoP,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAK,CACP,CAAC;QAAA;MAEX,CAAC,CACJ,GACD,EAAE,CAAC,EACT,GAAG7J,mBAAmB,CAAC,CAAC,EACxB;QACIsJ,MAAM,EAAEA,CAAA;UAAA,IAAAoE,gBAAA,EAAAC,eAAA;UAAA,oBACJxT,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAACzP,GAAG;YAAA0P,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,IAAA6D,gBAAA,GACCE,eAAe,CAAC,CAAC,cAAAF,gBAAA,uBAAjBA,gBAAA,CAAmB3K,GAAG,CACnB,CAAC4E,iBAAiB,EAAEhD,KAAK,KAAK;YAAA,IAAAkJ,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;YAC1BtG,+BAA+B,CAC3BC,iBACJ,CAAC;YACD,oBACIxN,KAAA,CAAAoP,aAAA,CAAC9P,GAAG;cACA6Q,EAAE,EAAE,EAAG;cACPC,EAAE,EAAE,EAAG;cACP1E,SAAS,EAAC,SAAS;cACnBpF,GAAG,EAAEkH,iBAAiB,CAAClH,GAAI;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAE1BlC,iBAAiB,CAAC3C,KAAK,IACpB,EAAE,iBACF7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,SAAS;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClBlC,iBAAiB,CAACG,QAAQ,iBACvB3N,KAAA,CAAAoP,aAAA;cACIiB,KAAK,EAAE;gBACHC,KAAK,EAAE;cACX,CAAE;cAAAjB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEGlC,iBAAiB,CAAC3C,KAAK,eAE3B7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,YAAY;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAK,CAC/B,CACP,eACD1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI,CAAC+Q,IAAI;cACNC,IAAI,EAAE,cAAe;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAGjB1P,KAAA,CAAAoP,aAAA,CAAApP,KAAA,CAAA6P,QAAA,qBACI7P,KAAA,CAAAoP,aAAA,CAAC/P,MAAM;cACHoR,IAAI,EAAC,MAAM;cACXC,OAAO,EAAEA,CAAA,KAAM;gBACXjM,yBAAyB,CACrB+I,iBAAiB,CAAClH,GACtB,CAAC;gBACD+F,4BAA4B,CACxB3H,mBACJ,CAAC;cACL,CAAE;cAAA2K,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GACL,4BAGO,CAAC,eAET1P,KAAA,CAAAoP,aAAA,CAAC1P,KAAK;cACFmL,KAAK,EAAC,aAAa;cACnB8F,QAAQ;cACRC,OAAO,EACHpD,iBAAiB,CAAClH,GAAG,IACrB9B,sBACH;cACDqM,IAAI,EAAEA,CAAA,KAAM;gBAAA,IAAAiD,sBAAA;gBACRnI,4BAA4B,CACxB,EAAAmI,sBAAA,GAAA/C,eAAe,CACVC,WAAW,cAAA8C,sBAAA,uBADhBA,sBAAA,CAEItG,iBAAiB,CACZlH,GAAG,CACX,KAAI,EAAE,EACPkH,iBAAiB,CAAClH,GACtB,CAAC;cACL,CAAE;cACF2K,QAAQ,EAAEA,CAAA,KACNxM,yBAAyB,CACrB,EACJ,CACH;cACDyM,KAAK,EAAE,IAAK;cAAA7B,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEX9K,0BAA0B,gBACvB5E,KAAA,CAAAoP,aAAA;cAAK1D,SAAS,EAAC,mCAAmC;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAC9C1P,KAAA,CAAAoP,aAAA,CAACjP,gBAAgB;cAAAkP,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAE,CAClB,CAAC,GACNhL,mBAAmB,IACrBvB,SAAS,gBACPnD,KAAA,CAAAoP,aAAA;cAAG1D,SAAS,EAAC,aAAa;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAElBtM,iCAEL,CAAC,gBAEJpD,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EAAEtP,uBAAuB,CAChCgD,mBAAmB,EACnB,EAAAgP,uBAAA,GAAA3C,eAAe,CACVC,WAAW,cAAA0C,uBAAA,uBADhBA,uBAAA,CAEIlG,iBAAiB,CACZlH,GAAG,CACX,KACG,EACR,CAAE;cACFyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cAAAoC,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CAEF,CAAC,EAEJxK,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CACX,iBACGtG,KAAA,CAAAoP,aAAA;cACI9I,GAAG,EACCkE,KACH;cAAA6E,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAED1P,KAAA,CAAAoP,aAAA,CAAC3N,kBAAkB;cACfuP,WAAW,EACP9L,aAAa,CACTsI,iBAAiB,CACZlH,GAAG,CAEf;cACDyG,0BAA0B,EACtBE,KAAK,IACJ;gBACDF,0BAA0B,CACtBS,iBAAiB,CAAClH,GAAG,EACrB2G,KACJ,CAAC;cACL,CAAE;cACFkE,UAAU,EACN,KACH;cACDC,UAAU,EACN5D,iBAAiB,CAAClH,GACrB;cACD+K,iBAAiB,EACbN,eAAe,aAAfA,eAAe,wBAAA4C,uBAAA,GAAf5C,eAAe,CACTC,WAAW,cAAA2C,uBAAA,uBADjBA,uBAAA,CAEInG,iBAAiB,CACZlH,GAAG,CAEf;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CACA;YAET;YAEN,CAAC,eAGP1P,KAAA,CAAAoP,aAAA,CAAC7O;YACG;YACA;YAAA;cACAoN,QAAQ,EACJH,iBAAiB,CAACG,QACrB;cACD2D,UAAU,EAAE,CAAE;cACdC,SAAS,EAAEhQ,UAAU,CAACiQ,YAAY,CAAC,CAAE;cACrCC,YAAY,EAAElQ,UAAU,CAACmQ,YAAY,CAAC,CAAE;cACxCC,oBAAoB,EAAEnQ,mCAAmC,CAAC,CAAE;cAC5DoQ,UAAU,EACNb,eAAe,aAAfA,eAAe,wBAAA6C,uBAAA,GAAf7C,eAAe,CACTC,WAAW,cAAA4C,uBAAA,uBADjBA,uBAAA,CAEIpG,iBAAiB,CACZlH,GAAG,CAEf;cACDuL,cAAc,EAAEA,CACZ5E,KAAK,EACL6E,cAAc,KACb;gBACDD,cAAc,CACVrE,iBAAiB,CAAClH,GAAG,EACrB2G,KAAK,EACL6E,cACJ,CAAC;cACL,CAAE;cACFC,oBAAoB,EAChBC,OAAO,IACN;gBACDC,yBAAyB,CACrBzE,iBAAiB,CAAClH,GAAG,EACrB0L,OACJ,CAAC;cACL,CAAE;cACFpG,YAAY,EACR,EAAAiI,uBAAA,GAAA9C,eAAe,CACVC,WAAW,cAAA6C,uBAAA,uBADhBA,uBAAA,CAEIrG,iBAAiB,CACZlH,GAAG,CACX,KAAI,EACR;cACD4L,mBAAmB,EAAC,MAAM;cAC1BC,sBAAsB,EAAC,MAAM;cAC7BC,UAAU,EAAE;gBACR5B,IAAI,EAAE;cACV,CAAE;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACL,CACM,CACV,CAAC;UAEd,CACJ,CAAC,GAAA8D,eAAA,GACAO,cAAc,CAAC,CAAC,cAAAP,eAAA,uBAAhBA,eAAA,CAAkB5K,GAAG,CAClB,CAAC0J,gBAAgB,EAAE9H,KAAK,KAAK;YAAA,IAAAwJ,sBAAA;YACzB,oBACIhU,KAAA,CAAAoP,aAAA,CAAC9P,GAAG;cACA6Q,EAAE,EAAE,EAAG;cACPC,EAAE,EAAE,EAAG;cACP1E,SAAS,EAAC,SAAS;cACnBpF,GAAG,EAAEgM,gBAAgB,CAAChM,GAAI;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAEzB4C,gBAAgB,CAACzH,KAAK,IACnB,EAAE,iBACF7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,SAAS;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAClB4C,gBAAgB,CAAC3E,QAAQ,iBACtB3N,KAAA,CAAAoP,aAAA;cACIiB,KAAK,EAAE;gBACHC,KAAK,EAAE;cACX,CAAE;cAAAjB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EACA4C,gBAAgB,CAACzH,KAAK,eACvB7K,KAAA,CAAAoP,aAAA;cAAI1D,SAAS,EAAC,YAAY;cAAA2D,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CAAK,CAC/B,CACP,eACD1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI,CAAC+Q,IAAI;cACNC,IAAI,EACA,uBACH;cAAAnB,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,gBAED1P,KAAA,CAAAoP,aAAA,CAACzN;YACG;YACA;YAAA;cACAgM,QAAQ,EACJ2E,gBAAgB,CAAC3E,QACpB;cACD2D,UAAU,EAAE,CAAE;cACdC,SAAS,EAAEhQ,UAAU,CAACiQ,YAAY,CAAC,CAAE;cACrCC,YAAY,EAAElQ,UAAU,CAACmQ,YAAY,CAAC,CAAE;cACxCG,cAAc,EACV5E,KAAK,IACJ;gBACDuF,iBAAiB,CACbF,gBAAgB,CAAChM,GAAG,EACpB2G,KACJ,CAAC;cACL,CAAE;cACF8E,oBAAoB,EAChBC,OAAO,IACN;gBACDS,4BAA4B,CACxBH,gBAAgB,CAAChM,GAAG,EACpB0L,OACJ,CAAC;cACL,CAAE;cACFpG,YAAY,EACR,EAAAoI,sBAAA,GAAAjD,eAAe,CACV2B,SAAS,cAAAsB,sBAAA,uBADdA,sBAAA,CAEI1B,gBAAgB,CACXhM,GAAG,CACX,KAAI,EACR;cAAA+I,MAAA;cAAAC,QAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAA,CACJ,CACM,CACV,CAAC;UAEd,CACJ,CACC,CAAC,eACN1P,KAAA,CAAAoP,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAK,CACP,CAAC;QAAA;MAEX,CAAC,EACD;QACIpJ,GAAG,EAAE,cAAc;QACnB6D,KAAK,EAAE,cAAc;QACrBW,MAAM,EAAEtK,YAAY;QACpBuK,WAAW,EAAE;UACTC,OAAO,EAAE;YAAElJ,SAAS,EAAEA;UAAU,CAAC;UACjCM,QAAQ,EAAGL,KAAK,IAAK;YACjB,IACIG,cAAc,CAAC+I,0BAA0B,IACzC,aAAa,EACf;cACElD,6BAA6B,CACzBhG,KAAK,EACLS,GAAG,EACH,cACJ,CAAC;YACL;UACJ,CAAC;UACDyR,QAAQ,EAAEtF;QACd;MACJ,CAAC;IAET,CAAC;EACL,CAAC;EAED,MAAMvC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BrH,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMmP,gCAAgC,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAC3C,IAAIC,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIlL,oBAAoB,GAAG/G,cAAc,GACnCH,OAAO,aAAPA,OAAO,wBAAAgS,mBAAA,GAAPhS,OAAO,CAAEQ,SAAS,cAAAwR,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB7K,aAAa,cAAA8K,qBAAA,uBAAjCA,qBAAA,CAAmCzR,SAAS,GAC5CR,OAAO,aAAPA,OAAO,wBAAAkS,mBAAA,GAAPlS,OAAO,CAAEQ,SAAS,cAAA0R,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB9K,UAAU,cAAA+K,qBAAA,uBAA9BA,qBAAA,CAAgC3R,SAAS;IAE/C,IAAI,CAAC0G,oBAAoB,EAAE,OAAOkL,WAAW;IAE7C,IAAI/K,cAAc,GAAGC,IAAI,CAACC,KAAK,CAC3BxH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyH,0BACpB,CAAC;IAED9C,MAAM,CAACC,IAAI,CAAC0C,cAAc,CAAC,CAACtD,OAAO,CAAE0D,cAAc,IAAK;MACpD,IAAI4K,YAAY,GAAGnL,oBAAoB,CAACO,cAAc,CAAC,IAAI,EAAE;MAC7D4K,YAAY,CAACtO,OAAO,CAAEuO,IAAI,IAAK;QAAA,IAAAC,mBAAA;QAC3B,MAAMpO,GAAG,GAAG,YAAYmO,IAAI,CAAC9J,cAAc,EAAE;QAC7C,MAAM5I,KAAK,IAAA2S,mBAAA,GAAG3R,IAAI,CAAC4E,aAAa,CAACrB,GAAG,CAAC,cAAAoO,mBAAA,cAAAA,mBAAA,GAAI,IAAI,CAAC,CAAC;QAC/CH,WAAW,CAACjO,GAAG,CAAC,GAAGvE,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,OAAOwS,WAAW;EACtB,CAAC;EAGD,MAAMI,QAAQ,GAAI5U,MAAM,IAAK;IAAA,IAAA6U,qBAAA,EAAAC,sBAAA;IACzBpR,mBAAmB,CAAC,IAAI,CAAC;IACzBP,QAAQ,CAACC,SAAS,CAAC;IACnB,IAAI2R,wBAAwB,GAAGlS,sBAAsB,CAACJ,GAAG,CAAC;IAC1D,IAAIsS,wBAAwB,IAAI3R,SAAS,EAAE;MACvC2R,wBAAwB,GAAG,CAAC,CAAC;IACjC;IACAA,wBAAwB,CAACC,mBAAmB,GACxC/T,8BAA8B,CAAC,CAAC;IACpC,IAAIgU,6BAA6B,GAAG;MAChC,KAAAJ,qBAAA,GAAGE,wBAAwB,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0B5D,WAAW;MACxC,GAAGpN;IACP,CAAC;IACD,IAAIqR,4CAA4C,GAAG;MAC/C,KAAAJ,sBAAA,GAAGC,wBAAwB,cAAAD,sBAAA,uBAAxBA,sBAAA,CAA0BnC,SAAS;MACtC,GAAG1O;IACP,CAAC;IACDjE,MAAM,CAAC,aAAa,CAAC,GAAGiV,6BAA6B;IACrDjV,MAAM,CAAC,WAAW,CAAC,GAAGkV,4CAA4C;;IAElE;IACA,IACIlS,IAAI,CAAC4E,aAAa,CAAC,qBAAqB,CAAC,IACzC,CAAAzF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0M,qCAAqC,KAAI,KAAK,EAChE;MACE,MAAMsG,sBAAsB,GAAGhB,gCAAgC,CAAC,CAAC;MAEjErN,MAAM,CAACsO,OAAO,CAACD,sBAAsB,CAAC,CAAChP,OAAO,CAAC,CAAC,CAACI,GAAG,EAAEvE,KAAK,CAAC,KAAK;QAC7D,IAAI,EAAEuE,GAAG,IAAIvG,MAAM,CAAC,EAAE;UAClBA,MAAM,CAACuG,GAAG,CAAC,GAAGvE,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,CAAC,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN;IAEA,IAAIqT,uBAAuB,GAAG;MAC1B,GAAGN,wBAAwB;MAC3B,GAAG/U,MAAM,CAAE;IACf,CAAC;IACD6C,sBAAsB,CAACJ,GAAG,CAAC,GAAG4S,uBAAuB;IAErD,IAAIC,6BAA6B,GAAG5U,aAAa,CAAC6U,iBAAiB,CAAC,CAAC,GAC/D,yBAAyB,GACzB,sBAAsB;IAC5B,IAAIhJ,MAAM,GAAG;MACT,CAAC+I,6BAA6B,GAAG;QAC7B,GAAGzS;MACP,CAAC;MACD2S,UAAU,EAAE/S;IAChB,CAAC;IACD;IACA,MAAM+J,UAAU,GAAIC,IAAI,IAAK;MACzB/I,mBAAmB,CAAC,KAAK,CAAC;MAC1BP,QAAQ,CAACC,SAAS,CAAC;MACnB,IAAIf,QAAQ,EAAE;QACVA,QAAQ,CAACI,GAAG,CAAC;MACjB;MACA;IACJ,CAAC;IACD,MAAMkK,OAAO,GAAIzJ,KAAK,IAAK;MACvBQ,mBAAmB,CAAC,KAAK,CAAC;MAC1BP,QAAQ,CAAC3B,UAAU,CAACoL,oBAAoB,CAAC1J,KAAK,CAAC,CAAC;IACpD,CAAC;IACD,IAAI2J,GAAG,GAAGvK,oBAAoB;IAC9Bd,UAAU,CAACiU,cAAc,CAAC5I,GAAG,EAAEN,MAAM,EAAEC,UAAU,EAAEG,OAAO,CAAC;EAC/D,CAAC;EAED,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAI2F,wBAAwB,GAAG,EAAE;IACjCA,wBAAwB,GAAGpV,yBAAyB,CAChD6B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0D,8BACpB,CAAC;IACD,OAAO6P,wBAAwB;EACnC,CAAC;EAED,MAAMpD,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIqD,uBAAuB,GAAG,EAAE;IAChCA,uBAAuB,GAAGpV,wBAAwB,CAC9C4B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0D,8BACpB,CAAC;IACD,OAAO8P,uBAAuB;EAClC,CAAC;EAED,MAAMjC,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIkC,mBAAmB,GAAG,EAAE;IAC5BA,mBAAmB,GAAGrT,cAAc,GAC9BjC,yBAAyB,CACrB6B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4D,2BACpB,CAAC,GACDzF,yBAAyB,CACrB6B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6D,wBACpB,CAAC;IACP,OAAO4P,mBAAmB;EAC9B,CAAC;EAED,MAAM5B,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI6B,kBAAkB,GAAG,EAAE;IAC3BA,kBAAkB,GAAGtT,cAAc,GAC7BhC,wBAAwB,CACpB4B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4D,2BACpB,CAAC,GACDxF,wBAAwB,CACpB4B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6D,wBACpB,CAAC;IACP,OAAO6P,kBAAkB;EAC7B,CAAC;EAED,MAAM/D,cAAc,GAAGA,CAACgE,OAAO,EAAE5I,KAAK,EAAE6E,cAAc,GAAG3O,SAAS,KAAK;IACnE2S,gBAAgB,CACZD,OAAO,EACP5I,KAAK,EACLrJ,cAAc,EACdC,iBAAiB,EACjBuI,iBAAiB,EACjB0F,cACJ,CAAC;EACL,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAACqD,OAAO,EAAE5I,KAAK,KAAK;IAAA,IAAA8I,qBAAA;IAC1C,IAAIC,iBAAiB,GAAGlW,CAAC,CAACmM,SAAS,CAACjI,sBAAsB,CAAC;IAC3D,IAAIiS,QAAQ,GAAG,EAAE;IAEjB,IACIjS,sBAAsB,CAAC6R,OAAO,CAAC,IAC/B,CAAA5I,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE/F,MAAM,OAAA6O,qBAAA,GAAI/R,sBAAsB,CAAC6R,OAAO,CAAC,cAAAE,qBAAA,uBAA/BA,qBAAA,CAAiC7O,MAAM,GAC1D;MACE+O,QAAQ,GAAGhJ,KAAK,CAACnB,MAAM,CAClBoK,UAAU,IACP,CAAClS,sBAAsB,CAAC6R,OAAO,CAAC,CAACpK,QAAQ,CAACyK,UAAU,CAC5D,CAAC;IACL,CAAC,MAAM;MACHD,QAAQ,GAAGhJ,KAAK;IACpB;IACA+I,iBAAiB,CAACH,OAAO,CAAC,GACtB7R,sBAAsB,CAAC6R,OAAO,CAAC,IAC/B5I,KAAK,CAAC/F,MAAM,IAAIlD,sBAAsB,CAAC6R,OAAO,CAAC,CAAC3O,MAAM,GAChD,CAAC,GAAGlD,sBAAsB,CAAC6R,OAAO,CAAC,EAAE,GAAGI,QAAQ,CAAC,GACjDhJ,KAAK;IACf;IACAhJ,yBAAyB,CAAC+R,iBAAiB,CAAC;IAC5C5J,iBAAiB,CAAC,CAAC;EACvB,CAAC;EAED,MAAM6F,yBAAyB,GAAGA,CAAC4D,OAAO,EAAE7D,OAAO,KAAK;IACpD,IAAImE,mBAAmB,GAAGrS,wBAAwB;IAClDqS,mBAAmB,CAACN,OAAO,CAAC,GAAG7D,OAAO;IACtCjO,2BAA2B,CAACoS,mBAAmB,CAAC;IAChD9R,wBAAwB,CAAC+R,wBAAwB,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAM3D,4BAA4B,GAAGA,CAACoD,OAAO,EAAE7D,OAAO,KAAK;IACvD,IAAImE,mBAAmB,GAAGjS,2BAA2B;IACrDiS,mBAAmB,CAACN,OAAO,CAAC,GAAG7D,OAAO;IACtC7N,8BAA8B,CAACgS,mBAAmB,CAAC;IACnD5R,iCAAiC,CAAC8R,iCAAiC,CAAC,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMD,wBAAwB,GAAGA,CAAA,KAAM;IACnC,IAAIE,QAAQ,GAAG,KAAK;IACpBzP,MAAM,CAACC,IAAI,CAAChD,wBAAwB,CAAC,CAAC8E,GAAG,CAAEiN,OAAO,IAAK;MACnD,IAAI,CAAC/R,wBAAwB,CAAC+R,OAAO,CAAC,EAAE;QACpCS,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CAAC;IACF,OAAO,CAACA,QAAQ;EACpB,CAAC;EAED,MAAMD,iCAAiC,GAAGA,CAAA,KAAM;IAC5C,IAAIC,QAAQ,GAAG,KAAK;IACpBzP,MAAM,CAACC,IAAI,CAAC5C,2BAA2B,CAAC,CAAC0E,GAAG,CAAEiN,OAAO,IAAK;MACtD,IAAI,CAAC3R,2BAA2B,CAAC2R,OAAO,CAAC,EAAE;QACvCS,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CAAC;IACF,OAAO,CAACA,QAAQ;EACpB,CAAC;EAED,IAAIvF,eAAe,GACfrQ,0BAA0B,CACtBkC,sBAAsB,CAACJ,GAAG,CAAC,EAC3BgM,IAAI,CAAC,CAAC,CAACxE,MACX,CAAC,IAAI,CAAC,CAAC;EACX;EACA;EACA,MAAMuM,MAAM,GAAGxT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,aAAa,CAAC,aAAa,CAAC;EACjD,IAAI,CAAAoJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyF,WAAW,KAAIrT,SAAS,IAAIoT,MAAM,IAAIpT,SAAS,EAAE;IAClEJ,IAAI,CAACwD,cAAc,CAAC;MAChBiQ,WAAW,EAAE;IACjB,CAAC,CAAC;EACN;EAEA,MAAMC,OAAO,GAAGA,CAACzM,MAAM,EAAE+G,eAAe,KAAK;IACzC,IAAI,CAACxL,kBAAkB,EAAE;MACrB,MAAMmR,QAAQ,GAAGxV,yBAAyB,CAAC8I,MAAM,EAAE+G,eAAe,CAAC;MACnE,IAAI2F,QAAQ,CAACxP,MAAM,GAAG,CAAC,EAAE;QACrB,OAAOwP,QAAQ;MACnB,CAAC,MAAM;QACH,OAAO,CAAC;UAAEvH,MAAM,EAAEA,CAAA,kBAAMnP,KAAA,CAAAoP,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,kBAAqB;QAAE,CAAC,CAAC;MAC1D;IACJ,CAAC,MAAM;MACH,OAAOlB,IAAI,CAAC,CAAC;IACjB;EACJ,CAAC;;EAED;EACA,MAAMmI,wBAAwB,GAC1B5F,eAAe,aAAfA,eAAe,wBAAAxO,qBAAA,GAAfwO,eAAe,CAAE6F,mBAAmB,cAAArU,qBAAA,uBAApCA,qBAAA,CAAsCuJ,MAAM,CAAEwC,cAAc,IAAK;IAC7D,OAAOV,kBAAkB,CAAC,CAAC,CAACiJ,IAAI,CAC3BC,MAAM,IAAKA,MAAM,CAAC/U,KAAK,KAAKuM,cACjC,CAAC;EACL,CAAC,CAAC;EAENyC,eAAe,CAAC6F,mBAAmB,GAAGD,wBAAwB;EAE9D,oBACI3W,KAAA,CAAAoP,aAAA;IAAK1D,SAAS,EAAC,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB1P,KAAA,CAAAoP,aAAA,CAAC5P,IAAI;IACDuX,aAAa,EAAEhG,eAAgB;IAC/BhO,IAAI,EAAEA,IAAK;IACXiU,MAAM,EAAC,UAAU;IACjBC,QAAQ,EAAEtC,QAAS;IACnBuC,cAAc,EAAE9K,iBAAkB;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElC1P,KAAA,CAAAoP,aAAA,CAACvP,WAAW;IACR2O,IAAI,EAAEiI,OAAO,CAACjI,IAAI,CAAC,CAAC,CAACxE,MAAM,EAAE+G,eAAe,CAAE;IAC9ChO,IAAI,EAAEA,IAAK;IACXoU,QAAQ,EAAE,CAAC5R,kBAAmB;IAAA8J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjC,CAAC,eAEF1P,KAAA,CAAAoP,aAAA;IAAK1D,SAAS,EAAC,SAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnB,CAAC,CAACtL,qBAAqB,IACpB,CAACE,8BAA8B,kBAAKtE,KAAA,CAAAoP,aAAA,CAACxP,IAAI;IAAAyP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAO,CAAC,EACpDnK,kBAAkB,iBACfvF,KAAA,CAAAoP,aAAA,CAAC/P,MAAM;IACHoR,IAAI,EAAC,SAAS;IACd2G,QAAQ,EAAC,QAAQ;IACjBnD,QAAQ,EACJzQ,gBAAgB,IAChB,CAACY,qBAAqB,IACtB,CAACU,aAAa,IACd,CAACR,8BACJ;IAAA+K,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACJ,MAEO,CACX,EACAlM,gBAAgB,gBACbxD,KAAA,CAAAoP,aAAA;IAAK1D,SAAS,EAAC,mCAAmC;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9C1P,KAAA,CAAAoP,aAAA,CAACjP,gBAAgB;IAAAkP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAClB,CAAC,GACN,IAAI,EACPzM,KAAK,gBAAGjD,KAAA,CAAAoP,aAAA;IAAG1D,SAAS,EAAC,aAAa;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEzM,KAAS,CAAC,GAAG,IACjD,CACH,CAEL,CAAC;AAEd,CAAC;AAED,MAAM6S,gBAAgB,GAAGA,CACrBD,OAAO,EACP5I,KAAK,EACLrJ,cAAc,EACdC,iBAAiB,EACjBuI,iBAAiB,EACjB0F,cAAc,KACb;EAAA,IAAAuF,qBAAA;EACD,IAAIrB,iBAAiB,GAAGlW,CAAC,CAACmM,SAAS,CAACrI,cAAc,CAAC;EACnD,IAAIqS,QAAQ,GAAG,EAAE;EAEjB,IACIrS,cAAc,CAACiS,OAAO,CAAC,IACvB,CAAA5I,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE/F,MAAM,OAAAmQ,qBAAA,GAAIzT,cAAc,CAACiS,OAAO,CAAC,cAAAwB,qBAAA,uBAAvBA,qBAAA,CAAyBnQ,MAAM,GAClD;IACE+O,QAAQ,GAAGhJ,KAAK,CAACnB,MAAM,CAClBoK,UAAU,IAAK,CAACtS,cAAc,CAACiS,OAAO,CAAC,CAACpK,QAAQ,CAACyK,UAAU,CAChE,CAAC;EACL,CAAC,MAAM;IACH,IAAItS,cAAc,CAACiS,OAAO,CAAC,EAAE;MACzBI,QAAQ,GAAG,CAAC,GAAG,IAAI/J,GAAG,CAACe,KAAK,CAACd,MAAM,CAACvI,cAAc,CAACiS,OAAO,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC,MAAM;MACHI,QAAQ,GAAGhJ,KAAK;IACpB;EACJ;;EAEA;EACA;EACA,MAAMqK,aAAa,GAAG1T,cAAc,CAACiS,OAAO,CAAC,IAAI,EAAE;;EAEnD;EACA;EACA;EACA,MAAM0B,aAAa,GAAG,CAAC,GAAG,IAAIrL,GAAG,CAAC,CAAC,GAAGe,KAAK,EAAE,GAAGqK,aAAa,CAAC,CAAC,CAAC;;EAEhE;EACA;EACA;EACA;EACAtB,iBAAiB,CAACH,OAAO,CAAC,GACtBjS,cAAc,CAACiS,OAAO,CAAC,IAAI5I,KAAK,CAAC/F,MAAM,IAAIoQ,aAAa,CAACpQ,MAAM,GACzDqQ,aAAa,GACbtK,KAAK;;EAEf;EACA,IAAI6E,cAAc,EAAE;IAChBkE,iBAAiB,CAACH,OAAO,CAAC,GAAG0B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEzL,MAAM,CAC7CoK,UAAU,IAAKA,UAAU,KAAKpE,cACnC,CAAC;EACL;EACAjO,iBAAiB,CAACmS,iBAAiB,CAAC;EACpC5J,iBAAiB,CAAC,CAAC;AACvB,CAAC;;AAED;AACA,SAAS0J,gBAAgB;AACzB,eAAe9T,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}