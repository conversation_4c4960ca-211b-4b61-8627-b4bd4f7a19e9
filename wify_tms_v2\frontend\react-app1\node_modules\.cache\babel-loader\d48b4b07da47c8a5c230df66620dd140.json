{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\wify-utils\\\\form-builder\\\\components\\\\FormBuilder\\\\FormInputs\\\\TimePicker.js\";\nimport React from 'react';\nimport moment from 'moment';\nimport { TimePicker as AntdTimePicker } from 'antd';\nimport HeaderLabel from './HeaderLabel';\nconst TimePicker = ({\n  item = {},\n  value,\n  onChange,\n  disabled = false,\n  placeholder = \"Select time\",\n  format = \"hh:mm A\",\n  use12Hours = true,\n  style = {\n    width: \"100%\"\n  },\n  ...rest\n}) => {\n  const parseValue = () => {\n    if (!value) return undefined;\n    if (moment.isMoment(value)) return value;\n    if (typeof value === 'string') {\n      // Parse with multiple formats to handle both API responses and user input\n      const parsed = moment(value, ['hh:mm A',\n      // 02:30 PM\n      'h:mm A',\n      // 2:30 PM\n      'hh:mm  A',\n      // 02:30  PM (double space)\n      'h:mm  A',\n      // 2:30  PM (double space)\n      'hh:mmA',\n      // 02:30PM (no space)\n      'h:mmA',\n      // 2:30PM (no space)\n      'HH:mm',\n      // 14:30 (24-hour)\n      'H:mm',\n      // 14:30 (24-hour)\n      moment.ISO_8601], true);\n      return parsed.isValid() ? parsed : undefined;\n    }\n    return undefined;\n  };\n  console.log({\n    value,\n    parsedValue: parseValue()\n  });\n\n  // Form builder preview mode - render with label\n  if (item && Object.keys(item).length > 0) {\n    const {\n      label,\n      required\n    } = item;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(HeaderLabel, {\n      label: label,\n      required: required,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"form-group\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(AntdTimePicker, {\n      format: format,\n      use12Hours: use12Hours,\n      placeholder: placeholder,\n      disabled: disabled,\n      allowClear: true,\n      style: style,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 21\n      }\n    })));\n  }\n\n  // Standard TimePicker for forms\n  return /*#__PURE__*/React.createElement(AntdTimePicker, {\n    format: format,\n    use12Hours: use12Hours,\n    placeholder: placeholder,\n    value: parseValue(),\n    onChange: (momentValue, timeString) => onChange(!momentValue ? null : timeString),\n    disabled: disabled,\n    allowClear: true,\n    style: style,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }\n  });\n};\nexport default TimePicker;", "map": {"version": 3, "names": ["React", "moment", "TimePicker", "AntdTimePicker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item", "value", "onChange", "disabled", "placeholder", "format", "use12Hours", "style", "width", "rest", "parseValue", "undefined", "isMoment", "parsed", "ISO_8601", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "parsedValue", "Object", "keys", "length", "label", "required", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "allowClear", "momentValue", "timeString"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/form-builder/components/FormBuilder/FormInputs/TimePicker.js"], "sourcesContent": ["import React from 'react';\r\nimport moment from 'moment';\r\nimport { TimePicker as AntdTimePicker } from 'antd';\r\nimport HeaderLabel from './HeaderLabel';\r\n\r\nconst TimePicker = ({\r\n    item = {},\r\n    value,\r\n    onChange,\r\n    disabled = false,\r\n    placeholder = \"Select time\",\r\n    format = \"hh:mm A\",\r\n    use12Hours = true,\r\n    style = { width: \"100%\" },\r\n    ...rest\r\n}) => {\r\n    const parseValue = () => {\r\n        if (!value) return undefined;\r\n        if (moment.isMoment(value)) return value;\r\n        if (typeof value === 'string') {\r\n            // Parse with multiple formats to handle both API responses and user input\r\n            const parsed = moment(value, [\r\n                'hh:mm A',   // 02:30 PM\r\n                'h:mm A',    // 2:30 PM\r\n                'hh:mm  A',  // 02:30  PM (double space)\r\n                'h:mm  A',   // 2:30  PM (double space)\r\n                'hh:mmA',    // 02:30PM (no space)\r\n                'h:mmA',     // 2:30PM (no space)\r\n                'HH:mm',     // 14:30 (24-hour)\r\n                'H:mm',      // 14:30 (24-hour)\r\n                moment.ISO_8601\r\n            ], true);\r\n            return parsed.isValid() ? parsed : undefined;\r\n        }\r\n        return undefined;\r\n    };\r\n\r\n    console.log({ value, parsedValue: parseValue() });\r\n\r\n    // Form builder preview mode - render with label\r\n    if (item && Object.keys(item).length > 0) {\r\n        const { label, required } = item;\r\n        return (\r\n            <div>\r\n                <HeaderLabel label={label} required={required} />\r\n                <div className=\"form-group\">\r\n                    <AntdTimePicker\r\n                        format={format}\r\n                        use12Hours={use12Hours}\r\n                        placeholder={placeholder}\r\n                        disabled={disabled}\r\n                        allowClear\r\n                        style={style}\r\n                    />\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Standard TimePicker for forms\r\n    return (\r\n        <AntdTimePicker\r\n            format={format}\r\n            use12Hours={use12Hours}\r\n            placeholder={placeholder}\r\n            value={parseValue()}\r\n            onChange={(momentValue, timeString) => onChange(!momentValue ? null : timeString)}\r\n            disabled={disabled}\r\n            allowClear\r\n            style={style}\r\n        />\r\n    );\r\n};\r\n\r\nexport default TimePicker;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,IAAIC,cAAc,QAAQ,MAAM;AACnD,OAAOC,WAAW,MAAM,eAAe;AAEvC,MAAMF,UAAU,GAAGA,CAAC;EAChBG,IAAI,GAAG,CAAC,CAAC;EACTC,KAAK;EACLC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChBC,WAAW,GAAG,aAAa;EAC3BC,MAAM,GAAG,SAAS;EAClBC,UAAU,GAAG,IAAI;EACjBC,KAAK,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC;EACzB,GAAGC;AACP,CAAC,KAAK;EACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACT,KAAK,EAAE,OAAOU,SAAS;IAC5B,IAAIf,MAAM,CAACgB,QAAQ,CAACX,KAAK,CAAC,EAAE,OAAOA,KAAK;IACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B;MACA,MAAMY,MAAM,GAAGjB,MAAM,CAACK,KAAK,EAAE,CACzB,SAAS;MAAI;MACb,QAAQ;MAAK;MACb,UAAU;MAAG;MACb,SAAS;MAAI;MACb,QAAQ;MAAK;MACb,OAAO;MAAM;MACb,OAAO;MAAM;MACb,MAAM;MAAO;MACbL,MAAM,CAACkB,QAAQ,CAClB,EAAE,IAAI,CAAC;MACR,OAAOD,MAAM,CAACE,OAAO,CAAC,CAAC,GAAGF,MAAM,GAAGF,SAAS;IAChD;IACA,OAAOA,SAAS;EACpB,CAAC;EAEDK,OAAO,CAACC,GAAG,CAAC;IAAEhB,KAAK;IAAEiB,WAAW,EAAER,UAAU,CAAC;EAAE,CAAC,CAAC;;EAEjD;EACA,IAAIV,IAAI,IAAImB,MAAM,CAACC,IAAI,CAACpB,IAAI,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;IACtC,MAAM;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAGvB,IAAI;IAChC,oBACIL,KAAA,CAAA6B,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACInC,KAAA,CAAA6B,aAAA,CAACzB,WAAW;MAACuB,KAAK,EAAEA,KAAM;MAACC,QAAQ,EAAEA,QAAS;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eACjDnC,KAAA,CAAA6B,aAAA;MAAKO,SAAS,EAAC,YAAY;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACvBnC,KAAA,CAAA6B,aAAA,CAAC1B,cAAc;MACXO,MAAM,EAAEA,MAAO;MACfC,UAAU,EAAEA,UAAW;MACvBF,WAAW,EAAEA,WAAY;MACzBD,QAAQ,EAAEA,QAAS;MACnB6B,UAAU;MACVzB,KAAK,EAAEA,KAAM;MAAAkB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAChB,CACA,CACJ,CAAC;EAEd;;EAEA;EACA,oBACInC,KAAA,CAAA6B,aAAA,CAAC1B,cAAc;IACXO,MAAM,EAAEA,MAAO;IACfC,UAAU,EAAEA,UAAW;IACvBF,WAAW,EAAEA,WAAY;IACzBH,KAAK,EAAES,UAAU,CAAC,CAAE;IACpBR,QAAQ,EAAEA,CAAC+B,WAAW,EAAEC,UAAU,KAAKhC,QAAQ,CAAC,CAAC+B,WAAW,GAAG,IAAI,GAAGC,UAAU,CAAE;IAClF/B,QAAQ,EAAEA,QAAS;IACnB6B,UAAU;IACVzB,KAAK,EAAEA,KAAM;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChB,CAAC;AAEV,CAAC;AAED,eAAejC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}