{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\wify-utils\\\\form-builder\\\\components\\\\FormBuilder\\\\FormInputs\\\\switchItems.js\";\n/* eslint-disable */\nimport React from 'react';\nimport { Tags, Label, Range, Email, Header, Rating, TextArea, Dropdown, TextInput, Paragraph, Hyperlink, LineBreak, Checkboxes, NumberInput, RadioButtons, Mobile, Date, TimePicker, Files, WIFY_MIC, WIFY_CAMERA, WIFY_BARCODE_SCANNER, WIFY_RICH_TEXT_INPUT, WIFY_BLE_COMPONENT, Button } from './index';\nexport default item => {\n  switch (item.element) {\n    case 'Checkboxes':\n      return /*#__PURE__*/React.createElement(Checkboxes, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 20\n        }\n      });\n    case 'Dropdown':\n      return /*#__PURE__*/React.createElement(Dropdown, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 20\n        }\n      });\n    case 'Header':\n      return /*#__PURE__*/React.createElement(Header, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 20\n        }\n      });\n    case 'HyperLink':\n      return /*#__PURE__*/React.createElement(Hyperlink, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 20\n        }\n      });\n    case 'Label':\n      return /*#__PURE__*/React.createElement(Label, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 20\n        }\n      });\n    case 'Date':\n      return /*#__PURE__*/React.createElement(Date, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 20\n        }\n      });\n    case 'TimePicker':\n      return /*#__PURE__*/React.createElement(TimePicker, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 20\n        }\n      });\n    case 'LineBreak':\n      return /*#__PURE__*/React.createElement(LineBreak, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 20\n        }\n      });\n    case 'NumberInput':\n      return /*#__PURE__*/React.createElement(NumberInput, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 20\n        }\n      });\n    case 'Paragraph':\n      return /*#__PURE__*/React.createElement(Paragraph, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 20\n        }\n      });\n    case 'RadioButtons':\n      return /*#__PURE__*/React.createElement(RadioButtons, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 20\n        }\n      });\n    case 'Range':\n      return /*#__PURE__*/React.createElement(Range, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 20\n        }\n      });\n    case 'Rating':\n      return /*#__PURE__*/React.createElement(Rating, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 20\n        }\n      });\n    case 'Tags':\n      return /*#__PURE__*/React.createElement(Tags, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 20\n        }\n      });\n    case 'TextArea':\n      return /*#__PURE__*/React.createElement(TextArea, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 20\n        }\n      });\n    case 'TextInput':\n      return /*#__PURE__*/React.createElement(TextInput, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 20\n        }\n      });\n    case 'Email':\n      return /*#__PURE__*/React.createElement(Email, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 20\n        }\n      });\n    case 'Mobile':\n      return /*#__PURE__*/React.createElement(Mobile, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 20\n        }\n      });\n    case 'Files':\n      return /*#__PURE__*/React.createElement(Files, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 20\n        }\n      });\n    case 'WIFY_MIC':\n      return /*#__PURE__*/React.createElement(WIFY_MIC, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 20\n        }\n      });\n    case 'WIFY_BLE_COMPONENT':\n      return /*#__PURE__*/React.createElement(WIFY_BLE_COMPONENT, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 20\n        }\n      });\n    case 'WIFY_CAMERA':\n      return /*#__PURE__*/React.createElement(WIFY_CAMERA, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 20\n        }\n      });\n    case 'WIFY_BARCODE_SCANNER':\n      return /*#__PURE__*/React.createElement(WIFY_BARCODE_SCANNER, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 20\n        }\n      });\n    case 'WIFY_RICH_TEXT_INPUT':\n      return /*#__PURE__*/React.createElement(WIFY_RICH_TEXT_INPUT, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 20\n        }\n      });\n    case 'Button':\n      return /*#__PURE__*/React.createElement(Button, {\n        item: item,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 20\n        }\n      });\n  }\n};", "map": {"version": 3, "names": ["React", "Tags", "Label", "Range", "Email", "Header", "Rating", "TextArea", "Dropdown", "TextInput", "Paragraph", "Hyperlink", "LineBreak", "Checkboxes", "NumberInput", "RadioButtons", "Mobile", "Date", "TimePicker", "Files", "WIFY_MIC", "WIFY_CAMERA", "WIFY_BARCODE_SCANNER", "WIFY_RICH_TEXT_INPUT", "WIFY_BLE_COMPONENT", "<PERSON><PERSON>", "item", "element", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/form-builder/components/FormBuilder/FormInputs/switchItems.js"], "sourcesContent": ["/* eslint-disable */\r\nimport React from 'react';\r\nimport {\r\n    Tags,\r\n    Label,\r\n    Range,\r\n    Email,\r\n    Header,\r\n    Rating,\r\n    TextArea,\r\n    Dropdown,\r\n    TextInput,\r\n    Paragraph,\r\n    Hyperlink,\r\n    LineBreak,\r\n    Checkboxes,\r\n    NumberInput,\r\n    RadioButtons,\r\n    Mobile,\r\n    Date,\r\n    TimePicker,\r\n    Files,\r\n    WIFY_MIC,\r\n    WIFY_CAMERA,\r\n    WIFY_BARCODE_SCANNER,\r\n    WIFY_RICH_TEXT_INPUT,\r\n    WIFY_BLE_COMPONENT,\r\n    Button,\r\n} from './index';\r\n\r\nexport default (item) => {\r\n    switch (item.element) {\r\n        case 'Checkboxes':\r\n            return <Checkboxes item={item} />;\r\n        case 'Dropdown':\r\n            return <Dropdown item={item} />;\r\n        case 'Header':\r\n            return <Header item={item} />;\r\n        case 'HyperLink':\r\n            return <Hyperlink item={item} />;\r\n        case 'Label':\r\n            return <Label item={item} />;\r\n        case 'Date':\r\n            return <Date item={item} />;\r\n        case 'TimePicker':\r\n            return <TimePicker item={item} />;\r\n        case 'LineBreak':\r\n            return <LineBreak item={item} />;\r\n        case 'NumberInput':\r\n            return <NumberInput item={item} />;\r\n        case 'Paragraph':\r\n            return <Paragraph item={item} />;\r\n        case 'RadioButtons':\r\n            return <RadioButtons item={item} />;\r\n        case 'Range':\r\n            return <Range item={item} />;\r\n        case 'Rating':\r\n            return <Rating item={item} />;\r\n        case 'Tags':\r\n            return <Tags item={item} />;\r\n        case 'TextArea':\r\n            return <TextArea item={item} />;\r\n        case 'TextInput':\r\n            return <TextInput item={item} />;\r\n        case 'Email':\r\n            return <Email item={item} />;\r\n        case 'Mobile':\r\n            return <Mobile item={item} />;\r\n        case 'Files':\r\n            return <Files item={item} />;\r\n        case 'WIFY_MIC':\r\n            return <WIFY_MIC item={item} />;\r\n        case 'WIFY_BLE_COMPONENT':\r\n            return <WIFY_BLE_COMPONENT item={item} />;\r\n        case 'WIFY_CAMERA':\r\n            return <WIFY_CAMERA item={item} />;\r\n        case 'WIFY_BARCODE_SCANNER':\r\n            return <WIFY_BARCODE_SCANNER item={item} />;\r\n        case 'WIFY_RICH_TEXT_INPUT':\r\n            return <WIFY_RICH_TEXT_INPUT item={item} />;\r\n        case 'Button':\r\n            return <Button item={item} />;\r\n    }\r\n};\r\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,oBAAoB,EACpBC,oBAAoB,EACpBC,kBAAkB,EAClBC,MAAM,QACH,SAAS;AAEhB,eAAgBC,IAAI,IAAK;EACrB,QAAQA,IAAI,CAACC,OAAO;IAChB,KAAK,YAAY;MACb,oBAAO3B,KAAA,CAAA4B,aAAA,CAACf,UAAU;QAACa,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACrC,KAAK,UAAU;MACX,oBAAOlC,KAAA,CAAA4B,aAAA,CAACpB,QAAQ;QAACkB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACnC,KAAK,QAAQ;MACT,oBAAOlC,KAAA,CAAA4B,aAAA,CAACvB,MAAM;QAACqB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACjC,KAAK,WAAW;MACZ,oBAAOlC,KAAA,CAAA4B,aAAA,CAACjB,SAAS;QAACe,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACpC,KAAK,OAAO;MACR,oBAAOlC,KAAA,CAAA4B,aAAA,CAAC1B,KAAK;QAACwB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAChC,KAAK,MAAM;MACP,oBAAOlC,KAAA,CAAA4B,aAAA,CAACX,IAAI;QAACS,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC/B,KAAK,YAAY;MACb,oBAAOlC,KAAA,CAAA4B,aAAA,CAACV,UAAU;QAACQ,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACrC,KAAK,WAAW;MACZ,oBAAOlC,KAAA,CAAA4B,aAAA,CAAChB,SAAS;QAACc,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACpC,KAAK,aAAa;MACd,oBAAOlC,KAAA,CAAA4B,aAAA,CAACd,WAAW;QAACY,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACtC,KAAK,WAAW;MACZ,oBAAOlC,KAAA,CAAA4B,aAAA,CAAClB,SAAS;QAACgB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACpC,KAAK,cAAc;MACf,oBAAOlC,KAAA,CAAA4B,aAAA,CAACb,YAAY;QAACW,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACvC,KAAK,OAAO;MACR,oBAAOlC,KAAA,CAAA4B,aAAA,CAACzB,KAAK;QAACuB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAChC,KAAK,QAAQ;MACT,oBAAOlC,KAAA,CAAA4B,aAAA,CAACtB,MAAM;QAACoB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACjC,KAAK,MAAM;MACP,oBAAOlC,KAAA,CAAA4B,aAAA,CAAC3B,IAAI;QAACyB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC/B,KAAK,UAAU;MACX,oBAAOlC,KAAA,CAAA4B,aAAA,CAACrB,QAAQ;QAACmB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACnC,KAAK,WAAW;MACZ,oBAAOlC,KAAA,CAAA4B,aAAA,CAACnB,SAAS;QAACiB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACpC,KAAK,OAAO;MACR,oBAAOlC,KAAA,CAAA4B,aAAA,CAACxB,KAAK;QAACsB,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAChC,KAAK,QAAQ;MACT,oBAAOlC,KAAA,CAAA4B,aAAA,CAACZ,MAAM;QAACU,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACjC,KAAK,OAAO;MACR,oBAAOlC,KAAA,CAAA4B,aAAA,CAACT,KAAK;QAACO,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAChC,KAAK,UAAU;MACX,oBAAOlC,KAAA,CAAA4B,aAAA,CAACR,QAAQ;QAACM,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACnC,KAAK,oBAAoB;MACrB,oBAAOlC,KAAA,CAAA4B,aAAA,CAACJ,kBAAkB;QAACE,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC7C,KAAK,aAAa;MACd,oBAAOlC,KAAA,CAAA4B,aAAA,CAACP,WAAW;QAACK,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IACtC,KAAK,sBAAsB;MACvB,oBAAOlC,KAAA,CAAA4B,aAAA,CAACN,oBAAoB;QAACI,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC/C,KAAK,sBAAsB;MACvB,oBAAOlC,KAAA,CAAA4B,aAAA,CAACL,oBAAoB;QAACG,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC/C,KAAK,QAAQ;MACT,oBAAOlC,KAAA,CAAA4B,aAAA,CAACH,MAAM;QAACC,IAAI,EAAEA,IAAK;QAAAG,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;EACrC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}