const { google } = require("googleapis");

const SERVICE_ACCOUNT_FILE = "./creds.json";
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets"];
const SPREADSHEET_ID = process.env.SPREADSHEET_ID || "your_spreadsheet_id";

const CHUNK_SIZE = 10000;
const DELAY_MS = 1000;

const revenueMastercolMapping = {
  Brand: "brand",
  Product_Type: "Item_SKU",
};

const technicianMastercolMapping = {
  Brand: "brand",
  Product_Type: "Item_SKU",
  Cost: "Technician Cost",
};

async function initializeSheetsAPI(serviceAccountFile, scopes) {
  try {
    const auth = new google.auth.GoogleAuth({
      keyFile: serviceAccountFile,
      scopes: scopes,
      autoRetry: true,
      maxRetries: 5,
      retryDelayMultiplier: 2,
      totalTimeout: 60000,
    });

    const sheets = google.sheets({
      version: "v4",
      auth,
      axios: {
        timeout: 60000,
      },
    });

    return sheets;
  } catch (error) {
    console.error(
      "tms-commercials-B2C-pnl-tab-data-aggregator :: Error initializing Google Sheets API:",
      error
    );
    throw error;
  }
}

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function readSheetAsJson(
  sheets,
  sheetName,
  columnLetters = ["A", "C", "E", "I"]
) {
  let allData = [];
  let headers = [];
  let startRow = 2;

  try {
    // Step 1: Read headers
    const headerRanges = columnLetters.map((col) => `${sheetName}!${col}1`);
    const headerResponse = await sheets.spreadsheets.values.batchGet({
      spreadsheetId: SPREADSHEET_ID,
      ranges: headerRanges,
    });

    headers = headerResponse.data.valueRanges.map(
      (r) => r.values?.[0]?.[0] || ""
    );

    let chunkIndex = 0;

    while (true) {
      const endRow = startRow + CHUNK_SIZE - 1;
      const rowRanges = columnLetters.map(
        (col) => `${sheetName}!${col}${startRow}:${col}${endRow}`
      );

      const response = await sheets.spreadsheets.values.batchGet({
        spreadsheetId: SPREADSHEET_ID,
        ranges: rowRanges,
      });

      const columnDataArrays = response.data.valueRanges.map(
        (r) => r.values || []
      );

      const maxRows = Math.max(...columnDataArrays.map((col) => col.length));
      for (let i = 0; i < columnDataArrays.length; i++) {
        while (columnDataArrays[i].length < maxRows) {
          columnDataArrays[i].push([""]); // empty row
        }
      }

      // Merge into row-wise objects
      const chunkData = Array.from({ length: maxRows }, (_, rowIndex) => {
        return Object.fromEntries(
          headers.map((header, i) => [
            header,
            columnDataArrays[i][rowIndex][0] || "",
          ])
        );
      });

      allData = allData.concat(chunkData);

      if (maxRows < CHUNK_SIZE) break;

      chunkIndex++;
      startRow += CHUNK_SIZE;
      await delay(DELAY_MS);
    }

    return allData;
  } catch (error) {
    console.error(
      "readNonContiguousColumnsInChunks :: Error reading sheet:",
      error
    );
    throw error;
  }
}

function findItemByBrandAndSKU(
  dataArray,
  orgNickName,
  skuKey,
  brandNameKey,
  itemSkuKey,
  columnMapping
) {
  return dataArray
    .filter(
      (item) =>
        item[brandNameKey] === orgNickName && skuKey.includes(item[itemSkuKey])
    )
    .map((item) => {
      const mapped = {};
      for (const [sheetKey, responseKey] of Object.entries(columnMapping)) {
        if (item.hasOwnProperty(sheetKey)) {
          mapped[responseKey] = item[sheetKey];
        }
      }
      if (item["Price to brand"]) {
        mapped["Actual Price"] = item["Price to brand"];
        mapped["Expected Price"] = item["Price to brand"];
      }

      return mapped;
    });
}

function getAdditionalCost({ requestData, customFields }) {
  let additionalCost = 0;

  let customFieldsData = customFields.filter(
    (field) => field.label && field.label.trim() === "Additional Amount :"
  );

  customFieldsData.forEach((field) => {
    additionalCost = requestData[field.key];
  });

  return additionalCost;
}

exports.handler = async (event) => {
  const { org_nick_name, sku_key, requestData, configData } = event;
  let responseStatus = false;
  const sheets = await initializeSheetsAPI(SERVICE_ACCOUNT_FILE, SCOPES);
  const allData = await readSheetAsJson(sheets, "Category wise Cost");
  const customFields = JSON.parse(
    configData.sp_cust_fields_json
  ).translatedFields;

  const respData = {
    revenue_master_data: findItemByBrandAndSKU(
      allData,
      org_nick_name,
      sku_key,
      "Brand",
      "Product_Type",
      revenueMastercolMapping
    ),
    technician_master_data: findItemByBrandAndSKU(
      allData,
      org_nick_name,
      sku_key,
      "Brand",
      "Product_Type",
      technicianMastercolMapping
    ),
    spAdditionalAmount: getAdditionalCost({ requestData, customFields }),
  };

  responseStatus = true;

  return {
    status: responseStatus,
    statusCode: 200,
    body: JSON.stringify(respData),
  };
};
